<?php

use App\Enums\PaymentMethodEnum;
use App\Enums\TelegramUserIdEnum;
use App\Jobs\SendHttpRequestToDiscordJob;
use App\Jobs\SendHttpRequestToTelegramJob;
use App\Models\ApiLog;
use App\Models\Order;
use App\Models\PgWebhookLogs;
use App\Models\SystemConfig;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\OrderService\Models\RegionOrders;

if (!function_exists('activityLog')) {
    function activityLog($log, $extraData = null, $user = null)
    {
        try {
            if (!$user) {
                $user = currentUser()->getInfo();
            }

            $message = ':memo: ';

            if ($user) {
                $message .= ($user->name ?? 'Unknown') . ' (ID: ' . ($user->id ?? 'none') . ' - Email: ' . ($user->email ?? 'none') . '): ' . $log;
            } else {
                $message .= 'Unknown user: ' . $log;
            }

            if ($extraData) {
                if (is_array($extraData)) {
                    $txt = '';

                    foreach ($extraData as $key => $value) {
                        $txt .= $key . ': ' . (is_array($value) ? json_encode($value) : $value) . PHP_EOL;
                    }

                    $message .= "\n```\n" . $txt . '```';
                } else {
                    $message .= "\n```\n" . $extraData . '```';
                }
            }

            logToDiscord($message, 'admin_activity');
        } catch (\Exception $e) {
            logException($e, 'activityLog');
        }
    }
}

if (!function_exists('anyToString')) {
    function anyToString($data): string
    {
        if (is_string($data)) {
            return $data;
        }

        if (is_array($data)) {
            $data = json_encode($data);
        }

        if (is_object($data)) {
            try {
                $data = get_class($data);
            } catch (\Exception $e) {
                $data = serialize($data);
            }
        }

        if (!is_string($data)) {
            $data = (string)$data;
        }

        return $data;
    }
}

if (!function_exists('logToDiscord')) {
    function logToDiscord($message, string $channel = 'error', $debug = false, $async = true, $limitChannel = 10, $embeds = [], $threadId = null): void
    {
        $url = config('senprints.discord_webhook_url.' . $channel);

        if (!$url) {
            logToDiscord('No Discord webhook URL configured for channel: ' . $channel);
            return;
        }
        $current_message = $message;
        if (!is_string($message)) {
            try {
                $current_message = json_encode($message, JSON_THROW_ON_ERROR);
            } catch (\Throwable $e) {
                $current_message = serialize($message);
            }
        }

        $logDelay = 300; // 5 min
        if ($channel === 'error') {
            $debug = true;
            $logDelay = 900; // 15 min
        }

        $env = app()->environment();

        $message = '';
        // add env to message for better debugging
        if ($env !== 'production') {
            $message = '[' . $env . '] ' . PHP_EOL;
            $logDelay = 60; // 1 min for dev
        }

        if ($debug) {
            try {
                $message .= '**Environment:** ' . $env . PHP_EOL;
                $message .= '**App Name:** ' . config('app.name') . PHP_EOL;
                $hostname = gethostname();

                if ($hostname) {
                    $message .= '**Host:** ' . $hostname . PHP_EOL;
                }

                if (!empty(config('senprints.server_info'))) {
                    $message .= '**Server Info:** ' . config('senprints.server_info') . PHP_EOL;
                }
            } catch (\Exception $e) {
            }
        }
        $message .= PHP_EOL . $current_message;

        if ($channel === 'social-feed_log-debug') {
            $logDelay = 1;
        }

        if (!str_starts_with($channel, 'topup')) {
            $logKey = md5($channel . $message . (!empty($embeds) ? json_encode($embeds) : ''));
            if (cache()->has($logKey)) {
                return;
            }
            cache()->put($logKey, $channel, $logDelay);
        }

        if ($debug) {
            try {
                $data = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, $limitChannel);

                if (count($data) > 0) {
                    $message .= PHP_EOL . ':detective: **Stacktrace:**' . PHP_EOL;

                    foreach ($data as $key => $line) {
                        $file = data_get($line, 'file', 'unknown');

                        if (Str::contains($file, '/app/')) {
                            $file = '/app/' . Str::after($file, '/app/');
                        }

                        $newLine = $file . ':' . data_get($line, 'line', 'unknown');

                        // append if it has function (or method)
                        if (isset($line['function'])) {
                            $newLine .= ' - Function: ' . anyToString($line['function']) . '()';
                        }

                        // append if it has class
                        if (isset($line['class'])) {
                            $newLine .= ' - Class: ' . anyToString($line['class']);
                        }

                        // append if it has object
                        if (isset($line['object']) && !isset($line['class'])) {
                            $newLine .= ' - Object: ' . anyToString($line['object']);
                        }

                        $message .= '> #' . $key . '. ' . $newLine . PHP_EOL;
                    }
                }
            } catch (\Exception $e) {
            }
        }

        $longMessage = null;
        if (strlen($message) > 1000) {
            $length = ($debug) ? 1000 : 1997; // for ... at the end
            if (strlen($message) > $length) {
                $longMessage = $message;
            }
            $message = Str::limit($message, $length);
        }
        if (!empty($threadId) && !str_contains($url, '?thread_id=')) {
            $url .= '?thread_id=' . $threadId;
        }
        if ($async) {
            SendHttpRequestToDiscordJob::dispatch($url, $message, $embeds, $longMessage);
        } else {
            $payload = [
                'content' => $message
            ];
            if (!empty($embeds)) {
                $payload['embeds'] = $embeds;
            }
            $http = Http::retry(3, 100)
                ->timeout(15);
            if (!empty($longMessage)) {
                $http->attach('file', $longMessage, 'log_' . date('YmdHis') . '.txt');
            } else {
                $http->asJson();
            }
            $http->post($url, $payload);
        }
    }
}

if (!function_exists('logToDiscordNow')) {
    function logToDiscordNowWithEmbeds($message, string $channel = 'error', $debug = false, $embeds = []): void
    {
        logToDiscord($message, $channel, $debug, false, 7, $embeds);
    }
}

if (!function_exists('logToDiscordNow')) {
    function logToDiscordNow($message, string $channel = 'error', $debug = false): void
    {
        logToDiscord($message, $channel, $debug, false);
    }
}

if (!function_exists('mentionDiscord')) {
    function mentionDiscord($userId): string
    {
        return '<@' . $userId . '>';
    }
}

if (!function_exists('logEmail')) {
    function logEmail($content): void
    {
        Http::post('*********************************************************************************', array(
            'text' => $content
        ));
    }
}

if (!function_exists('logException')) {
    /**
     * @param $ex
     * @param string $name
     * @param string $channel
     * @param bool $debug
     * @return void
     */
    function logException($ex, string $name = __FUNCTION__, string $channel = 'error', bool $debug = false): void
    {
        $date = date('Y-m-d H:i:s');
        logToDiscord(
            "⛔ Exception Logs - Time: {$date} \r\n\r\n" .
            "🔰 Type: " . get_class($ex) . "\r\n" .
            "🔰 Function: " . $name . "\r\n" .
            "🔰 File: {$ex->getFile()}:{$ex->getLine()}" . "\r\n" .
            "🔰 Message: {$ex->getMessage()}" .
            "\r\n",
            $channel,
            $debug
        );
        graylogError($ex->getMessage(), [
            'category' => 'backend_api_exception',
            'user_type' => 'system',
            'function' => $name,
            'trace' => $ex->getTraceAsString(),
        ]);
    }
}

if (!function_exists('logNotice')) {
    function logNotice(string $message, $channel = 'error', $debug = false): void
    {
        $date = date('Y-m-d H:i:s');
        logToDiscord(
            "✅ Notice Logs - Time: {$date} \r\n\r\n" .
            "```" . "\r\n" .
            $message . "\r\n" .
            "```" . "\r\n",
            $channel,
            $debug
        );
    }
}

if (!function_exists('logHttp')) {
    function logHttp(array $data): void
    {
        if(isEnvTesting()){
            return;
        }
        $request = request();
        $apiLog = new ApiLog();
        $apiLog->setConnection(env('DB_REGION_SG_CONNECTION', 'mysql_sg'));
        $apiLog->ip_address = $request->ip();
        $apiLog->user_agent = $request->userAgent();
        $apiLog->url = $request->url();
        $apiLog->fill($data);
        $apiLog->save();
    }
}

if (!function_exists('logToTelegram')) {
    function logToTelegram($chatId, $message, $async = true, $debug = false): void
    {
        if ($debug) {
            $botToken = config('services.telegram_debug.bot_token');
        } else {
            $botToken = config('senprints.telegram_bot_token');
        }

        $endpoint = 'https://api.telegram.org/bot' . $botToken . '/sendMessage';

        if (!is_string($message)) {
            $message = json_encode($message);
        }

        $env = app()->environment();

        // add env to message for better debugging
        if (($env !== 'production')) {
            $message = '[' . $env . '] ' . PHP_EOL . $message;
        }

        if ($async) {
            SendHttpRequestToTelegramJob::dispatch($endpoint, $chatId, $message);
        } else {
            try {
                Http::post($endpoint, [
                    'chat_id' => $chatId,
                    'text' => $message
                ]);
            } catch (\Throwable $e) {
                logToDiscord('Cannot send Telegram message to chat ID: ' . $chatId . ' - Error: ' . $e->getMessage());
            }
        }
    }
}

if (!function_exists('telegramStartTyping')) {
    function telegramStartTyping($chatId, $async = true): void
    {
        $endpoint = 'https://api.telegram.org/bot' . config('senprints.telegram_bot_token') . '/sendChatAction';

        if ($async) {
            SendHttpRequestToTelegramJob::dispatch($endpoint, $chatId, null, true);
        } else {
            Http::post($endpoint, [
                'chat_id' => $chatId,
                'action' => 'typing'
            ]);
        }
    }
}

if (!function_exists('logToTelegramNow')) {
    function logToTelegramNow($chatId, $message): void
    {
        logToTelegram($chatId, $message, false);
    }
}

if (!function_exists('telegramDebug')) {
    /**
     * Function for quickly debugging
     *
     * @param $message
     * @return void
     */
    function telegramDebug($message): void
    {
        logToTelegram(TelegramUserIdEnum::JUNO, $message, false, true);
    }
}

if (!function_exists('telegramDebugProduction')) {
    /**
     * Function for quickly debugging
     *
     * @param $message
     * @return void
     */
    function telegramDebugProduction($message): void
    {
        if (app()->isProduction()) {
            logToTelegram(TelegramUserIdEnum::JUNO, $message, false, true);
        }
    }
}

if (!function_exists('debugEmail')) {
    function debugEmail($success, $func)
    {
        $debugInfo = "\n\n" . json_encode(debug_backtrace());

        logToDiscord($func . ' ' . ($success ? 'Successfully' : 'Failed') . $debugInfo, 'email');
    }
}

if (!function_exists('graylog')) {
    function graylogInfo(string $message, array $context = []): void
    {
        Log::channel('gelf')->info($message, $context);
    }
}

if (!function_exists('graylogError')) {
    function graylogError(string $message, array $context = []): void
    {
        Log::channel('gelf')->error($message, $context);
    }
}

if (!function_exists('logSocialFeed')) {
    function logSocialFeed(string $message, array $context, string $level = 'info'): void
    {
        $config = SystemConfig::getConfig('debug_log_feed', false);
        if (!$config && $level === 'debug') {
            return;
        }

        if (isset($context['time'])) {
            $time = round($context['time'], 2);
            $context['time'] = $time;
            if ($time > 20) {
                $level = 'notice';
            }
        }

        Log::channel('gelf')->$level(
            $message,
            [
                'category' => 'social_feed',
            ] + $context
        );

        if (!in_array($level, [
            'info',
            'debug',
        ])) {
            foreach ($context as $key => $text) {
                if (is_array($text)) {
                    $text = json_encode($text);
                }
                $message .= PHP_EOL . $key . ': ' . $text;
            }
            try {
                logToDiscord(
                    $message,
                    'social_feed'
                );
            } catch (\Throwable $e) {

            }
        }
    }
}

if (!function_exists('sendLogOrderFraudToDiscord')) {
    /**
     * @param Order|RegionOrders $order
     * @param $fraudStatus
     * @param null $staffName
     * @param array $messages
     * @return void
     */
    function sendLogOrderFraudToDiscord(Order|RegionOrders $order, $fraudStatus, $staffName = null, array $messages = []): void
    {
        try {
            $order->loadMissing(['payment_gateway', 'seller']);
            $message = 'Order number: ' . $order->order_number . "\n";
            $message .= 'Fraud status: ' . strtoupper($fraudStatus) . "\n";
            $message .= 'Amount: ' . $order->total_amount . " " . $order->currency_code . "\n";
            $message .= 'Payment gateway: ' . ucfirst($order->payment_gateway->name) . ' - ' . $order->payment_gateway->account_id . "\n";
            $message .= 'Seller: ' . $order->seller->email . "\n";
            if ($order->payment_method === PaymentMethodEnum::PAYPAL) {
                $message .= 'Payment link: https://www.paypal.com/unifiedtransactions/details/payment/' . $order->transaction_id . "\n";
            }
            if (str_contains($order->payment_method, PaymentMethodEnum::STRIPE)) {
                $message .= 'Payment link: https://dashboard.stripe.com/payments/' . $order->transaction_id . "\n";
            }
            if ($staffName) {
                $message .= 'Staff: ' . $staffName . ".\n";
            }
            if (!empty($messages)) {
                $message .= '---------------RISK DATA----------------' . "\n";
                $message .= implode("\n", $messages) . "\n";
            }
            logToDiscord($message, 'fraud_orders');
        } catch (\Throwable $e) {
            logException($e, 'sendLogOrderFraudToDiscord');
        }
    }
}

if (!function_exists('storePaymentGatewayWebhookLogs')) {
    /**
     * @param $gateway
     * @param $eventType
     * @param null $orderId
     * @param null $paymentGatewayId
     * @param null $payload
     * @return bool
     */
    function storePaymentGatewayWebhookLogs($gateway, $eventType, $orderId = null, $paymentGatewayId = null, $payload = null): bool
    {
        try {
            return PgWebhookLogs::query()->insert([
                'gateway' => $gateway,
                'event_type' => $eventType,
                'order_id' => $orderId,
                'payment_gateway_id' => $paymentGatewayId,
                'payload' => is_array($payload) ? json_encode($payload, JSON_THROW_ON_ERROR) : $payload
            ]);
        } catch (\Throwable $e) {
            logException($e);
            return false;
        }
    }
}
