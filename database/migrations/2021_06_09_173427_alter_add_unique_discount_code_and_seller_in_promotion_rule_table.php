<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddUniqueDiscountCodeAndSellerInPromotionRuleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('promotion_rule');

        if (!array_key_exists('unique_discount_code_seller', $indexesFound)) {
            Schema::table(
                'promotion_rule',
                function (Blueprint $table) use ($indexesFound){
                    if (array_key_exists('promotion_rule_seller_id_foreign', $indexesFound)) {
                        // remove prevent error when drop index
                        $table->dropForeign(['seller_id']);
                    }

                    $table->unique(
                        [
                            'discount_code',
                            'seller_id',
                        ],
                        'unique_discount_code_seller'
                    );

                    $table->foreign('seller_id')
                        ->references('id')
                        ->on('user')
                        ->onDelete('cascade');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
