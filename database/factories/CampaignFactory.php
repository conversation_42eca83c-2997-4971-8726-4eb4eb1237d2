<?php

namespace Database\Factories;

use App\Enums\CampaignPublicStatusEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Models\Campaign;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CampaignFactory extends Factory
{
    protected $model = Campaign::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->words(10, true),
            'slug' => $this->faker->slug,
            'description' => $this->faker->paragraph,
            'status' => ProductStatus::ACTIVE,
            'product_type' => ProductType::CAMPAIGN,
            'public_status' => CampaignPublicStatusEnum::YES,
        ];
    }

    public function forSeller(User $seller): self
    {
        return $this->state(fn(array $attributes) => [
            'seller_id' => $seller->id,
        ]);
    }
}
