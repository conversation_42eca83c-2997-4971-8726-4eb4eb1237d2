<?php

use App\Enums\SupplierEnum;

/** @noinspection SpellCheckingInspection */
/** @noinspection HttpUrlsUsage */
return [
    'printlogistic'    => [
        'supplier_id' => SupplierEnum::PRINTLOGISTIC,
        'path'        => '\PrintLogistic',
        'validate_billing_importer' => \App\Imports\Supplier\PrintLogistic\ValidateBillingImporter::class,
        'token'       => '',
        'dev'         => [
            'api' => 'https://api.smartfactore.eu/connector/v1',
        ],
        'production'  => [
            'api' => 'https://api.smartfactore.eu/senprint/v1',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'monsterdigital'   => [
        'supplier_id' => SupplierEnum::MONSTERDIGITAL,
        'path'        => '\MonsterDigital',
        'validate_billing_importer' => \App\Imports\Supplier\MonsterDigital\ValidateBillingImporter::class,
        'sync_oss_variant' => \App\Providers\FulfillAPI\MonsterDigital\SyncVariantOOS::class,
        'token'       => '34d447f4054b4390ad323f816d6d5112',
        'secret'      => 'e5aabf3619d9466586db8c9a1df10c7b',
        'skip_corner_placement' => true,
        'dev'         => [
            'api' => 'https://apptest.tscmiami.com/api',
        ],
        'production'  => [
            // 'api' => 'https://mdp.monsterdigitial.biz/api',
            'api' => 'https://app.tscmiami.com/api',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ]
    ],
    'dreamship'        => [
        'supplier_id' => SupplierEnum::DREAMSHIP,
        'path'        => '\Dreamship',
        'dev'         => [
            'token' => '8fc3236980f81738b2e5532df46164b7a96b0ecd',
        ],
        'production'  => [
            'token' => '52e6f094a216f12d6d3caedf8802a091beb0baf7',
        ],
        'api'         => 'https://api.dreamship.com/v1',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => true,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    //moteefee
    'shirtplatform'    => [
        'supplier_id' => SupplierEnum::SHIRTPLATFORM,
        'path'        => '\ShirtPlatform',
        'production'  => [
            'user_id'    => 438,
            'account_id' => 201,
            'shop_id'    => 1099,
            'username'   => 'Senprints',
            'password'   => '9hsBpDhNSp',
            'api'        => 'https://api.shirtplatform.com/webservices/rest',
        ],
        'dev'         => [
            'user_id'    => 378,
            'account_id' => 197,
            'shop_id'    => 1051,
            'username'   => 'SenPrints',
            'password'   => 'v2aY36AV1',
            'api'        => 'https://pilot.shirtplatform.com/webservices/rest',
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'printhigh'        => [
        'supplier_id' => SupplierEnum::PRINTHIGH,
        'path'        => '\PrintHigh',
        'production'  => [
            'token' => 'e9a3d5d6077bdb6c2bd2f8368ac0599d3508d692ed233dfb219c91a562949c12'
        ],
        'dev'         => [
            'token' => 'ab3bce2ae63af6a1a64be18df1dd4b2e4dd42fafda9b6b7ee17ee500b9a0a8de'
        ],
        'api'         => 'https://gateway.printhigh.com/api',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'customcat'        => [
        'supplier_id' => SupplierEnum::CUSTOMCAT,
        'path'        => '\CustomCat',
        'production'  => [
            'token' => 'CEED92C2-01D1-CCB0-EE6EDB8ABDD76CDB'
        ],
        'dev'         => [
            'token' => '3A75ACDB-0D94-6A26-180351EBE590A097'
        ],
        'api'         => 'https://customcat-beta.mylocker.net/api/v1',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'jondo'            => [
        'supplier_id' => SupplierEnum::JONDO,
        'path'        => '\JonDo',
        'validate_billing_importer' => \App\Imports\Supplier\Jondo\ValidateBillingImporter::class,
        'userId'      => 476,
        'apiKey'      => '5KpMONLIqmrxfJ',
        'api'         => 'https://jondohd.com/jondoApi',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => false,
            'cancel_order'        => true,
        ],
    ],
    'invition'         => [
        'supplier_id' => SupplierEnum::INVITION,
        'path'        => '\Invition',
        'production'  => [
            'api'  => 'https://api.prod.invition.nl',
            'code' => 'SENPRI',
            'key'  => 'OPLcJMhhCToLUcCBqHsrQWvivZPhhafzLYPbmwzh',
        ],
        'dev'         => [
            'api'  => 'https://api.test.invition.nl',
            'code' => 'TESTSENPRI',
            'key'  => 'TFpTpZusDhAedRborUrnzdlmRGqmEMepYwGSTMqE',
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'mww'              => [
        'supplier_id' => SupplierEnum::MWW,
        'path'        => '\MWW',
        'api'         => 'https://api.mwwondemand.com/api',
        'production'  => [
            'token' => 'HWtNDBV6QdoPG5XXyr7E3ruQMPmEj4fT',
        ],
        'dev'         => [
            'token' => '',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            // 'crawl_order'   => true, // temp remove, just use webhook
            'crawl_order'         => false,
            'cancel_order'        => true,
        ],
    ],
    'gooten'           => [
        'supplier_id' => SupplierEnum::GOOTEN,
        'path'        => '\Gooten',
        'validate_billing_importer' => \App\Imports\Supplier\Gooten\ValidateBillingImporter::class,
        'api'         => 'https://api.print.io/api/v/5/source/api',
        'token'       => 'fa28332a-f7e7-4d54-a630-1df16a5640e8',
        'key'         => 'qqOM0UvUUGJNy5mm04YQTnoCQLrFCNtRWtj6b1E7eA4=',
        'on_change'   => [
            'print_space'
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'burgerprints'     => [
        'supplier_id' => SupplierEnum::BURGERPRINTS,
        'path'        => '\BurgerPrints',
        'api'         => 'https://seller.burgerprints.com/pspfulfill/api/v1/dropship-api/order',
        'production'  => [
            'token' => '290a7649-1013-45d5-8f66-e912b7c1a730',
        ],
        'dev'         => [
            'token' => '6e03bd33-9666-497a-9562-0bfe5a992f6f',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'gearbubble'       => [
        'supplier_id' => SupplierEnum::GEARBUBBLE,
        'path'        => '\GearBubble',
        'production'  => [
            'api'   => 'https://www.gearbubble.com/api/v1',
            'token' => '20061d2fbcbfcc11091bdf72851ec99c',
        ],
        'dev'         => [
            'api'   => 'http://staging.gearbubble.com/api/v1',
            'token' => '5520ba3914bae65989134744f2104cdd',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'printgeek'        => [
        'supplier_id' => SupplierEnum::PRINTGEEK,
        'path'        => '\PrintGeek',
        'validate_billing_importer' => \App\Imports\Supplier\PrintGeek\ValidateBillingImporter::class,
        'production'  => [
            'api' => 'https://api.printgeek.ca/api',
        ],
        'dev'         => [
            'api' => 'https://api-staging.printgeek.ca/api',
        ],
        'token'       => 'D278B00B09F34326B5DCC43583DB4E02',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
        'shipping_carriers' => [
            'standard' => 'Canada Post',
            'express' => 'Canada Post',
        ],
    ],
    'textildruck'      => [
        'supplier_id' => SupplierEnum::TEXTILDRUCK,
        'path'        => '\Textildruck',
        'validate_billing_importer' => \App\Imports\Supplier\Textildruck\ValidateBillingImporter::class,
        'api'         => 'https://api.tdeu.de/v2024-10',
        'sync_oss_variant' => \App\Providers\FulfillAPI\Textildruck\SyncVariantOOS::class,
        'production'  => [
            'key'    => '22fb4c80-b0fd-XXX12345XXX-11ec-87dd-d2317ee50153',
            'secret' => '31c1b917-b0fd-11ec-87dd-d2317ee50153',
        ],
        'dev'         => [
            'key'    => '22fb4c80-b0fd-XXX12345XXX-11ec-87dd-d2317ee50153',
            'secret' => '2ba687a3-b0fd-11ec-87dd-d2317ee50153',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
        "separate_name" => true
    ],
    'printforia'       => [
        'supplier_id' => SupplierEnum::PRINTFORIA,
        'path'        => '\Printforia',
        'production'  => [
            'key' => 'b6c5efc64209fab2e4165effb824406b',
            'api' => 'https://api.printforia.com/v2',
        ],
        'dev'         => [
            'key' => '711b81760e3b03efe8cacfa44eade4f7',
            'api' => 'https://api-sandbox.printforia.com/v2',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'yoycol'           => [
        'supplier_id' => SupplierEnum::YOYCOL,
        'path'        => '\Yoycol',
        'production'  => [
            'username'   => 'QYG',
            'access_key' => 'QYG@cdFR8b480',
            'secret_key' => '<EMAIL>',
        ],
        'dev'         => [
            'username'   => 'QYG_TEST',
            'access_key' => 'QYG@cdFR8b689',
            'secret_key' => '<EMAIL>',
        ],
        'api'         => 'https://api.yoycol.com',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'printway'         => [
        'supplier_id' => SupplierEnum::PRINTWAY,
        'path'        => '\Printway',
        'production'  => [
            'username' => '<EMAIL>',
            'password' => 'Senprints2020@',
            'token'    => '9f2b1c4a5ce975f8703d0e89aa15f2b9e2296fb2',
        ],
        'dev'         => [
            'username' => '<EMAIL>',
            'password' => 'n7kKw3kupVGCerd@',
            'token'    => 'e3a36787286d5f3a57f3c183602fcb27ea424a5a',
        ],
        'api'         => 'https://api.printway.io/cdn/api',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'gearment'         => [
        // doc https://api.gearment.com/
        'supplier_id' => SupplierEnum::GEARMENT,
        'path'        => '\Gearment',
        'production'  => [
            'api_key'       => 'VJZIJwRMQMpxqoMt',
            'api_signature' => 'nkqkJSB3j4Hyj8s8j3VX5sALMpS45UFS',
        ],
        'dev'         => [
            'api_key'       => 'Z1jBwkVMCXzlvEVQ',
            'api_signature' => 'Cj7NKCPYkR2mrlp6UH6KMYnYjNL1PvKG',
        ],
        'api'         => 'https://api.gearment.com/v2',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'prima'            => [
        'supplier_id' => SupplierEnum::PRIMA,
        'path'        => '\Prima',
        'api_key'     => '',
        'production'  => [
            'api' => 'http://prima.photo-products.com.au:10510/apifwd/?method=push_async',
        ],
        'dev'         => [
            'api' => 'http://prima.photo-products.com.au:10510/apifwd/?method=staging_push_async',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => false,
            'cancel_order'        => false,
        ],
    ],
    'swiftpod'         => [
        'supplier_id' => SupplierEnum::SWIFTPOD,
        'path'        => '\SwiftPOD',
        'api_key'     => 'emVLMDdDZFo3SGxPeTRxb2ZrdGxqZz09',
        'api'         => 'https://api.swiftpod.com/v1',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'swiftpod_tiktok'         => [
        'supplier_id' => SupplierEnum::SWIFTPOD_TIKTOK,
        'path'        => '\SwiftPodTikTok',
        'api_key'     => 'emVLMDdDZFo3SGxPeTRxb2ZrdGxqZz09',
        'api'         => 'https://api.swiftpod.com/v1',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'swiftpod_tiktok_label'         => [
        'supplier_id' => SupplierEnum::SWIFTPOD_TIKTOK_LABEL,
        'path'        => '\SwiftPodTikTokLabel',
        'api_key'     => 'emVLMDdDZFo3SGxPeTRxb2ZrdGxqZz09',
        'api'         => 'https://api.swiftpod.com/v1',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'onos'             => [
        'supplier_id' => SupplierEnum::ONOS,
        'path'        => '\Onos',
        'email'       => '<EMAIL>',
        'password'    => 'senprints@2023',
        'production'  => [
            'api'   => 'https://api-app.onospod.com/api/v1',
            'token' => 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************.BqQIjpdGQD4y9l-WdAx7w3mrm71dPEykFa8IxRnt5Qg',
        ],
        'dev'         => [
            'api'   => 'https://api-dev.onospod.com/api/v1',
            'token' => '',
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'otp'              => [
        'supplier_id' => SupplierEnum::OTP,
        'path'        => '\OTP',
        'sku_importer' => \App\Imports\Supplier\OTP\Import::class,
        'sync_oss_variant' => \App\Providers\FulfillAPI\OTP\SyncVariantOOS::class,
        'production'  => [
            'username' => 'senprints',
            'password' => 'C%#%i@9j8iSy2@s',
            'api'      => 'https://legacy-api.integrations.optondemand.com/v1/senprints',
        ],
        'dev'         => [
            'username' => 'senprints-pilot',
            'password' => '4PLNvgpw@m9jX9v',
            'api'      => 'https://legacy-api.integrations.optondemand.com/v1/senprints-pilot',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
        'shipping_carriers' => [
            'standard' => 'Spring',
            'express' => 'DHL Express',
        ],
    ],
    'luxury_pro'       => [
        'supplier_id' => SupplierEnum::LUXURY_PRO,
        'path'        => '\LuxuryPro',
        'sku_importer' => \App\Imports\Supplier\LuxuryPro\Import::class,
        'api'         => '', //'http://**************:8080/api'
        'production'  => [
            'token' => 'sk-mqy7-kvrvqeWdqKrauuQFdAuXQlHpk6PKnY7vO7TRXrVCRqz',
        ],
        'dev'         => [
            'token' => '',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => false,
            'cancel_order'        => false,
        ],
    ],
    'duplium'          => [
        'supplier_id' => SupplierEnum::DUPLIUM,
        'path'        => '\Duplium',
        'sku_importer' => App\Imports\Supplier\Duplium\Import::class,
        'production'  => [
            'token' => 'SenTDRRz9cayfT4',
        ],
        'dev'         => [
            'token' => 'SenAm5ESkMU9#59',
        ],
        'api'         => 'https://podapi.duplium.com/orderAPI/api',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'qtco'             => [
        'supplier_id' => SupplierEnum::QTCO,
        'path'        => '\QTCO',
        'sku_importer' => App\Imports\Supplier\QTCO\Import::class,
        'validate_billing_importer' => \App\Imports\Supplier\QTCO\ValidateBillingImporter::class,
        'production'  => [
            'api'   => 'https://api.qtco.com.au/api/v2',
            'token' => '21|izUZKFxMr7qn1HWs5aRvNWIxQI883Q9Q7jZHgIqv',
        ],
        'dev'         => [
            'api'   => 'https://sandbox.qtco.com.au/api/v2',
            'token' => '24|0Dnvi9pPgTOc2s9nh1MnNPuGAPORdpCUr150SBx5',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => false,
            'cancel_order'        => false,
        ],
    ],
    'printlogistic_v2' => [
        'supplier_id' => SupplierEnum::PRINTLOGISTIC_V2,
        'path'        => '\PrintLogistic_v2',
        'api'         => 'https://panel.smartfactore.eu:7443/api',
        'sync_oss_variant' => \App\Providers\FulfillAPI\PrintLogistic_v2\SyncVariantOOS::class,
        'dev'         => [
            'token' => 'TEST+0715wEhPk3lNSdGPZM',
        ],
        'production'  => [
            'token' => 'ZEziNak0YNUPaSHmuWLCjAy',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'printlogistic_uk' => [
        'supplier_id' => SupplierEnum::PRINTLOGISTIC_UK,
        'path'        => '\PrintLogisticUk',
        'api'         => 'https://panel.smartfactore.eu:7443/api',
        'sync_oss_variant' => \App\Providers\FulfillAPI\PrintLogistic_v2\SyncVariantOOS::class,
        'dev'         => [
            'token' => 'TEST+0715wEhPk3lNSdGPZM',
        ],
        'production'  => [
            'token' => 'wfmofRT+f/VFsY4etmv0jSheqKo',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'lenful'           => [
        'supplier_id' => SupplierEnum::LENFUL,
        'path'        => '\Lenful',
        'api'         => 'https://s-lencam.lenful.com/api',
        'store_id'    => '64ef1991074769dcaa13585b',
        'auth'        => [
            'user_name' => '<EMAIL>',
            'password'  => '1f9badb953',
        ],
        'endpoints'   => [
            'token'        => '/seller/login',
            'products'     => '/product',
            'variants'     => '/product/%s',
            'order_create' => '/order/%s/create',
            'order_cancel' => '/order/cancel',
            'order_detail' => '/order/%s',
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'printway_v2'         => [
        'supplier_id' => SupplierEnum::PRINTWAY_V2,
        'path'        => '\Printway_v2',
        'production'  => [
            'username' => '<EMAIL>',
            'password' => 'Senprints2020@',
            'token'    => 'U2FsdGVkX1/sgGEj1A+bRUyPxJY7uhkaqwzeszg3jOq9qsDy79WO5s64V/Hbg2+AVP8yHCo32eAyN8MiAfTXmhfB8nhSSzAcdyahuBCmigZEDn1gJ6AMM7Q4MBYV5cZBVga0OUszQ7KP2FOgl4o29hc2FLct2XfxL96gl096oMxTYGbL+ML1eaiAqe39HZOp6gyIKYCJaEoXILyrD8vCivPJJAW8Wyi8HIdbxws2P/GiU0W3l328CFeFiWV7rqPy',
            'api'      => 'https://apis.printway.io/v3',
        ],
        'dev'         => [
            'username' => '<EMAIL>',
            'password' => 'n7kKw3kupVGCerd@',
            'token'    => 'U2FsdGVkX199CjO9H4Kl36Eg3bSS3Dgb1ackOMpl1jqx9mo9yF1ebe0I8FZROtUONPVDbHadL6IQU40oAJz7QIlOEm2qfjXgaSAyRWLDCt+zzohpkoEFpozb0L60SsmVTkCXV952Dv96uSbTRvRLeIVRoiOFUfUhfJNa8ANH/XNSSQFUPeUdST0Dp3y50ZO5QuMKXPW8XTlu6GjkwQqXxPLmXyS1ip9dAbdgiNAzsv+WNj08a97DznkNG/iXyq19',
            'api'      => 'https://test.printway.work/api/v3',
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'gearment_fba'         => [
        'supplier_id' => SupplierEnum::GEARMENT_LABEL,
        'path'        => '\GearmentLabel',
        'production'  => [
            'api_key'       => 'VJZIJwRMQMpxqoMt',
            'api_signature' => 'nkqkJSB3j4Hyj8s8j3VX5sALMpS45UFS',
        ],
        'dev'         => [
            'api_key'       => 'Z1jBwkVMCXzlvEVQ',
            'api_signature' => 'Cj7NKCPYkR2mrlp6UH6KMYnYjNL1PvKG',
        ],
        'api'         => 'https://api.gearment.com/v2',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'wideshop'         => [
        'supplier_id' => SupplierEnum::WIDE_SHOP,
        'path'        => '\WideShop',
        'validate_billing_importer' => \App\Imports\Supplier\WidePrint\ValidateBillingImporter::class,
        'api'         => '',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => false,
            'cancel_order'        => false,
        ],
        'shipping_carriers' => [
            'standard' => 'USPS',
            'express' => 'USPS',
        ],
    ],
    'senprints_vn'         => [
        'supplier_id' => SupplierEnum::SENPRINT_VN,
        'path'        => '\SenprintsVN',
        'api'         => '',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => false,
            'cancel_order'        => false,
        ],
    ],
    'hubfulfill'           => [
        'supplier_id' => SupplierEnum::HUB_FULFILL,
        'path'        => '\Hubfulfill',
        'api'         => 'https://hubfulfill.com/api',
        'auth_url'    => 'https://hubfulfill.com/ws/login',
        'dev'         => [
            'token'   => '5a2933cdf1994a80b04bc0891c03c7de747fcf893f0d23a5b19e291fa5e75499'
        ],
        'production'  => [
            'token'   => '8d3398cd8302730a853d9f67924b0341efbfe4a671d4a0c0a8cc212fdd6cd687'

        ],
        'endpoints'   => [
            'order_create' => '/orders',
            'order_detail' => '/orders/%s',
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'flashship'         => [
        'supplier_id'   => SupplierEnum::FLASH_SHIP,
        'sku_importer'  => App\Imports\Supplier\FlashShip\Import::class,
        'path'          => '\FlashShip',
        'validate_billing_importer' => \App\Imports\Supplier\FlashShip\ValidateBillingImporter::class,
        'dev'           => [
            'api'       => 'https://devpod.flashship.net/seller-api-v2',
        ],
        'production'    => [
            'api'       => 'https://api.flashship.net/seller-api-v2',
        ],
        'username'      => 'SenPrint',
        'password'      => 'SenPrint@23!',
        'proxy'         => true,
        'endpoints'     => [
            'token'        => '/token',
            'order_create' => '/orders/shirt-add',
            'order_detail' => '/orders',
            'order_cancel' => '/orders/seller-reject',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
        'shipping_carriers' => [
            'standard' => 'USPS',
            'express' => 'USPS',
        ],
    ],
    'flashship_expedite' => [
        'supplier_id'   => SupplierEnum::FLASH_SHIP_EXPEDITE,
        'sku_importer'  => \App\Imports\Supplier\FlashShip\ImportExpedite::class,
        'path'          => '\FlashShipExpedite',
        'validate_billing_importer' => \App\Imports\Supplier\FlashShip\ValidateBillingImporter::class,
        'dev'           => [
            'api'       => 'https://devpod.flashship.net/seller-api-v2',
        ],
        'production'    => [
            'api'       => 'https://api.flashship.net/seller-api-v2',
        ],
        'username'      => 'SenPrint',
        'password'      => 'SenPrint@23!',
        'proxy'         => true,
        'endpoints'     => [
            'token'        => '/token',
            'order_create' => '/orders/shirt-add',
            'order_detail' => '/orders',
            'order_cancel' => '/orders/seller-reject',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
        'shipping_carriers' => [
            'standard' => 'USPS',
            'express' => 'USPS',
        ],
    ],
    'flashship_label'         => [
        'supplier_id'   => SupplierEnum::FLASH_SHIP_LABEL,
        'sku_importer'  => \App\Imports\Supplier\FlashShip\ImportLabel::class,
        'path'          => '\FlashShipLabel',
        'validate_billing_importer' => \App\Imports\Supplier\FlashShip\ValidateBillingImporter::class,
        'dev'           => [
            'api'       => 'https://devpod.flashship.net/seller-api-v2',
        ],
        'production'    => [
            'api'       => 'https://api.flashship.net/seller-api-v2',
        ],
        'username'      => 'SenPrint',
        'password'      => 'SenPrint@23!',
        'proxy'         => true,
        'endpoints'     => [
            'token'        => '/token',
            'order_create' => '/orders/shirt-add',
            'order_detail' => '/orders',
            'order_cancel' => '/orders/seller-reject',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
        'shipping_carriers' => [
            'standard' => 'USPS',
            'express' => 'USPS',
        ],
    ],
    'shine_on'         => [
        'supplier_id'   => SupplierEnum::SHINE_ON,
        'path'          => '\ShineOn',
        'api'           => 'https://api.shineon.com/v1',
        'dev'           => [
            'token'       => '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            'webhook_token' => "NDQ2NHQ0ZjRnNTk0dDQ5NDczNzRiNHkyODR4MmY0NjR0MjE0NzQ4NDg0NTQxNDg0ajVqNTQ0dTJ5Mmk1NjQ2NDI0NjRpNWw1OTRpNTQ0ODRxMjU0eDI5NGc1",
        ],
        'production'    => [
            'token'       => '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            'webhook_token' => "OTQ1NHQ0bjUzNDU0czRiNDIzNTQ0M3gydTJsNWo1OTQ5NDE0eDI0M3UyOTRxMjEzdzJlNXYyMDRiNGQ0dTI5NDM0dzJ2MjQ0ajUwM2w1MDN0MjY0MjNoNXMy",
        ],
        'endpoints'     => [
            'order_create' => '/orders',
            'order_detail' => '/orders/%s',
            'order_cancel' => '/orders/%s/cancel',
            'crawl_product'=> '/product_templates',
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
        'unset_options' => [
            'box' => false,
            'gift_wrap' => true
        ]
    ],
    'to_add_it'         => [
        'supplier_id'   => SupplierEnum::TO_ADD_IT,
        'path'          => '\Toaddit',
        'api'           => '',
        'dev'           => [
            'token'       => '',
            'webhook_token' => "",
        ],
        'production'    => [
            'token'       => '',
            'webhook_token' => "",
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => false,
            'cancel_order'        => false,
        ],
    ],
    'bee_ful'         => [
        'supplier_id'   => SupplierEnum::BEE_FUL,
        'path'          => '\Beeful',
        'sync_oss_variant' => \App\Providers\FulfillAPI\Beeful\SyncVariantOOS::class,
        'api'           => 'https://app.beeful.net/api',
        'dev'           => [
            'api'           => 'https://app.beeful.net/api',
            'token'       => 'ACdD0O7G-0mcc-CK6V-HRuW',
            'webhook_token' => "",
        ],
        'production'    => [
//            'api'       => 'https://app.beeful.net/api',
            'api'           => 'https://app.beeful.net/api',
            'token'       => 'ACdD0O7G-0mcc-CK6V-HRuW',
            'webhook_token' => "",
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'senhub'         => [
        'supplier_id' => SupplierEnum::SENHUB,
        'path'        => '\SenHub',
        'validate_billing_importer' => \App\Imports\Supplier\SenHub\ValidateBillingImporter::class,
        'api'         => '',
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => false,
            'cancel_order'        => false,
        ],
    ],
    'gearment_two_days'         => [
        // doc https://api.gearment.com/
        'supplier_id' => SupplierEnum::GEARMENT_TWO_DAYS,
        'path'        => '\GearmentTwoDays',
        'production'  => [
            'api_key'       => 'VJZIJwRMQMpxqoMt',
            'api_signature' => 'nkqkJSB3j4Hyj8s8j3VX5sALMpS45UFS',
        ],
        'dev'         => [
            'api_key'       => 'Z1jBwkVMCXzlvEVQ',
            'api_signature' => 'Cj7NKCPYkR2mrlp6UH6KMYnYjNL1PvKG',
        ],
        'api'         => 'https://api.gearment.com/v2',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'gearment_two_days_label'         => [
        // doc https://api.gearment.com/
        'supplier_id' => SupplierEnum::GEARMENT_TWO_DAYS_LABEL,
        'path'        => '\GearmentTwoDaysLabel',
        'production'  => [
            'api_key'       => 'VJZIJwRMQMpxqoMt',
            'api_signature' => 'nkqkJSB3j4Hyj8s8j3VX5sALMpS45UFS',
        ],
        'dev'         => [
            'api_key'       => 'Z1jBwkVMCXzlvEVQ',
            'api_signature' => 'Cj7NKCPYkR2mrlp6UH6KMYnYjNL1PvKG',
        ],
        'api'         => 'https://api.gearment.com/v2',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'gooten_two_days'           => [
        'supplier_id' => SupplierEnum::GOOTEN_TWO_DAYS,
        'path'        => '\GootenTwoDays',
        'validate_billing_importer' => \App\Imports\Supplier\Gooten\ValidateBillingImporter::class,
        'api'         => 'https://api.print.io/api/v/5/source/api',
        'token'       => 'fa28332a-f7e7-4d54-a630-1df16a5640e8',
        'key'         => 'qqOM0UvUUGJNy5mm04YQTnoCQLrFCNtRWtj6b1E7eA4=',
        'on_change'   => [
            'print_space'
        ],
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'dreamship_two_days'        => [
        'supplier_id' => SupplierEnum::DREAMSHIP_TWO_DAYS,
        'path'        => '\DreamshipTwoDays',
        'dev'         => [
            'token' => '8fc3236980f81738b2e5532df46164b7a96b0ecd',
        ],
        'production'  => [
            'token' => '52e6f094a216f12d6d3caedf8802a091beb0baf7',
        ],
        'api'         => 'https://api.dreamship.com/v1',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => true,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'printik'         => [
        'supplier_id' => SupplierEnum::PRINTIK,
        'path'        => '\Printik',
        'dev'         => [
            'token'     => '4a384a8043f6221134e293e37436b51c12fc43ec900bd54af5af7f68144b8419',
            'api'       => 'https://app-staging.printik.io/api/v1/external-api',
        ],
        'production'  => [
            'token'     => '2605b1572619bdfb2b0d536bf8104c9e6970c28c5eeba17cab0609f45a989e93',
            'api'       => 'https://app.printik.io/api/v1/external-api',
        ],
        'testing_note' => 'printik_test:on',
        'support_type' => [
            'DTF'
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ]
    ],
    'bondre'        => [
        'supplier_id' => SupplierEnum::BONDRE,
        'path'        => '\Bondre',
        'dev'         => [
            'store_number' => 'F61'
        ],
        'production'  => [
            'store_number' => 'F61'
        ],
        'api'         => 'https://www.bondre.cn/api',
        'endpoints'  => [
            'order_create'        => '/open/order/info/create',
            'order_detail'        => '/open/order/info/fetchOrderInfoAll'
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'merchize'        => [
        'supplier_id' => SupplierEnum::MERCHIZE,
        'path'        => '\Merchize',
        'dev'         => [
            'token' => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.PvuqHvi52lwAEWB58faCN3ZB4CGZqLOU9jHDFy4R2-M',
            'api' => 'https://bo-group-2-1.merchize.com/qjrh82v/bo-api/order/external'
        ],
        'production'  => [
            'token' => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.pm-mP14HZrech12bu2xR6daEyH51nDWtB6GcrfUrg3Y',
            'api' => 'https://bo-group-2-1.merchize.com/w8g59on/bo-api/order/external'
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'beeful_v2'        => [
        'supplier_id' => SupplierEnum::BEEFUL_V2,
        'path'        => '\Beeful_v2',
        'dev'         => [
            'token' => '36798543-c67a-470b-9326-9d72c60592cb',
            'api' => 'https://be.grabink.co/api/external'
        ],
        'production'  => [
            'token' => '36798543-c67a-470b-9326-9d72c60592cb',
            'api' => 'https://be.grabink.co/api/external'
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'nltp'        => [
        'supplier_id' => SupplierEnum::NLTP,
        'path'        => '\Nltp',
        'dev'         => [
            'token' => '9cd54bff-e905-4d2d-9fe7-864701869162',
            'api' => 'https://be.grabink.co/api/external'
        ],
        'production'  => [
            'token' => '9cd54bff-e905-4d2d-9fe7-864701869162',
            'api' => 'https://be.grabink.co/api/external'
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'print_arrow'        => [
        'supplier_id' => SupplierEnum::PRINT_ARROW,
        'path'        => '\PrintArrow',
        'production'  => [
            'username' => '<EMAIL>',
            'password' => '1Qaz@123',
            'api'      => 'https://monkeykingprint.com/rest/V1',
        ],
        'dev'         => [
            'username' => '<EMAIL>',
            'password' => 'Admin123',
            'api'      => 'https://staging.monkeykingprint.com/rest/V1',
        ],
        'endpoints'     => [
            'token'        => '/integration/customer/token',
            'order_create' => '/vendors/order/import',
            'order_detail' => '/vendors/order',
            'order_cancel' => '',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'print_arrow_embroidery'        => [
        'supplier_id' => SupplierEnum::PRINT_ARROW_EMBROIDERY,
        'path'        => '\PrintArrowEmbroidery',
        'production'  => [
            'username' => '<EMAIL>',
            'password' => '1Qaz@123',
            'api'      => 'https://printarrows.com/rest/V1',
        ],
        'dev'         => [
            'username' => '<EMAIL>',
            'password' => 'Admin123',
            'api'      => 'https://staging.monkeykingprint.com/rest/V1',
        ],
        'endpoints'     => [
            'token'        => '/integration/customer/token',
            'order_create' => '/vendors/order/import',
            'order_detail' => '/vendors/order',
            'order_cancel' => '',
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => false,
        ],
    ],
    'pressify'        => [
        'supplier_id' => SupplierEnum::PRESSIFY,
        'path'        => '\Pressify',
        'dev'         => [
            'token' => 'ABPn4whl-sZDj-NvDc-sE8D',
            'api' => 'https://pressify.us/api'
        ],
        'production'  => [
            'token' => 'ABPn4whl-sZDj-NvDc-sE8D',
            'api' => 'https://pressify.us/api'
        ],
        'support_type' => [
            'DTF'
        ],
        'have_api'    => [
            'crawl_product'       => false,
            'crawl_variant'       => false,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
    'customcat_label'        => [
        'supplier_id' => SupplierEnum::CUSTOMCAT_LABEL,
        'path'        => '\CustomCatLabel',
        'production'  => [
            'token' => 'CEED92C2-01D1-CCB0-EE6EDB8ABDD76CDB'
        ],
        'dev'         => [
            'token' => '3A75ACDB-0D94-6A26-180351EBE590A097'
        ],
        'api'         => 'https://customcat-beta.mylocker.net/api/v1',
        'have_api'    => [
            'crawl_product'       => true,
            'crawl_variant'       => true,
            'crawl_shipping_rule' => false,
            'crawl_order'         => true,
            'cancel_order'        => true,
        ],
    ],
];
