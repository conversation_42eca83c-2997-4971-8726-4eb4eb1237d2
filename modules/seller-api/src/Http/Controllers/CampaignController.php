<?php

namespace Modules\SellerAPI\Http\Controllers;

use App\Enums\CampaignPublicStatusEnum;
use App\Enums\PersonalizedType;
use App\Enums\ProductStatus;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Campaign;
use App\Models\Product;
use App\Models\Store;
use App\Models\User;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Request as RequestFacade;
use Modules\SellerAPI\Data\FilterIndexCampaignData;
use Modules\SellerAPI\Http\Requests\Campaign\IndexCampaignRequest;

class CampaignController extends Controller
{
    use ApiResponse;
    use ElasticClient;

    public function index(IndexCampaignRequest $request): JsonResponse
    {
        $data = FilterIndexCampaignData::from($request->validated());
        $store = Store::find(Store::SENPRINTS_STORE_ID);
        if (empty($store)) {
            return $this->errorResponse('Store not found', 404);
        }

        $response = $this->getListingCampaigns($data, $store);

        $uri = preg_replace("/page=\d+/", '', RequestFacade::getRequestUri());
        $result = new LengthAwarePaginator(
            $response->data,
            $response->total,
            $data->limit,
            $data->page,
            [
                'path' => $uri,
            ]
        );

        return response()->json($result);
    }

    public function show($id): JsonResponse
    {
        if (empty($id)) {
            return $this->errorResponse('ID required', 400);
        }

        $ids = explode('-', $id);
        if (count($ids) !== 2) {
            return $this->errorResponse('ID invalid', 400);
        }

        $sellerId = $ids[0];
        $campaignId = $ids[1];

        $seller = User::query()
            ->select([
                'id',
            ])
            ->whereNotIn('status', UserStatusEnum::getLimitedStatuses())
            ->findOrFail($sellerId);

        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select([
                'market_location',
            ])
            ->where('seller_id', $sellerId)
            ->where('status', ProductStatus::ACTIVE)
            ->where('public_status', CampaignPublicStatusEnum::YES)
            ->findOrFail($campaignId);
        $marketLocation = $campaign->market_location;

        $products = Product::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'name',
                'sku',
                'description',
                'thumb_url',
                'base_cost',
                'base_costs',
                'price as suggested_price',
                'shipping_cost',
                'old_price',
                'options',
                'print_spaces',
                'extra_print_cost',
                'pricing_mode',
                'full_printed',
                'market_location',
                'attributes',
                'campaign_id',
                'seller_id',
            ])
            ->where('campaign_id', $campaignId)
            ->where('seller_id', $sellerId)
            ->where('personalized', PersonalizedType::NONE)
            ->where('status', ProductStatus::ACTIVE)
            ->with([
                'images' => function ($q) use ($seller) {
                    $q->onSellerConnection($seller);
                    $q->select([
                        'product_id',
                        'file_url',
                        'file_url_2',
                    ]);
                },
                'variants' => function ($q) use ($seller, $marketLocation) {
                    $q->onSellerConnection($seller);
                    $q->select([
                        'product_id',
                        'sku',
                        'price',
                        'out_of_stock',
                        'variant_key',
                        'location_code',
                        'base_cost',
                        'old_price',
                        'adjust_price',
                        'weight',
                        'quantity',
                        'check_quantity',
                    ]);
                    if($marketLocation) {
                        $q->where('location_code', $marketLocation);
                    }
                },
                'shipping_rules' => function ($q) use ($marketLocation) {
                    $q->select([
                        'product_id',
                        'variant_key',
                        'shipping_method',
                        'location_code',
                        'shipping_cost',
                        'extra_cost',
                    ]);
                    if ($marketLocation) {
                        $q->where('location_code', $marketLocation);
                    }
                },
            ])
            ->get();

        return $this->successResponse($products);
    }
}
