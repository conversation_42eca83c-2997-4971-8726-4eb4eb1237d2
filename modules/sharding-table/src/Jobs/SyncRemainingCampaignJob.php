<?php

namespace Modules\ShardingTable\Jobs;

use App\Enums\CacheKeys;
use App\Enums\CampaignRenderModeEnum;
use App\Enums\DiscordChannel;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\File;
use App\Models\IndexCampaign;
use App\Models\IndexProduct;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserInfo;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\DB;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Modules\ShardingTable\Traits\InitSellerConnection;

class SyncRemainingCampaignJob implements ShouldQueue
{
    use Dispatchable, Queueable, InitSellerConnection;

    const LIMIT = 1000;

    public User $seller;
    public string $oldConnection;

    public function __construct(User $seller, string $oldConnection = 'mysql')
    {
        $this->seller = $seller;
        $this->oldConnection = $oldConnection;
        $this->onQueue('sync-remaining-campaign');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $this->initConnection();
            DB::beginTransaction();
            $campaignModel = Campaign::query()->on($this->oldConnection);
            if ($this->oldConnection === 'mysql') {
                $campaignModel = IndexCampaign::query();
            }
            $campaignModel
                ->withTrashed()
                ->select('id')
                ->where('seller_id', $this->seller->id)
                ->where('temp_status', '>', TempStatusEnum::SYNCHRONIZED)
                ->orderBy('id')
                ->chunkById(self::LIMIT, function (Collection $indexCampaigns) {
                    if ($this->oldConnection === 'mysql') {
                        $campaigns = Campaign::query()
                            ->on($this->oldConnection)
                            ->withTrashed()
                            ->whereIn('id', $indexCampaigns->pluck('id'))
                            ->get();
                    }
                    else {
                        $campaigns = $indexCampaigns;
                    }
                    $campaignIds = $campaigns->pluck('id')->toArray();
                    graylogInfo("Start sync remaining campaign for seller: {$this->seller->id}", [
                        'category' => 'sharding_insert_log',
                        'action' => 'sync_remaining_campaign',
                        'seller_id' => $this->seller->id,
                        'ids' => $campaignIds,
                    ]);
                    $campaigns->each(function (Campaign $campaign) {
                        //delete seller campaign;
                        Campaign::query()
                            ->withTrashed()
                            ->onSellerConnection($this->seller)
                            ->where('id', $campaign->id)
                            ->forceDelete();

                        //sync campaign
                        $originalCampaign = $campaign->getOriginal();
                        if (!in_array($originalCampaign['render_mode'], CampaignRenderModeEnum::asArray())) {
                            $originalCampaign['render_mode'] = CampaignRenderModeEnum::NATURE;
                        }
                        Campaign::query()
                            ->onSellerConnection($this->seller)
                            ->insert($originalCampaign);
                        $sellerCampaign = Campaign::query()
                            ->withTrashed()
                            ->onSellerConnection($this->seller)
                            ->find($campaign->id);

                        //sync designs
                        $campaign->load('designs');
                        $designs = $campaign->designs;
                        if ($designs->count()) {
                            $sellerCampaign->designs()->forceDelete();
                            $designChunks = $designs->chunk(500);
                            foreach ($designChunks as $chunk) {
                                File::query()
                                    ->onSellerConnection($this->seller)
                                    ->insert($chunk->toArray());
                            }
                        }

                        //sync products
                        $campaign->load('products');
                        $products = $campaign->products;
                        if ($products->count()) {
                            $sellerCampaign->products()->withTrashed()->forceDelete();
                            $products->map(function ($product) {
                                if (!in_array($product->render_mode, CampaignRenderModeEnum::asArray())) {
                                    $product->render_mode = CampaignRenderModeEnum::NATURE;
                                };
                            });
                            Product::query()
                                ->onSellerConnection($this->seller)
                                ->insert($products->toArray());
                        }

                        //sync product variants and files
                        $campaign->products->load(['variants', 'files']);
                        $campaign->products->each(function (Product $product) {
                            if ($product->variants->count()) {
                                $product->variants->each(function (ProductVariant $variant) {
                                    ProductVariant::query()
                                        ->onSellerConnection($this->seller)
                                        ->where([
                                            'product_id' => $variant->product_id,
                                            'variant_key' => $variant->variant_key,
                                            'location_code' => $variant->location_code,
                                        ])
                                        ->delete();
                                });
                                ProductVariant::query()
                                    ->onSellerConnection($this->seller)
                                    ->insert($product->variants->toArray());
                            }
                            if ($product->files->count()) {
                                File::query()
                                    ->onSellerConnection($this->seller)
                                    ->whereIn('id', $product->files->pluck('id')->toArray())
                                    ->delete();
                                File::query()
                                    ->onSellerConnection($this->seller)
                                    ->insert($product->files->toArray());
                            }
                        });
                    });
                    DB::commit();
                    graylogInfo("Sync remaining campaign for seller: {$this->seller->id} successfully.", [
                        'category' => 'sharding_insert_log',
                        'action' => 'sync_remaining_campaign',
                        'seller_id' => $this->seller->id,
                        'ids' => $campaignIds,
                    ]);
                });

            UserInfo::query()->create([
                'key' => 'old_connection',
                'value' => $this->oldConnection,
                'user_id' => $this->seller->id,
            ]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            logToDiscord("Sync remaining campaign for seller: {$this->seller->id} fail. " . $e->getMessage(), DiscordChannel::SHARDING_DATABASE);
        }
    }
}
