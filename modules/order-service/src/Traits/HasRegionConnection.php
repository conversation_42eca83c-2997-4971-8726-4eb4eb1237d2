<?php
namespace Modules\OrderService\Traits;

use Illuminate\Database\Eloquent\Builder;
use Modules\OrderService\Helpers\RegionDbConnectionManager;

trait HasRegionConnection
{
    /**
     * @param $region
     * @return mixed
     * @return Builder
     */
    public static function onRegion($region): Builder
    {
        return static::on(RegionDbConnectionManager::getConnectionNameFromRegion($region));
    }
}
