<?php

namespace Modules\OrderService\Traits;

use App\Enums\CampaignSortByAllowEnum;
use App\Enums\CurrencyEnum;
use App\Enums\EnvironmentEnum;
use App\Enums\FulfillMappingEnum;
use App\Enums\FulfillmentStatusEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderAssigneeEnum;
use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderSupportStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\PromotionTypeEnum;
use App\Enums\ShippingMethodEnum;
use App\Enums\ShippingRuleType;
use App\Enums\ShippingTime;
use App\Events\OrderPaymentCompleted;
use App\Http\Controllers\Admin\FulfillController;
use App\Jobs\OrderCostStatisticsJob;
use App\Models\FulfillMapping;
use App\Models\Order;
use App\Models\OrderAssignSupplierHistory;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\PaymentGateway;
use App\Models\ProductCollection;
use App\Models\PromotionRule;
use App\Models\ShippingRule;
use App\Models\Supplier;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Services\OrderService;
use App\Services\StoreService;
use Carbon\Carbon;
use DeepCopy\DeepCopy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\OrderService\Jobs\DatabaseSync\SyncOrderJob;
use Modules\OrderService\Models\RegionOrderHistory;
use Modules\OrderService\Models\RegionOrderProducts;
use Modules\OrderService\Models\RegionOrders;
use Throwable;
use function DeepCopy\deep_copy;
use App\Enums\PricingModeEnum;
use App\Models\Campaign;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

trait UpdatingOrderAttributesTrait
{
    /**
     * @param $method
     * @return bool
     */
    public function isShippingMethodValid($method): bool
    {
        foreach ($this->availableShippingMethods() as $option) {
            if ($option['name'] === $method) {
                return true;
            }
        }
        return false;
    }

    /**
     * @return array|null
     */
    public function availableShippingMethods(): ?array
    {
        $shippingMethods = $this->type !== OrderTypeEnum::SERVICE ? config('senprints.shipping_methods') : null;

        if ($shippingMethods) {
            if ($this->shipping_method === ShippingRuleType::STANDARD) {
                $indexAvailableMethod = 0;
                $indexMissingMethod = 1;
                $missingMethod = ShippingRuleType::EXPRESS;
            } else {
                $indexAvailableMethod = 1;
                $indexMissingMethod = 0;
                $missingMethod = ShippingRuleType::STANDARD;
            }
            $shippingMethods[$indexAvailableMethod]["shipping_cost"] = $this->total_shipping_amount;
            $shippingMethods[$indexAvailableMethod]["shipping_time"] = $this->getShippingTime();
            $shippingMethods[$indexAvailableMethod]["printing_time"] = $this->getPrintingTime();

            $cloneOrder = deep_copy($this);
            $cloneOrder->shipping_method = $missingMethod;
            $cloneOrder->calculateShippingCost();
            $shippingMethods[$indexMissingMethod]["shipping_cost"] = $cloneOrder->total_shipping_amount;
            $shippingMethods[$indexMissingMethod]["shipping_time"] = $cloneOrder->getShippingTime();
            $shippingMethods[$indexMissingMethod]["printing_time"] = $cloneOrder->getPrintingTime();

            // no express
            $shippingExpressEnable = SystemConfig::getConfig('shipping_express_enable', true) !== 'false';
            if (
                $this->shipping_method === $cloneOrder->shipping_method
                ||
                !$shippingExpressEnable
            ) {
                unset($shippingMethods[1]);
            } elseif ($shippingMethods[1]['shipping_time'][0] > $shippingMethods[0]['shipping_time'][0]) {
                // if min shipping time of express is greater than standard, use standard
                $shippingMethods[1]['shipping_time'][0] = $shippingMethods[0]['shipping_time'][0];
            }
        }

        return $shippingMethods;
    }

    /**
     * @return array
     */
    public function getShippingTime(): array
    {
        $shippingTime = getShippingTime($this->country, $this->shipping_method);

        if (!empty($shippingTime)) {
            return $shippingTime;
        }

        $shippingTimes = ShippingTime::SHIPPING_TIME;
        $locationInfo = getLocationByCode($this->country);
        $locationCodes = ["*"];
        if (!is_null($locationInfo)) {
            $locationCodes = $locationInfo->getRegionCodes();
        }
        foreach ($locationCodes as $locationCode) {
            if (isset($shippingTimes[$locationCode])) {
                $shippingTime = $shippingTimes[$locationCode];
                break;
            }
        }

        $shippingTime ??= $shippingTimes["*"];
        if ($this->shipping_method === ShippingMethodEnum::EXPRESS) {
            $shippingTime[1] = (int)round($shippingTime[1] * 0.7);
        }

        return $shippingTime;
    }

    /**
     * @return int
     */
    public function getPrintingTime(): int
    {
        $avgProcessingDay = 0;
        $rate = SystemConfig::getConfig('printing_time_rate', 1.5);

        $this->products->each(function ($product) use (&$avgProcessingDay) {
            $avg = getAverageProcessingDay($product->template_id, $product->supplier_id);
            if ($avg > $avgProcessingDay) {
                $avgProcessingDay = $avg;
            }
        });

        if ($avgProcessingDay === 0) {
            $avgProcessingDay = 3;
        } else {
            $avgProcessingDay = (int)round($avgProcessingDay * $rate);
        }

        if ($this->shipping_method === ShippingMethodEnum::EXPRESS) {
            $avgProcessingDay--;
        }

        return $avgProcessingDay > 0 ? $avgProcessingDay : 1;
    }

    /**
     * @return float
     */
    public function getInsuranceFee(): float
    {
        if ($this->total_amount == 0) {
            return 0;
        }
        $totalAmount = $this->total_amount;
        if ($this->currency_code === CurrencyEnum::EUR) {
            $totalAmount /= $this->currency_rate;
        }
        // $insuranceFee = 0.98 + ($totalAmount > 100 ? 0.5 * (int) (($this->total_amount - 100) / 50) : 0);
        $insuranceFee = max((int)(($totalAmount * 2 / 100) / 0.5) * 0.5 - 0.02, Order::ORDER_INSURANCE_FEE); // 2% or min 0.98
        if ($this->currency_code === CurrencyEnum::EUR) {
            return $insuranceFee / $this->currency_rate;
        }
        return round($insuranceFee, 2);
    }

    /**
     * @throws Throwable
     */
    public function assignSupplier($reAssign = true, string $source = Order::NOT_CHECKOUT_ASSIGN_SUPPLIER, bool $isRegion = false): void
    {
        // for new order which don't have country yet
        if (empty($this->country) || $this->fulfill_status === OrderFulfillStatus::FULFILLED || $this->isCustomServiceOrder() || $this->isServiceOrder()) {
            return;
        }
        $orderLocation = SystemLocation::findByCountryCodeThenSetForAssign($this);
        if (is_null($orderLocation)) {
            logToDiscord('Country code invalid. Check Order Id: ' . $this->id);
            return;
        }

        $logs = [];
        $currentSupplierId = null;
        foreach ($this->products as $op) {
            if ($op->skipAssignSupplier()) {
                continue;
            }

            // assign to current supplier if not reassign. If not try to assign to the same supplier of previous item
            if (!$reAssign && !is_null($op->supplier_id)) {
                $currentSupplierId = $op->supplier_id;
            }
            $supplierName = FulfillController::assignSupplierByLocation(
                $op,
                $orderLocation,
                $currentSupplierId,
            );
            if ($source !== Order::CHECKOUT_ASSIGN_SUPPLIER && $op->supplier_id && !empty($op->id) && $op->wasReAssignSupplier()) {
                OrderAssignSupplierHistory::createFromOrderProduct($op);
            }

            // Nếu bất cứ sản phẩm nào trong đơn hàng đã được đánh dấu là cross shipping thì cũng đánh dấu
            // đơn hàng là cross shipping. Việc này để đội vận hành có thể lọc ra để xử lí thủ công
            if ($op->cross_shipping) {
                $this->markCrossShipping(true);
            }

            $logs[] = [$op->id, $op->product_name, $supplierName];
        }

        // Tính toán lại giá vận chuyển và lợi nhuận của sup cho đơn hàng
        if ($this->isPaid()) {
            OrderCostStatisticsJob::dispatchAfterResponse($this->id);
        }

        if (!$isRegion) {
            OrderHistory::insertLog(
                $this,
                OrderHistoryActionEnum::ASSIGNED_SUPPLIER,
                collect($logs)->map(static fn($log) => implode(' - ', $log))->implode("\n"),
            );
        } else {
            RegionOrderHistory::insertLog(
                $this,
                OrderHistoryActionEnum::ASSIGNED_SUPPLIER,
                collect($logs)->map(static fn($log) => implode(' - ', $log))->implode("\n"),
                OrderHistoryDisplayLevelEnum::ADMIN,
                config('app.region')
            );
        }
    }

    /**
     * @param int|null $value
     * @return Order|RegionOrders
     */
    public function markCrossShipping(?int $value = null): Order|RegionOrders
    {
        $this->cross_shipping = $value ?? 0;

        return $this;
    }

    /**
     * @return bool
     */
    public function isCrossShipping(): bool
    {
        return $this->cross_shipping === 1;
    }

    /**
     * @return bool
     */
    public function isPaid(): bool
    {
        return (bool) $this->paid_at;
    }

    /**
     * @param $hours
     * @return bool
     */
    public function isPaidAfter($hours): bool
    {
        return $this->isPaid() && $this->paid_at->diffInHours(now()) >= $hours;
    }

    /**
     * @param $hours
     * @return bool
     */
    public function isPaidUnder($hours): bool
    {
        return $this->getPaidAt() && $this->getPaidAt()->diffInHours(now()) <= $hours;
    }

    /**
     * @return bool
     */
    public function isSeparateName(): bool
    {
        return (bool)$this->products->first(
            fn($p) => in_array($p->supplier_id, Supplier::listIdSeparateName(), true)
        );
    }

    /**
     * @param $defaultTip
     * @return void
     */
    public function calculateOrder($defaultTip = 0): void
    {
        $this->total_product_amount = $this->calculateSubtotal();
        // empty order
        if ($this->total_product_amount == 0) {
            $this->tip_amount = 0;
        } else {
            if (!empty($defaultTip)) {
                $this->tip_amount = $defaultTip * 0.01 * $this->total_product_amount;
            }
            if ($this->tip_amount > $this->total_product_amount * 0.5) {
                $this->tip_amount = round($this->total_product_amount * 0.5, 2);
            }
        }

        $this->total_quantity = $this->countTotalQuantity();

        // assign then calculate
        // $this->assignSupplier();
        if ($this->type === OrderTypeEnum::FBA) {
            $this->total_shipping_amount = $this->total_quantity * self::FBA_COST_PER_UNIT + self::FBA_FEE_PER_ORDER * $this->total_product_amount;
        } else {
            $this->total_shipping_amount = $this->calculateShippingCost();
        }
        $this->total_shipping_amount = round($this->total_shipping_amount, 2);

        $this->total_discount = $this->calculateDiscount();

        // adjust discount amount for order products
        if (!empty($this->promotion_rule_id)) {
            $this->products->map(fn($product) => $product->discount_amount = ($product->total_amount > 0) ? $this->total_discount * $product->total_amount / $this->total_product_amount : 0);
        }

        $this->payment_discount = $this->calculatePaymentDiscount();
        $this->total_tax_amount = $this->calculateTax();
        $this->processing_fee = $this->calculateProcessingFee();
        $this->total_amount = $this->total_product_amount + $this->total_shipping_amount - $this->total_discount - $this->payment_discount + $this->total_tax_amount + $this->tip_amount;
        if ($this->insurance_fee > 0) {
            $this->insurance_fee = $this->getInsuranceFee(); // recalculate insurance fee
            $this->total_amount += $this->insurance_fee;
        }
        if (in_array($this->type, [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])) {
            $this->total_amount += $this->processing_fee;
        }
        $this->total_amount = round($this->total_amount, 2);
    }

    /**
     * @return float
     */
    public function calculateSubtotal()
    {
        $subtotal = 0;
        $totalBaseCost = 0;
        $processedProductIds = [];
        $storeInfo = StoreService::getStoreInfo($this->store_id, ['enable_dynamic_base_cost']);
        // Find all combo campaigns
        $campaignIds = $this->products->pluck('campaign_id')->filter()->unique()->toArray();
        $comboCampaigns = Campaign::query()
            ->whereIn('id', $campaignIds)
            ->where('system_type', ProductSystemTypeEnum::COMBO)
            ->get();

        if ($comboCampaigns->isNotEmpty()) {
            foreach ($comboCampaigns as $campaign) {
                $campaignProducts = $this->products->where('campaign_id', $campaign->id);
                $requiredProductCount = $campaign->products->count();

                $validProducts = $campaignProducts->filter(function ($product) {
                    return $product->fulfill_status !== OrderProductFulfillStatus::OUT_OF_STOCK &&
                        $product->fulfill_status !== OrderProductFulfillStatus::CANCELLED;
                });

                // If we have enough products for the combo
                if ($validProducts->count() >= $requiredProductCount) {
                    $comboSets = floor($validProducts->count() / $requiredProductCount);

                    for ($i = 0; $i < $comboSets; $i++) {
                        $comboProducts = $validProducts->slice($i * $requiredProductCount, $requiredProductCount);
                        $adjustPrice = 0;
                        $normalPrice = 0;

                        foreach ($comboProducts as $product) {
                            $totalBaseCost += $product->calculateTotalBaseCost();
                            $processedProductIds[] = $product->id;

                            $variantKey = getVariantKey($product->options);
                            if (!empty($variantKey)) {
                                $variant = $product->findVariantByKey($variantKey, $this->country);

                                // calculate dynamic base cost
                                $dynamicBaseCost = 0;
                                if ($variant && $product->product->pricing_mode === PricingModeEnum::ADJUST_PRICE && $storeInfo->enable_dynamic_base_cost) {
                                    $market = getLocationByCode($product->product->market_location);
                                    $customer = getLocationByCode($this->country);
                                    $marketRegionCodes = $market ? $market->getRegionCodes() : ['*'];
                                    $customerRegionCodes = $customer ? $customer->getRegionCodes() : ['*'];
                                    $templateInfoOrderAndCampLocation = StoreService::getBaseVariantOnOrderAndCampLocation($product->template->id, $variantKey, $customerRegionCodes, $marketRegionCodes);
                                    OrderService::getDynamicBaseCostIndex($templateInfoOrderAndCampLocation['campaign_location'], $templateInfoOrderAndCampLocation['order_location'], $dynamicBaseCost);
                                }

                                if ($variant) {
                                    $adjustPrice += $product->quantity * (ceil($variant->adjust_price) + $dynamicBaseCost);
                                }
                            }

                            $normalPrice += $product->calculateTotal();
                        }

                        $finalComboPrice = $campaign->combo_price * $comboProducts[0]->quantity + $adjustPrice;

                        //correct price each item in set combo
                        foreach ($comboProducts as $product) {
                            $product->price = ($finalComboPrice / $normalPrice) * $product->price;
                            $product->calculateTotal(); // recalculate total with corrected price
                        }

                        $subtotal += $finalComboPrice;
                    }
                }
            }
        }

        // not combo campaign
        $remainingProducts = $this->products->filter(function($product) use ($processedProductIds) {
            return !in_array($product->id, $processedProductIds);
        });

        foreach ($remainingProducts as $product) {
            if (
                $product->fulfill_status !== OrderProductFulfillStatus::OUT_OF_STOCK &&
                $product->fulfill_status !== OrderProductFulfillStatus::CANCELLED
            ) {
                $subtotal += $product->calculateTotal();
                $totalBaseCost += $product->calculateTotalBaseCost();
            }
        }

        $this->total_product_amount = round($subtotal, 2);
        $this->total_product_base_cost = round($totalBaseCost, 2);

        return round($subtotal, 2);
    }

    public function calculateProcessingFee()
    {
        $processingFee = 0;
        if ($this->type == OrderTypeEnum::REGULAR) {
            $processingFeeRate = getProcessingFee();
            $processingFee = ($this->total_product_amount - $this->total_discount + $this->tip_amount) * $processingFeeRate;
        } else if ($this->type == OrderTypeEnum::FULFILLMENT) {
            $processingFeeRate = getFulfillProcessingFee();
            $processingFee = ($this->total_product_amount + $this->total_shipping_amount) * $processingFeeRate;
        }

        $this->processing_fee = round($processingFee, 2);
        return $this->processing_fee;
    }

    public function calculateShippingCost()
    {
        $this->total_shipping_amount = 0;

        if (empty($this->shipping_method)) {
            $this->shipping_method = ShippingMethodEnum::STANDARD;
        }

        $locationInfo = SystemLocation::findByCountryCodeThenSetForAssign($this);

        $excludeMappings = FulfillMapping::filterByExcludeLocation(
            FulfillMappingEnum::SHIPPING_EXCLUDE_LOCATION,
            $locationInfo,
            $this->shipping_method,
        );

        if (
            $this->shipping_method === ShippingMethodEnum::EXPRESS
            &&
            $excludeMappings->count() > 0
            &&
            // apply for any supplier
            is_null($excludeMappings->first()->supplier_id)
        ) {
            $this->shipping_method = ShippingMethodEnum::STANDARD;
        }

        $products = $this->products->sortByDesc('base_shipping_cost');
        $productIds = [];
        foreach ($products as $product) {
            $productIds[] = $product->template_id ?: $product->product_id;
        }
        $productIds = array_unique($productIds);

        $locationCodes = ["*"];

        if (!is_null($locationInfo)) {
            $locationCodes = $locationInfo->getRegionCodes();
        }

        $query = ShippingRule::query()
            ->whereIn('product_id', $productIds)
            ->whereIn('location_code', $locationCodes);

        if ($this->shipping_method === ShippingMethodEnum::STANDARD) {
            $query->where('shipping_method', ShippingMethodEnum::STANDARD);
        } else {
            $query->orderByRaw("FIELD(shipping_method, '" . ShippingMethodEnum::STANDARD . "', '" . ShippingMethodEnum::EXPRESS . "') desc");
        }

        $rules = $query
            ->orderByDesc('supplier_id') // get not null first
            ->get();

        $arr = [];
        $arr2 = [];
        $standardShippingOnly = false;
        /**
         * @var $product OrderProduct
         */
        foreach ($products as $product) {
            $quantity = $product->quantity;
            $totalProductShippingAmount = 0;

            if (
                $quantity > 0 && $product->fulfill_status !== OrderProductFulfillStatus::OUT_OF_STOCK
                && $product->fulfill_status !== OrderProductFulfillStatus::CANCELLED
            ) {
                $rule = $this->matchLocation($rules, $locationCodes, $product);

                if ($rule === null) {
                    $shippingCost = $product->base_shipping_cost;
                    $totalProductShippingAmount = ($shippingCost * $quantity);
                } else {
                    $supplierId = $product->supplier_id ?? 0;
                    $templateId = $product->template_id;
                    $shippingCost = $rule->shipping_cost;
                    $shippingExtraCost = !empty($rule->extra_cost) ? $rule->extra_cost : $shippingCost;

                    if (empty($arr2[$templateId]) && (empty($arr[$supplierId]) || $supplierId === 0)) {
                        $arr[$supplierId] = $shippingCost + ($shippingExtraCost * ($quantity - 1));
                    } else {
                        $arr[$supplierId] = ($shippingExtraCost * $quantity);
                    }

                    $totalProductShippingAmount = $arr[$supplierId];
                    $arr2[$templateId] = $totalProductShippingAmount;

                    $product->shipping_rule_id = $rule->id;
                    $product->setRelation('shipping_rule', $rule);

                    if (
                        $this->shipping_method === ShippingMethodEnum::EXPRESS
                        &&
                        ($supplierId === 0
                            ||
                            $rule->shipping_method === ShippingMethodEnum::STANDARD
                            ||
                            $excludeMappings->contains('supplier_id', $supplierId)
                        )
                    ) {
                        $standardShippingOnly = true;
                        break;
                    }
                }
            }

            $product->shipping_cost = $totalProductShippingAmount;
            $this->total_shipping_amount += $product->shipping_cost;
        }

        if ($standardShippingOnly) {
            $this->shipping_method = ShippingMethodEnum::STANDARD;
            $this->total_shipping_amount = $this->calculateShippingCost();
        }

        return round($this->total_shipping_amount, 2);
    }

    /**
     * @param Collection $rules
     * @param array $locationCodes
     * @param $product
     * @return ShippingRule|null
     */
    private function matchLocation($rules, $locationCodes, $product): ?object
    {
        foreach ($locationCodes as $locationCode) {
            // check location code
            foreach ($rules as $rule) {
                // match product or template
                if ($rule->product_id === $product->id || $rule->product_id === $product->template_id) {
                    if (
                        $rule->location_code === $locationCode
                        && (
                            // check supplier if exist
                            is_null($rule->supplier_id) || $product->supplier_id === $rule->supplier_id
                        )
                        && (
                            // check variant key if exist
                            is_null($rule->variant_key) || $rule->variant_key === getVariantKey($product->options, $product->full_printed)
                        )
                    ) {
                        return $rule;
                    }
                }
            }
        }

        return null;
    }

    public function calculateTax(): float
    {
        return round($this->total_tax_amount, 2);
    }

    public function isRegularOrder(): bool
    {
        return $this->type === OrderTypeEnum::REGULAR;
    }

    public function isCustomOrder(): bool
    {
        return $this->type === OrderTypeEnum::CUSTOM;
    }

    public function isServiceOrder(): bool
    {
        return $this->type === OrderTypeEnum::SERVICE;
    }

    public function isFulfillmentOrder(): bool
    {
        return $this->type === OrderTypeEnum::FULFILLMENT || $this->type === OrderTypeEnum::FBA;
    }

    public function isCustomServiceOrder(): bool
    {
        return $this->type === OrderTypeEnum::CUSTOM && $this->products->count() === $this->products->filter(static function ($product) {
            return is_null($product->product_id) && is_null($product->campaign_id);
        })->count();
    }

    /**
     * Is product in store, campaign or collection
     * @param $product
     * @param $promotionRule
     * @return bool
     */
    private function matchRule($product, $promotionRule): bool
    {
        if (
            $product->quantity === 0 || $product->fulfill_status === OrderProductFulfillStatus::OUT_OF_STOCK
            || $product->fulfill_status === OrderProductFulfillStatus::CANCELLED
        ) {
            return false;
        }

        if (!empty($promotionRule->campaign_id)) {
            return $product->campaign_id === $promotionRule->campaign_id;
        }

        if (
            $promotionRule->collections_id
            && $product->relationLoaded('collections')
            && !$product->collections->contains('collections_id', $promotionRule->collections_id)
        ) {
            return false;
        }

        if (!empty($promotionRule->template_id)) {
            return $product->template_id === $promotionRule->template_id;
        }

        return true;
    }

    /**
     * @param $promotionRule
     * @return bool
     */
    private function findX($promotionRule): bool
    {
        $totalQuantity = 0;
        $totalAmount = 0;
        $minimumAmount = 0;
        $minimumQuantity = 0;

        $jsonData = json_decode($promotionRule->rules);

        if (isset($jsonData->buy)) {
            $buyRules = $jsonData->buy;
            $minimumAmount = $buyRules->minimum_amount ?? 0;
            $minimumQuantity = $buyRules->minimum_quantity ?? 0;
        }

        foreach ($this->tempProducts as $product) {
            if ($this->matchRule($product, $promotionRule)) {
                //Log::channel('stack')->info('found X: ' . $product->campaign_id);
                while ($product->quantity > 0) {
                    $totalQuantity++;
                    $totalAmount += $product->price;
                    $product->quantity--;

                    if ($product->quantity === 0) {
                        $productKey = $this->tempProducts->search(function ($item) use ($product) {
                            return $item->temp_index === $product->temp_index;
                        });

                        if ($productKey !== false) {
                            $this->tempProducts->pull($productKey);
                        }
                    }

                    if ($totalQuantity >= $minimumQuantity && $totalAmount >= $minimumAmount) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * @param $promotionRule
     * @return bool
     */
    private function findY($promotionRule, $isBXGYForProduct = false): bool
    {
        $jsonData = json_decode($promotionRule->rules);
        $getYRules = $jsonData->get;
        $getQuantity = $getYRules->quantity ?? 0;
        $discountPercentage = $getYRules->discount_percentage ?? 0;
        $discountPercentage /= 100;
        $totalQuantity = 0;
        foreach ($this->tempProducts as $product) {
            if ($this->matchGetYRule($product, $promotionRule)) {
                //Log::channel('stack')->info('found Y: ' . $product->campaign_id);
                while ($product->quantity > 0) {
                    $totalQuantity++;
                    $product->quantity--;

                    if ($product->quantity === 0) {
                        $productKey = $this->tempProducts->search(function ($item) use ($product) {
                            return $item->temp_index === $product->temp_index;
                        });

                        if ($productKey !== false) {
                            $this->tempProducts->pull($productKey);
                        }
                    }

                    $productKey = $this->products->search(fn($item) => $item->temp_index === $product->temp_index);

                    $discountAmount = $product->price * $discountPercentage;
                    $this->products[$productKey]->discount_amount += $discountAmount;


                    if ($totalQuantity >= $getQuantity) {
                        if ($isBXGYForProduct) {
                            return false;
                        }
                        return true;
                    }
                }
            }
        }

        if ($isBXGYForProduct) {
            return false;
        }


        return $totalQuantity > 0;
    }

    /**
     * @param $product
     * @param $promotionRule
     * @return bool
     */
    public function matchGetYRule($product, $promotionRule): bool
    {
        $jsonData = json_decode($promotionRule->rules);
        $getYRule = $jsonData->get;

        if (!empty($getYRule->campaigns_id)) {
            return $product->campaign_id !== $getYRule->campaigns_id;
        }

        if (!empty($getYRule->product_id)) {
            return $product->template_id === $getYRule->product_id;
        }

        if (!empty($getYRule->store_id)) {
            $storeCampaign = $product->product->store_campaign;
            $listStoreIds = $storeCampaign->pluck('store_id')->toArray();
            return in_array($getYRule->store_id, $listStoreIds, true);
        }

        if (
            !empty($getYRule->collections_id)
            && $product->relationLoaded('collections')
            && !$product->collections->contains('collections_id', $getYRule->collections_id)
        ) {
            return false;
        }

        return true;
    }

    public function calculateDiscount()
    {
        try {
            $this->total_discount = 0;
            $totalAmount = 0;
            $totalQuantity = 0;
            $discountPercentage = 0;
            $matchRule = false;

            $products = $this->products;
            $campaignIds = $products->pluck('campaign_id')->toArray();
            $collections = ProductCollection::query()
                ->whereIn('product_id', $campaignIds)
                ->get();
            // set all discount = 0
            $productPromotion = null;
            foreach ($products as $product) {
                $product->discount_amount = 0;
                $product->setRelation('collections', $collections->where('product_id', $product->campaign_id));
                $productPromotion = PromotionRule::query()->firstWhere('id', $product->promotion_rule_id);
                $product->setRelation('promotionRule', $productPromotion);
            }

            if ($this->relationLoaded('promotion')) {
                $promotionRule = $this->promotion;
            } else {
                $promotionRule = PromotionRule::query()->firstWhere('id', $this->promotion_rule_id);
            }
            $isLoadingBDOrderProduct = false;
            if (
                empty($promotionRule) ||
                (!empty($promotionRule) && $promotionRule->type === PromotionTypeEnum::BUNDLE_DISCOUNT)
            ) {
                $isLoadingBDOrderProduct = true;
                $isBundleDiscountOrderProduct = false;

                $totalInfoBundleGroup = [];
                foreach ($products as $product) {
                    if (empty($totalInfoBundleGroup[$product->promotion_rule_id])) {
                        $totalInfoBundleGroup[$product->promotion_rule_id] = [
                            'total_quantity' => 0,
                            'total_amount' => 0
                        ];
                    }
                    $opPromotionRule = $product->promotionRule;
                    if (!isset($opPromotionRule)) {
                        continue;
                    }
                    $opPromotionRuleType = $opPromotionRule->type;
                    if ($opPromotionRuleType !== PromotionTypeEnum::BUNDLE_DISCOUNT) {
                        continue;
                    }
                    $isBundleDiscountOrderProduct = true;
                    // Find and remove X from list
                    if (!$product->findX($opPromotionRule, $totalInfoBundleGroup[$product->promotion_rule_id]['total_quantity'], $totalInfoBundleGroup[$product->promotion_rule_id]['total_amount'])) {
                        continue;
                    }
                    // Find and remove from list
                    if (!$product->findY($opPromotionRule, $totalInfoBundleGroup[$product->promotion_rule_id]['total_quantity'])) {
                        continue;
                    }
                    $this->total_discount += $product->discount_amount;

                    $this->promotion_rule_id = $product->promotion_rule_id;
                    if (empty($promotionRule) && !empty($productPromotion)) {
                        $this->discount_code = $productPromotion->discount_code;
                        $promotionRule = $productPromotion;
                    }
                }
            }
            if (
                !$this->promotion_rule_id ||
                is_null($promotionRule) ||
                (!empty($promotionRule->store_id) && ($promotionRule->store_id !== $this->store_id))
            ) {
                if ($isLoadingBDOrderProduct && !$isBundleDiscountOrderProduct) {
                    return 0;
                }
                return $this->total_discount;
            }

            $rules = json_decode($promotionRule->rules, true, 512, JSON_THROW_ON_ERROR);
            $type = strtoupper($promotionRule->type);
            // Buy X Get Y || Bundle Discount
            // Check Condition
            if ($promotionRule->type === PromotionTypeEnum::BUY_X_GET_Y) {
                $this->total_discount = 0;

                // Set temp index
                $index = 1;
                foreach ($products as $product) {
                    $product->temp_index = $index;
                    $index++;
                }

                $copier = new DeepCopy();
                $this->tempProducts = $copier->copy($products);
                $isBXGYForProduct = !!data_get($rules, 'get.product_id');
                if ($promotionRule->type === PromotionTypeEnum::BUY_X_GET_Y && !$isBXGYForProduct) {
                    $this->tempProducts = $this->tempProducts->sortByDesc('price');
                }

                while (true) {
                    // Find and remove X from list
                    $findX = $this->findX($promotionRule, $isBXGYForProduct);
                    if (!$findX) {
                        break;
                    }

                    if ($isBXGYForProduct) {
                        $this->tempProducts = $copier->copy($products);
                    }

                    // Find and remove from list
                    $findY = $this->findY($promotionRule, $isBXGYForProduct);
                    if (!$findY) {
                        break;
                    }
                }
                foreach ($products as $product) {
                    unset($product->temp_index);
                    $this->total_discount += $product->discount_amount;
                }

                unset($this->tempProducts);
                return $this->total_discount;
            }
            foreach ($products as $productX) {
                $productX->is_match_rule = false;

                // If current promotion follow rule then add product to $matches_product
                if ($this->matchRule($productX, $promotionRule)) {
                    $totalAmount += $productX->calculateTotal();
                    $totalQuantity += $productX->quantity;
                    $productX->is_match_rule = true;
                }
            }

            if ($promotionRule->type === PromotionTypeEnum::TIERS_DISCOUNT) {
                if (array_key_exists('tiers', $rules)) {
                    $tiers = $rules['tiers'];
                    $quantities = [];

                    foreach ($tiers as $tier) {
                        if (array_key_exists('qty', $tier)) {
                            $quantities[$tier['qty']] = $tier['discount'];
                        }
                    }

                    ksort($quantities);

                    foreach ($quantities as $quantityRule => $value) {
                        if ($totalQuantity >= $quantityRule) {
                            $discountPercentage = $value / 100;
                        }
                    }
                }
                if ($discountPercentage > 0) {
                    $matchRule = true;
                }
            } elseif ($totalQuantity > 0) {
                $matchRule = true;
            }

            if (array_key_exists('minimum_amount', $rules)) {
                $minimumAmount = $rules['minimum_amount'];

                if ($totalAmount < $minimumAmount) {
                    $matchRule = false;
                }
            }

            if (array_key_exists('minimum_quantity', $rules)) {
                $minimumQuantity = $rules['minimum_quantity'];

                if ($totalQuantity < $minimumQuantity) {
                    $matchRule = false;
                }
            }

            if ($matchRule) { // Calculate discount if match rule
                $totalDiscount = 0;

                switch ($type) {
                    case PromotionTypeEnum::PERCENT_DISCOUNT: // percent discount
                        $discountPercentage = ($rules['discount_percentage'] / 100);
                        foreach ($products as $productX) {
                            if ($productX->is_match_rule) {
                                $productX->discount_amount = $productX->calculateTotal() * $discountPercentage;
                                $totalDiscount += $productX->discount_amount;
                            }
                        }
                        break;
                    case PromotionTypeEnum::FIXED_DISCOUNT: // fixed discount
                        $allowDiscount = false;
                        foreach ($products as $productX) {
                            if ($productX->is_match_rule) {
                                $productX->discount_amount = ($productX->calculateTotal() / $totalAmount) * $totalDiscount;
                                $allowDiscount = true;
                            }
                        }
                        if ($allowDiscount) {
                            $totalDiscount = $rules['discount_amount'];
                        }
                        break;
                    case PromotionTypeEnum::FREE_SHIPPING:
                        $matchCountry = false;

                        if (isset($rules['countries'])) {
                            $countryCode = strtoupper($this->country);
                            $locationInfo = getLocationByCode($countryCode);
                            $locationCodes = ["*"];

                            if (!is_null($locationInfo)) {
                                $locationCodes = $locationInfo->getRegionCodes();
                            }
                            $locations = explode(',', $rules['countries']);

                            // get match at least one
                            $c = array_intersect($locations, $locationCodes);
                            if (count($c) > 0) {
                                $matchCountry = true;
                            }
                        }
                        if ($matchCountry) {
                            $totalDiscountOrder = $this->total_shipping_amount;
                            $standardProducts = collect();
                            if ($this->shipping_method === ShippingMethodEnum::EXPRESS) {
                                $cloneOrder = deep_copy($this);
                                /** @var Order $cloneOrder */
                                $cloneOrder->shipping_method = ShippingMethodEnum::STANDARD;
                                $cloneOrder->products = $products;
                                $totalDiscountOrder = $cloneOrder->calculateShippingCost();
                                $standardProducts = $cloneOrder->products;
                            }
                            $fsPercent = Arr::get($rules, 'fs_percent');
                            $fsMaxAmount = (int)Arr::get($rules, 'fs_max_amount');
                            $fsPercent = empty($fsPercent) ? 100 : (int)$fsPercent;
                            if ($fsPercent > 0) {
                                $totalDiscountOrder = $totalDiscountOrder * $fsPercent / 100;
                            }

                            if ($fsMaxAmount > 0 && $totalDiscountOrder > $fsMaxAmount) {
                                $totalDiscountOrder = $fsMaxAmount;
                            }
                            $totalFsDiscount = 0;
                            foreach ($products as $productX) {
                                if ($productX->is_match_rule) {
                                    $productX->discount_amount = ($productX->calculateTotal() / $totalAmount) * $totalDiscountOrder;
                                    $productForCountingDiscount = $productX;
                                    if (isset($productX->id) && $standardProducts->isNotEmpty()) {
                                        $productForCountingDiscount = $standardProducts->filter(function ($sProduct) use ($productX) {
                                            return (int)$sProduct->id === (int)$productX->id;
                                        })->first();
                                    }
                                    $productDiscount = $productForCountingDiscount?->shipping_cost ?? $productX?->shipping_cost ?? 0;
                                    if ($fsPercent > 0) {
                                        $productDiscount = $productDiscount * $fsPercent / 100;
                                    }

                                    $totalFsDiscount += $productDiscount;
                                }
                            }

                            if ($fsMaxAmount > 0 && $totalFsDiscount > $fsMaxAmount) {
                                $totalDiscount += $fsMaxAmount;
                            } else {
                                $totalDiscount += $totalFsDiscount;
                            }
                        }
                        break;
                    case PromotionTypeEnum::TIERS_DISCOUNT: // tiers discount
                        foreach ($products as $productX) {
                            if ($productX->is_match_rule) {
                                $productX->discount_amount = $productX->calculateTotal() * $discountPercentage;
                                $totalDiscount += $productX->discount_amount;
                            }
                        }
                        break;
                    default:
                        break;
                }
                $this->total_discount += $totalDiscount;
            }

            // cancel discount if total discount is greater than 50% subtotal
            if ($this->total_discount > $this->total_product_amount * 0.5) {
                $this->total_discount = 0;
            }

            foreach ($products as $productX) {
                if ($this->total_discount === 0) {
                    $productX->discount_amount = 0;
                }
                //Remove custom field
                unset($productX->is_match_rule);
            }
        } catch (\Exception $e) {
            graylogError('Error in calculate discount', array(
                'category' => 'order_logs',
                'order_id' => $this->id,
                'data_error' => json_encode($e),
            ));
        }
        return round($this->total_discount, 2);
    }

    public function calculatePaymentDiscount()
    {
        if ($this->payment_discount > 0) {
            $this->payment_discount = $this->total_product_amount * getPaymentDiscountRate();
        }
        return round($this->payment_discount, 2);
    }

    public function countTotalQuantity(): int
    {
        $quantity = 0;
        $this->products->map(static function ($product) use (&$quantity) {
            if (
                $product->fulfill_status !== OrderProductFulfillStatus::OUT_OF_STOCK
                && $product->fulfill_status !== OrderProductFulfillStatus::CANCELLED
            ) {
                $quantity += (int)$product->quantity;
            }
        });
        return $quantity;
    }

    public function calculateSellerProfit(): void
    {
        $processingFee = getProcessingFee();
        $processingFeeMin = getProcessingFeeMin();
        $transactionFeeRate = getTransactionFee();
        $transactionFeeMin = getTransactionFeeMin();
        $tipShareRate = getTipShareRate();
        $fulfillProcessingFeeRate = getFulfillProcessingFee();
        $this->total_seller_profit = 0;
        $this->total_artist_profit = 0;
        $this->products->map(
            function ($orderProduct) use ($processingFee, $processingFeeMin, $transactionFeeRate, $transactionFeeMin, $fulfillProcessingFeeRate) {
                if ($orderProduct->total_amount > 0) {
                    $totalAmount = $orderProduct->total_amount - $orderProduct->discount_amount;
                    $profit = 0;

                    if ($this->type === OrderTypeEnum::REGULAR) {
                        $profit = $totalAmount - $orderProduct->base_cost * $orderProduct->quantity - max($totalAmount * $processingFee, $processingFeeMin);
                    } else if ($this->type === OrderTypeEnum::CUSTOM) {
                        $totalAmount += $orderProduct->shipping_cost;
                        $transactionFee = max($totalAmount * $transactionFeeRate, $transactionFeeMin);
                        $fulfillFee = ($orderProduct->base_cost * $orderProduct->quantity + $orderProduct->shipping_cost) * (1 + $fulfillProcessingFeeRate);
                        $profit = $totalAmount - $transactionFee - $fulfillFee;
                    }

                    if ($orderProduct->seller_id === $this->seller_id) {
                        $orderProduct->seller_profit = $profit;
                        $orderProduct->artist_profit = 0;
                    } else {
                        $orderProduct->seller_profit = $profit * (optional($this->seller)->getSellerCommissionRate() ?? 0);
                        // prevent seller null
                        $orderProduct->artist_profit = $profit * (optional($orderProduct->seller)->getArtistCommissionRate() ?? 0);
                    }
                } else {
                    $orderProduct->seller_profit = 0;
                    $orderProduct->artist_profit = 0;
                }

                if ($this->ad_campaign === 'smart_remarketing') {
                    // share profit 50:50 for smart remarketing order
                    $orderProduct->seller_profit /= 2;
                }

                if ($this->type === OrderTypeEnum::CUSTOM && $orderProduct->artist_profit > 0) {
                    $this->processing_fee += $orderProduct->artist_profit; // charge artist profit from custom gate
                }

                $this->total_seller_profit += $orderProduct->seller_profit;
                $this->total_artist_profit += $orderProduct->artist_profit;
            }
        );

        if ($this->ad_campaign === 'smart_remarketing' && $this->type === OrderTypeEnum::CUSTOM) {
            // charge share profit if customer pay via custom gate
            $this->processing_fee += $this->total_seller_profit;
        }

        $this->total_seller_profit += $this->insurance_fee * 0.2;
        $this->total_seller_profit += $this->tip_amount * $tipShareRate;
        if ($this->type === OrderTypeEnum::REGULAR) {
            $this->total_seller_profit -= $this->tip_amount * $processingFee;
        }
    }

    public function calculateFulfillProcessingFee(): void
    {
        if ($this->type !== OrderTypeEnum::CUSTOM) {
            return;
        }

        $transactionFeeRate = getTransactionFee();
        $this->processing_fee = ($this->total_amount - $this->insurance_fee) * $transactionFeeRate;
        $this->processing_fee += $this->insurance_fee * 0.8;
        $fulfillProcessingFeeRate = getFulfillProcessingFee();
        $this->total_fulfill_fee = ($this->total_product_base_cost + $this->total_shipping_amount) * (1 + $fulfillProcessingFeeRate);
    }

    /**
     * @return Order
     */
    public function calcTotalShippingCostAndProfit(): Order
    {
        $this->total_fulfill_base_cost = 0; // tổng giá vốn
        $this->total_fulfill_shipping_cost = 0; // tổng phí vận chuyển
        $this->total_fulfill_profit = 0; // tổng lợi nhuận

        if (!$this->products) {
            $this->load('products');
        }

        $this->products
            // Khi sản phẩm đã bị hủy thì không tính phí vận chuyển
            ->reject(static fn(OrderProduct $p) => $p->fulfill_status === OrderFulfillStatus::CANCELLED)

            // Khi sản phẩm chưa gắn supplier thì không tính phí vận chuyển
            // Vì phí vận chuyển của sản phẩm fulfill sẽ được gắn đến từng variant
            ->reject(static fn(OrderProduct $p) => !$p->supplier_id)

            // Tính phí vận chuyển cho từng sản phẩm theo supplier và variant của nó
            // và cộng dồn vào tổng phí vận chuyển của order theo logic là từ sản phẩm thứ 2
            // trở đi của cùng supplier thì tính theo phí extra
            ->reduce(function (array $exists, OrderProduct $op) {
                $op->fulfill_shipping_cost = 0;

                $rule = app(ShippingRule::class)->takeLowestCost(
                    $op->fulfill_product_id,
                    $op->supplier_id,
                    getVariantKey($op->options, $op->full_printed),
                    $this->prepareLocationCodes()
                );

                if (empty($rule)) {
                    $rule = (object)['shipping_cost' => 0, 'extra_cost' => 0];
                }

                $rule->extra_cost = $rule->extra_cost ?: $rule->shipping_cost;

                // sản phẩm thứ 2 trở đi của cùng supplier thì tính theo phí extra
                if (array_key_exists($op->supplier_id, $exists)) {
                    $op->fulfill_shipping_cost = $rule->extra_cost * $op->quantity;
                } else {
                    $op->fulfill_shipping_cost = $rule->shipping_cost + $rule->extra_cost * ($op->quantity - 1);
                }

                $exists[$op->supplier_id] = $op->fulfill_shipping_cost;

                $this->total_fulfill_base_cost += $op->quantity * $op->fulfill_base_cost;
                $this->total_fulfill_shipping_cost += $op->fulfill_shipping_cost;
                $this->total_fulfill_profit += $op->fulfill_profit;

                return $exists;
            }, []);

        return $this;
    }

    public function calculateCustomerPaid(): void
    {
        $totalPaid = $this->total_paid;
        $arrFill = [
            'total_orders' => DB::raw("`total_orders` + 1"),
            'total_purchases' => DB::raw("`total_purchases` + $totalPaid"),
        ];
        $this->customer()->update($arrFill);
        $this->seller_customer()->update($arrFill);
    }


    /**
     * ------------------------------
     * Getters
     * ------------------------------
     */

    public function getId()
    {
        return $this->id;
    }

    public function getStoreId()
    {
        return $this->store_id;
    }

    public function getRefId()
    {
        return $this->ref_id;
    }

    public function getIpAddress()
    {
        return $this->ip_address;
    }

    public function getIpLocation()
    {
        return $this->ip_location;
    }

    public function getDevice()
    {
        return $this->device;
    }

    public function getDeviceDetail()
    {
        return strtolower($this->device_detail);
    }

    public function getDeviceId()
    {
        return $this->device_id;
    }

    public function getCompanyId()
    {
        return $this->company_id;
    }

    public function getAdId()
    {
        return $this->ad_id;
    }

    public function getAdSource()
    {
        return $this->ad_source;
    }

    public function getAdMedium()
    {
        return $this->ad_medium;
    }

    public function getAdCampaign()
    {
        return $this->ad_campaign;
    }

    public function getSessionId()
    {
        return $this->session_id;
    }

    public function getPaymentGatewayId()
    {
        return $this->payment_gateway_id;
    }

    public function getPaymentMethod()
    {
        return $this->payment_method;
    }

    public function getSellerId()
    {
        return $this->seller_id;
    }

    public function getSubtotal($recalculate = false)
    {
        if ($recalculate) {
            $this->calculateSubtotal();
        }

        return $this->total_product_amount;
    }

    public function getTotalQuantity()
    {
        return $this->total_quantity;
    }

    public function getAccessToken()
    {
        return $this->access_token;
    }

    public function getOrderNumber()
    {
        return $this->order_number;
    }

    public function getOrderNumber2()
    {
        return $this->order_number_2;
    }

    public function getOrderNote()
    {
        return $this->order_note;
    }

    public function getStoreName()
    {
        return $this->store_name;
    }

    public function getStoreDomain()
    {
        return $this->store_domain;
    }

    public function getCustomerName()
    {
        return $this->customer_name;
    }

    public function getCustomerEmail()
    {
        return $this->customer_email;
    }

    public function getCustomerPhone()
    {
        return $this->customer_phone;
    }

    public function getRegion()
    {
        return $this->region;
    }

    public function getBundleProductId()
    {
        return $this->bundle_product_id;
    }

    public function getTotalShippingAmount()
    {
        return $this->total_shipping_amount;
    }

    public function getTotalBaseShippingCost()
    {
        return $this->total_base_shipping_cost;
    }

    public function getTotalFulfillShippingCost()
    {
        return $this->total_fulfill_shipping_cost;
    }

    public function getTotalDiscount()
    {
        return (float)$this->total_discount;
    }

    public function getTotalTaxAmount()
    {
        return (float)$this->total_tax_amount;
    }

    public function getTotalAmount()
    {
        return (float)$this->total_amount;
    }

    public function getTotalPaid()
    {
        return (float)$this->total_paid;
    }

    public function getTotalRefund()
    {
        return (float)$this->total_refund;
    }

    public function getTotalProductCost()
    {
        return (float)$this->total_product_cost;
    }

    public function getFulfillStatus()
    {
        return $this->fulfill_status;
    }

    public function getTotalProductBaseCost()
    {
        return (float)$this->total_product_base_cost;
    }

    public function getTotalSellerProfit()
    {
        return (float)$this->total_seller_profit;
    }

    public function getTotalProfit()
    {
        return (float)$this->total_profit;
    }

    public function getCurrencyCode()
    {
        return $this->currency_code;
    }

    public function getCurrencyRate()
    {
        return (float)$this->currency_rate;
    }

    public function getDiscountCode()
    {
        return $this->discount_code;
    }

    public function getShippingAddress()
    {
        return $this->shipping_address;
    }

    public function getBillingAddress()
    {
        return $this->billing_address;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function getStatusUrl()
    {
        return $this->status_url;
    }

    public function getCountryCode()
    {
        return $this->country;
    }

    // Alias of getCountryCode
    public function getCountry()
    {
        return $this->country;
    }

    public function getShippingMethod()
    {
        return $this->shipping_method;
    }

    public function getPromotionRuleId()
    {
        return $this->promotion_rule_id;
    }

    public function getPromotionRule()
    {
        return $this->promotion_rule;
    }

    public function getPromotion()
    {
        return $this->promotion;
    }

    public function getSeller()
    {
        return $this->seller;
    }

    public function getStore()
    {
        return $this->store;
    }

    public function getOrderHistories()
    {
        return $this->order_history;
    }

    public function getOrderItems()
    {
        return $this->orderItems;
    }

    public function getFraudStatus()
    {
        return $this->fraud_status;
    }

    /**
     * TODO: Implement get description
     */
    public function getDescription()
    {
        return $this->getId();
    }

    /**
     * @return string|null
     */
    public function getPaymentLog(): string|null
    {
        return $this->payment_log;
    }

    public function getPaymentGateway()
    {
        return $this->paymentGateway;
    }

    /**
     * @return string|null
     */
    public function getTransactionId(): string|null
    {
        return $this->transaction_id;
    }

    public function getCheckoutDomain()
    {
        return $this->checkout_domain;
    }

    public function getCheckoutStoreId()
    {
        return $this->checkout_store_id;
    }

    /**
     * @return string|null
     */
    public function getType(): string|null
    {
        return $this->type;
    }

    /**
     * @return float
     */
    public function getPaymentDiscount(): float
    {
        return (float)$this->payment_discount;
    }

    /**
     * @return float
     */
    public function getTipAmount(): float
    {
        return (float)$this->tip_amount;
    }

    /**
     * @return float
     */
    // Alias of getSubtotal
    public function getTotalProductAmount(): float
    {
        return (float)$this->total_product_amount;
    }

    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @return string|null
     */
    public function getAddress(): string|null
    {
        return $this->address;
    }

    /**
     * @return string|null
     */
    public function getAddress2(): string|null
    {
        return $this->address_2;
    }

    /**
     * @return string|null
     */
    public function getCity(): string|null
    {
        return $this->city;
    }

    /**
     * @return string|null
     */
    public function getState(): string|null
    {
        return $this->state;
    }

    /**
     * @return string|null
     */
    public function getPostcode(): string|null
    {
        return $this->postcode;
    }

    /**
     * @return string|null
     */
    public function getAddressVerified(): string|null
    {
        return $this->address_verified;
    }

    public function getPaidAt()
    {
        return $this->paid_at;
    }

    /**
     * @return string|null
     */
    public function getPaymentStatus(): string|null
    {
        return $this->payment_status;
    }

    /**
     * @return float
     */
    public function getTotalShippingCost(): float
    {
        return (float)$this->total_shipping_cost;
    }

    /**
     * @return float
     */
    public function getPaymentFee(): float
    {
        return (float)$this->payment_fee;
    }

    /**
     * @return float
     */
    public function getProcessingFee(): float
    {
        return (float)$this->processing_fee;
    }

    /**
     * @return float
     */
    public function getTotalFulfillFee(): float
    {
        return (float)$this->total_fulfill_fee;
    }

    /**
     * @return float
     */
    public function getProcessingFeePaid(): float
    {
        return (float)$this->processing_fee_paid;
    }

    /**
     * @return float
     */
    public function getFulfillFeePaid(): float
    {
        return (float)$this->fulfill_fee_paid;
    }

    /**
     * @return int|null
     */
    public function getRemarketingStatus(): int|null
    {
        return $this->remarketing_status;
    }

    /**
     * @return int|null
     */
    public function getStatsStatus(): int|null
    {
        return $this->stats_status;
    }

    public function getCreatedAt()
    {
        return $this->created_at;
    }

    public function getFulfilledAt()
    {
        return $this->fulfilled_at;
    }

    public function getDeliveredAt()
    {
        return $this->delivered_at;
    }

    public function getReceivedAt()
    {
        return $this->received_at;
    }

    /**
     * @return string|null
     */
    public function getFulfillLog(): string|null
    {
        return $this->fulfill_log;
    }

    /**
     * @return string|null
     */
    public function getFlagLog(): string|null
    {
        return $this->flag_log;
    }

    public function getDatestamp()
    {
        return $this->datestamp;
    }

    public function getSyncAt()
    {
        return $this->synced_at;
    }

    /**
     * @return string|null
     */
    public function getSenFulfillStatus(): string|null
    {
        return $this->sen_fulfill_status;
    }

    /**
     * @return int|null
     */
    public function getCustomerId(): int|null
    {
        return $this->customer_id;
    }

    /**
     * @return string|null
     */
    public function getAdminNote(): string|null
    {
        return $this->admin_note;
    }

    /**
     * @return int|null
     */
    public function getExportId(): int|null
    {
        return $this->export_id;
    }

    public function getExportedAt()
    {
        return $this->exported_at;
    }

    public function getDeletedAt()
    {
        return $this->deleted_at;
    }

    /**
     * @return int|null
     */
    public function getPersonalized(): int|null
    {
        return $this->personalized;
    }

    /**
     * @return int|null
     */
    public function getLast4CardNumber(): int|null
    {
        return $this->last4_card_number;
    }

    /**
     * @return float|null
     */
    public function getRawInsuranceFee()
    {
        return $this->insurance_fee;
    }

    /**
     * @return string|null
     */
    public function getVisitInfo()
    {
        return $this->visit_info;
    }


    public function getHouseNumber()
    {
        return $this->house_number;
    }

    public function getMailboxNumber()
    {
        return $this->mailbox_number;
    }

    public function getRegionSyncedAt()
    {
        return $this->region_synced_at;
    }

    public function getEstimateDeliveryDate()
    {
        return $this->estimate_delivery_date;
    }

    public function getIossNumber()
    {
        return $this->ioss_number;
    }

    public function getIsCornerPlacement()
    {
        return $this->is_corner_placement;
    }

    public function getApprovedAt()
    {
        return $this->approved_at;
    }


    /**
     * ------------------------------
     * Setters
     * ------------------------------
     */

    public function setDeliveredAt($value)
    {
        $this->delivered_at = $value;
    }

    public function setFulfilledAt($value)
    {
        $this->fulfilled_at = $value;
    }

    public function setUpdatedAt($value)
    {
        $this->updated_at = $value;
    }

    public function setCreatedAt($value)
    {
        $this->created_at = $value;
    }

    public function setOrderNote($value)
    {
        $this->order_note = $value;
    }

    public function setAddressVerified($value)
    {
        $this->address_verified = $value;
    }

    public function setType($value)
    {
        $this->type = $value;
    }

    public function setStatusUrl($value)
    {
        $this->status_url = $value;
    }

    public function setRemarketingStatus($value)
    {
        $this->remarketing_status = $value;
    }

    public function setFulfillFeePaid($value)
    {
        $this->fulfill_fee_paid = $value;
    }

    public function setProcessingFeePaid($value)
    {
        $this->processing_fee_paid = $value;
    }

    public function setTotalFulfillFee($value)
    {
        $this->total_fulfill_fee = $value;
    }

    public function setInsuranceFee($value)
    {
        $this->insurance_fee = $value;
    }

    public function setProcessingFee($value)
    {
        $this->processing_fee = $value;
    }

    public function setPaymentFee($value)
    {
        $this->payment_fee = $value;
    }

    public function setShippingAddress($value)
    {
        $this->shipping_address = $value;
    }

    public function setBillingAddress($value)
    {
        $this->billing_address = $value;
    }

    public function setTotalProfit($value)
    {
        $this->total_profit = $value;
    }

    public function setTotalSellerProfit($value)
    {
        $this->total_seller_profit = $value;
    }

    public function setTotalShippingCost($value)
    {
        $this->total_shipping_cost = $value;
    }

    public function setTotalProductBaseCost($value)
    {
        $this->total_product_base_cost = $value;
    }

    public function setTotalProductCost($value)
    {
        $this->total_product_cost = $value;
    }

    public function setTotalRefund($value)
    {
        $this->total_refund = $value;
    }

    public function setTotalTaxAmount($value)
    {
        $this->total_tax_amount = $value;
    }

    public function setTipAmount($value)
    {
        $this->tip_amount = $value;
    }

    public function setTotalQuantity($value)
    {
        $this->total_quantity = $value;
    }

    public function setReceivedAt($value)
    {
        $this->received_at = $value;
    }

    public function setFulfillLog($value)
    {
        $this->fulfill_log = $value;
    }

    public function setFlagLog($value)
    {
        $this->flag_log = $value;
    }

    public function setDatestamp($value)
    {
        $this->datestamp = $value;
    }

    public function setSyncAt($value)
    {
        $this->synced_at = $value;
    }

    public function setTransactionId($value)
    {
        $this->transaction_id = $value;
    }

    public function setSenFulfillStatus($value)
    {
        $this->sen_fulfill_status = $value;
    }

    public function setAvailableShippingMethods($value)
    {
        $this->available_shipping_methods = $value;
    }

    public function setCheckoutDomain($value)
    {
        $this->checkout_domain = $value;
    }

    public function setCheckoutStoreId($value)
    {
        $this->checkout_store_id = $value;
    }

    public function setTotalProductFulfillBaseCost($value)
    {
        $this->total_product_fulfill_base_cost = $value;
    }

    public function setPaymentGatewaysAvailable($value)
    {
        $this->payment_gateways_available = $value;
    }

    public function setSellerId($value)
    {
        $this->seller_id = $value;
    }

    public function setRefId($value)
    {
        $this->ref_id = $value;
    }

    public function setCustomerName($value)
    {
        $this->customer_name = $value;
    }

    public function setCustomerEmail($value)
    {
        $this->customer_email = $value;
    }

    public function setCustomerPhone($value)
    {
        $this->customer_phone = $value;
    }

    public function setAddress($value)
    {
        $this->address = $value;
    }

    public function setTotalDiscount($value)
    {
        $this->total_discount = $value;
    }

    public function setTotalAmount($value)
    {
        $this->total_amount = $value;
    }

    public function setAddress2($value)
    {
        $this->address_2 = $value;
    }

    public function setCity($value)
    {
        $this->city = $value;
    }

    public function setState($value)
    {
        $this->state = $value;
    }

    public function setStatus($value)
    {
        $this->status = $value;
    }

    public function setPostcode($value)
    {
        $this->postcode = $value;
    }

    public function setCountry($value)
    {
        $this->country = $value;
    }

    public function setStoreId($value)
    {
        $this->store_id = $value;
    }

    public function setFulfillStatus($status)
    {
        $this->fulfill_status = $status;
    }

    public function setTotalProductAmount($value)
    {
        $this->total_product_amount = $value;
    }


    public function setTotalBaseShippingCost($value)
    {
        $this->total_base_shipping_cost = $value;
    }

    public function setTotalFulfillShippingCost($value)
    {
        $this->total_fulfill_shipping_cost = $value;
    }

    public function setTotalShippingAmount($value)
    {
        $this->total_shipping_amount = round($value, 2);
    }

    public function setId($value)
    {
        return $this->id = $value;
    }

    public function setShippingMethod($value)
    {
        $this->shipping_method = $value;
    }

    public function setAccessToken($value)
    {
        $this->access_token = $value;
    }

    public function setOrderNumber($value)
    {
        $this->order_number = $value;
    }

    public function setOrderNumber2($value)
    {
        $this->order_number_2 = $value;
    }

    public function setRefIf($value)
    {
        $this->ref_id = $value;
    }

    public function setIpAddress($value)
    {
        $this->ip_address = $value;
    }

    public function setIpLocation($value)
    {
        $this->ip_location = $value;
    }

    public function setDevice($value)
    {
        $this->device = $value;
    }

    public function setDeviceDetail($value)
    {
        $this->device_detail = strtolower($value);
    }

    public function setDeviceId($value)
    {
        $this->device_id = $value;
    }

    public function setStoreName($value)
    {
        $this->store_name = $value;
    }

    public function setCompanyId($value)
    {
        $this->company_id = $value;
    }

    public function setAdId($value)
    {
        $this->ad_id = $value;
    }

    public function setAdSource($value)
    {
        $this->ad_source = $value;
    }

    public function setAdMedium($value)
    {
        $this->ad_medium = $value;
    }

    public function setAdCampaign($value)
    {
        $this->ad_campaign = $value;
    }

    public function setSessionId($value)
    {
        $this->session_id = $value;
    }

    public function setPaymentGatewayId($value)
    {
        $this->payment_gateway_id = $value;
    }

    public function setPaymentLog($value)
    {
        $this->payment_log = $value;
    }

    public function setPaymentMethod($value)
    {
        $this->payment_method = $value;
    }

    public function setCurrencyRate($value)
    {
        $this->currency_rate = $value;
    }

    public function setCurrencyCode($value)
    {
        $this->currency_code = $value;
    }

    public function setStoreDomain($value)
    {
        $this->store_domain = $value;
    }

    public function setPromotionRule($value)
    {
        $this->promotion_rule = $value;
    }

    public function setPromotionRuleId($value)
    {
        $this->promotion_rule_id = $value;
    }

    public function setDiscountCode($value)
    {
        $this->discount_code = $value;
    }

    public function setTotalPaid($value)
    {
        $this->total_paid = $value;
    }

    public function setFraudStatus($value)
    {
        $this->fraud_status = $value;
    }

    public function setStatsStatus($value)
    {
        return $this->stats_status = $value;
    }

    public function setPaymentStatus($value)
    {
        $this->payment_status = $value;
    }

    public function setPaidAt($value)
    {
        $this->paid_at = $value;
    }

    public function setRegion($value)
    {
        $this->region = $value;
    }

    public function setBundleProductId($value)
    {
        $this->bundle_product_id = $value;
    }

    public function setCustomerId($value)
    {
        $this->customer_id = $value;
    }

    public function setPaymentDiscount($value)
    {
        $this->payment_discount = $value;
    }

    public function setAdminNote($value)
    {
        $this->admin_note = $value;
    }

    public function setExportId($value)
    {
        $this->export_id = $value;
    }

    public function setExportedAt($value)
    {
        $this->exported_at = $value;
    }

    public function setDeletedAt($value)
    {
        $this->deleted_at = $value;
    }

    public function setPersonalized($value)
    {
        $this->personalized = $value;
    }

    public function setLast4CardNumber($value)
    {
        $this->last4_card_number = $value;
    }

    public function setVisitInfo($value)
    {
        $this->visit_info = $value;
    }

    /**
     * @param $value
     * @return void
     */
    public function setHouseNumber($value): void
    {
        $this->house_number = $value;
    }

    /**
     * @param $value
     * @return void
     */
    public function setMailboxNumber($value): void
    {
        $this->mailbox_number = $value;
    }

    /**
     * @param $value
     * @return void
     */
    public function setRegionSyncedAt($value): void
    {
        $this->region_synced_at = $value;
    }

    /**
     * @param $value
     * @return void
     */
    public function setEstimateDeliveryDate($value): void
    {
        $this->estimate_delivery_date = $value;
    }

    /**
     * Set the value of `ioss_number`.
     *
     * @param $value
     */
    public function setIossNumber($value): void
    {
        $this->ioss_number = $value;
    }

    /**
     * Set the value of `is_corner_placement`.
     *
     * @param $value
     */
    public function setIsCornerPlacement($value): void
    {
        $this->is_corner_placement = $value;
    }

    /**
     * Set the value of `approved_at`.
     *
     * @param $value
     */
    public function setApprovedAt($value): void
    {
        $this->approved_at = $value;
    }

    /**
     * Update status and payment status of order
     *
     * @param $query
     * @param $totalPaid
     * @param string $paymentObjectId
     * @param bool $isPayPalECheck
     * @param ?string $currencyCode
     * @param ?float $currencyRate
     * @param null $gatewayId
     * @return int
     */
    public function scopePaymentCompleted($query, $totalPaid, string $paymentObjectId = '', bool $isPayPalECheck = false, ?string $currencyCode = null, ?float $currencyRate = null, $gatewayId = null): int
    {
        $status = $isPayPalECheck ? OrderStatus::PENDING_PAYMENT : OrderStatus::PROCESSING;
        $paymentStatus = $isPayPalECheck ? OrderPaymentStatus::PENDING : OrderPaymentStatus::PAID;

        $data = [
            'payment_status' => $paymentStatus,
            'status' => $status,
            'total_paid' => $totalPaid,
            'transaction_id' => $paymentObjectId,
            'paid_at' => DB::raw('CASE WHEN `paid_at` IS NULL THEN CURRENT_TIMESTAMP ELSE `paid_at` END'),
        ];

        // update currency
        if (!empty($currencyCode) && $currencyRate !== null) {
            $data['currency_code'] = $currencyCode;
            $data['currency_rate'] = $currencyRate;
        }
        $this->correctPaymentMethod($data, $paymentObjectId, $gatewayId);
        $updated = $query->where('id', $this->id)
            ->whereIn('payment_status', [
                OrderPaymentStatus::UNPAID,
                OrderPaymentStatus::PENDING,
                OrderPaymentStatus::FAILED,
            ])
            ->update($data);

        if (is_numeric($updated) && $updated > 0) {
            // dispatch event to update seller profits
            $this->status = $status;
            try {
                if ($this instanceof RegionOrders) {
                    RegionOrderHistory::insertLog(
                        $this,
                        OrderHistoryActionEnum::PAID_BY_CUSTOMER,
                        $this->payment_method . ' / ' . $paymentObjectId,
                        OrderHistoryDisplayLevelEnum::CUSTOMER,
                        $this->region
                    );
                    SyncOrderJob::dispatch($this->id, $this->getRegion())->onQueue('sync_order_region');
                } else {
                    OrderHistory::insertLog(
                        $this,
                        OrderHistoryActionEnum::PAID_BY_CUSTOMER,
                        $this->payment_method . ' / ' . $paymentObjectId,
                        OrderHistoryDisplayLevelEnum::CUSTOMER
                    );
                    OrderPaymentCompleted::dispatch($this);
                }
            } catch (\Throwable $e) {
                graylogError('order payment complete error', [
                    'category' => 'order_payment_complete_error',
                    'data' => $e
                ]);
                logException($e);
            }
        }

        return $updated;
    }

    /**
     * @param $query
     * @param $failedLog
     * @param string|null $paymentObjectId
     * @param int|null $gatewayId
     * @return int
     */
    public function scopePaymentFailed($query, $failedLog, ?string $paymentObjectId = '', ?int $gatewayId = null): int
    {
        $data = [
            'payment_status' => OrderPaymentStatus::FAILED,
            'status' => $this->status === OrderStatus::PENDING_PAYMENT ? OrderStatus::PENDING : $this->status,
            // 'transaction_id' => !empty($paymentObjectId) ? $paymentObjectId : null, // temporary remove
            'payment_log' => (!empty($paymentObjectId) ? $paymentObjectId . '|' : '') . $failedLog,
        ];
        $this->correctPaymentMethod($data, $paymentObjectId, $gatewayId);
        return $query->where('id', $this->id)
            ->whereIn('payment_status', [
                OrderPaymentStatus::UNPAID,
                OrderPaymentStatus::PENDING,
                OrderPaymentStatus::FAILED,
            ])
            ->update($data);
    }

    /**
     * @param array $data
     * @param $paymentObjectId
     * @param $gatewayId
     * @return mixed
     */
    private function correctPaymentMethod(array &$data, $paymentObjectId, $gatewayId): array
    {
        if (empty($this->payment_method)) {
            if (!empty($paymentObjectId)) {
                if (str_starts_with($paymentObjectId, 'pi_')) {
                    $data['payment_method'] = PaymentMethodEnum::STRIPE;
                } else {
                    $data['payment_method'] = PaymentMethodEnum::PAYPAL;
                }
                $this->payment_method = $data['payment_method'];
            }
        } else if (!is_null($gatewayId)) {
            $data['payment_gateway_id'] = $gatewayId;
            $gateway = PaymentGateway::query()
                ->where('id', $gatewayId)
                ->where('active', 1)
                ->value('gateway');
            if (!empty($gateway) && !str_starts_with(strtolower($this->payment_method), strtolower($gateway))) {
                $data['payment_method'] = $gateway;
                $this->payment_method = $data['payment_method'];
            }
        }
        return $data;
    }


    /**
     * @param $query
     * @param string|null $paymentObjectId
     * @return int
     */
    public function scopePaymentPendingReview($query, ?string $paymentObjectId = ''): int
    {
        return $query->where('id', $this->id)
            ->whereIn('payment_status', [
                OrderPaymentStatus::UNPAID,
                OrderPaymentStatus::PENDING,
                OrderPaymentStatus::FAILED,
            ])
            ->update([
                'payment_status' => OrderPaymentStatus::PENDING,
                'status' => OrderStatus::PENDING_PAYMENT,
                'transaction_id' => !empty($paymentObjectId) ? $paymentObjectId : null
            ]);
    }

    public function scopeListingExport($query, $storeDomain, $sortBy, $sortDirection)
    {
        // todo @long: transform raw to eloquent
        return $query
            ->select('store_domain')
            ->addSelect(DB::raw("COUNT(IF(export_id is null, NULL, 1)) as total_order"))
            ->addSelect(
                DB::raw("COUNT(IF(fulfill_status = '"
                    . FulfillmentStatusEnum::FULFILLED
                    . "' or fulfill_status = '"
                    . FulfillmentStatusEnum::PARTIAL_FULFILLED
                    . "', 1, NULL)) as total_has_tracking")
            )
            ->addSelect(DB::raw("
                sum(
                    (
                        select
                          count(*)
                        from
                          `tracking_status`
                        where
                          `order`.`order_number` = `tracking_status`.`order_number`
                          and `exported_at` is not null
                    )
                ) as `total_exported`
            "))
            ->addSelect(DB::raw("
                sum(
                    (
                        select
                          count(*)
                        from
                          `tracking_status`
                        where
                          `order`.`order_number` = `tracking_status`.`order_number`
                          and `exported_at` is null
                    )
                ) as `total_unexported`
            "))
            ->when(!is_null($storeDomain), function ($query) use ($storeDomain) {
                $query->where('store_domain', 'like', '%' . $storeDomain . '%');
            })
            ->groupBy('store_domain')
            ->when(!is_null($sortBy) && !is_null($sortDirection), function ($query) use ($sortBy, $sortDirection) {
                $query->orderBy($sortBy, $sortDirection);
            });
    }

    public function scopeFilterFulfillProduct(
        $query,
        $dateRange,
        $startDate = null,
        $endDate = null,
        $status = null,
        $fulfillStatus = null,
        $shippingMethod = null,
        $supportStatus = OrderSupportStatusEnum::ALL,
        $assignee = OrderAssigneeEnum::ALL,
        $dateRangeFilterColumn = 'order.fulfilled_at',
        $isListing = false,
        $isTrackingLate = false,
        $isShippingLate = false,
        $expressShippingLate = false,
    ) {
        return $query
            ->filterDateRange($dateRange, $startDate, $endDate, $dateRangeFilterColumn)
            ->filterSupport($supportStatus, $assignee)
            ->when($shippingMethod, fn($q) => $q->where('order.shipping_method', $shippingMethod))
            ->filterFulfill($status, $fulfillStatus, $supportStatus, $isListing, $isTrackingLate, $isShippingLate, $expressShippingLate);
    }

    /** @noinspection TypeUnsafeComparisonInspection */
    public function scopeFilterSupport($query, $supportStatus, $assignee)
    {
        $query->when(
            $supportStatus != OrderSupportStatusEnum::ALL,
            function ($q) use ($supportStatus) {
                if ($supportStatus === OrderSupportStatusEnum::NEED_SUPPORT) {
                    return $q->where('order.support_status', '>', OrderSupportStatusEnum::NORMAL)
                        ->whereNotIn('order.status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::COMPLETED])
                        ->whereNotIn('order.fulfill_status', [OrderFulfillStatus::CANCELLED, OrderFulfillStatus::FULFILLED, OrderFulfillStatus::ON_DELIVERY])
                        ->whereNotNull('order.admin_note')
                        ->where(function ($q) {
                            $q->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM]);
                            $q->orWhere(function ($q) {
                                $q->whereIn('order.type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                                    ->where('order.payment_status', OrderPaymentStatus::PAID)
                                    ->where('order.total_paid', '>', 0)
                                    ->whereNotNull('order.paid_at');
                            });
                        });
                }

                return $q->where('order.support_status', $supportStatus);
            }
        );

        $query->when(
            $assignee != OrderAssigneeEnum::ALL,
            fn($q) => match ($assignee) {
                OrderAssigneeEnum::ME => $q->where('order.assignee', currentUser()->getUserId()),
                OrderAssigneeEnum::UNASSIGNED => $q->whereNull('order.assignee'),
                default => $q->where('order.assignee', $assignee),
            }
        );

        return $query;
    }

    public function scopeFilterFulfill($query, $status = null, $fulfillStatus = null, $supportStatus = OrderSupportStatusEnum::ALL, $isListing = false, $isTrackingLate = false, $isShippingLate = false, $expressShippingLate = false)
    {
        if ($supportStatus !== OrderSupportStatusEnum::NEED_SUPPORT && !$isTrackingLate && !$expressShippingLate) {
            // default
            if (empty($status)) {
                $status = [
                    OrderStatus::PROCESSING,
                ];
            }
            if (empty($fulfillStatus)) {
                $fulfillStatus = [
                    OrderFulfillStatus::PROCESSING,
                    OrderFulfillStatus::UNFULFILLED,
                    OrderFulfillStatus::ON_HOLD,
                    OrderFulfillStatus::INVALID,
                    OrderFulfillStatus::REVIEWING,
                    OrderFulfillStatus::DESIGNING,
                ];
            }
        } else {
            // get all
            $status = null;
            $fulfillStatus = null;
        }

        if ($isShippingLate) {
            $fulfillStatus = null;
        }

        if ($isListing) {
            $arrSenFulfillStatus = [
                OrderSenFulfillStatus::YES,
                OrderSenFulfillStatus::PENDING,
            ];
        } else {
            $arrSenFulfillStatus = [
                OrderSenFulfillStatus::YES,
            ];
        }

        return $query->where(
            function ($query) use ($status, $fulfillStatus, $arrSenFulfillStatus) {
                if (!empty($status) && $status !== ['1']) {
                    $query->whereIn('order.status', $status);
                }
                if (!empty($fulfillStatus) && $fulfillStatus !== ['1']) {
                    $query->whereIn('order.fulfill_status', $fulfillStatus);
                }

                $query->whereIn('order.sen_fulfill_status', $arrSenFulfillStatus);
            }
        );
    }

    public function scopeSelectedFieldToFulfill($query)
    {
        return $query->select([
            'order.id as id',
            'order.store_domain',
            'order.order_number',
            'order.customer_email',
            'order.created_at',
            'order.total_amount',
            'order.customer_name',
            'order.address',
            'order.address_2',
            'order.city',
            'order.state',
            'order.postcode',
            'order.country',
            'order.customer_phone',
            'order.order_note',
        ]);
    }

    public function scopeIsAbandoned($query)
    {
        $limitFromThisDate = Carbon::parse('2021-12-27 21:18:00'); #TODO: @james send order to abandoned after this date

        if (!app()->environment(EnvironmentEnum::PRODUCTION)) {
            $dateForIdentifyAbandoned = now()->subMinute(); // for testing
        } else {
            $dateForIdentifyAbandoned = now()->subHour();
        }

        return $query->where(
            function ($query) use ($dateForIdentifyAbandoned, $limitFromThisDate) {
                return $query->where('status', OrderStatus::PENDING)
                    ->where('payment_status', OrderPaymentStatus::UNPAID)
                    ->where('updated_at', '<', $dateForIdentifyAbandoned) // 5 minutes before
                    ->where('updated_at', '>', now()->subDays(30)) // 30 days before
                    ->where('updated_at', '>', $limitFromThisDate);
            }
        );
    }

    public function scopeGetChangesDetail(): string
    {
        return getChangesDetailForModel($this);
    }

    public function scopeSelectForHistory($q, $addSelect = '')
    {
        $q->select(OrderHistory::ARR_SELECT_ORDER);
        if (!empty($addSelect)) {
            $q->addSelect($addSelect);
        }

        return $q;
    }

    public function scopeFilterReceivedDateRange($query, $dateRange)
    {
        $dateRanges = [
            'column' => 'order.received_at',
            'type' => $dateRange
        ];

        return filterQueryByDateRange($dateRanges, $query);
    }

    public function scopeGetSoldItems($query)
    {
        return $query->selectRaw("sum(case when `order`.`type` = '" . OrderTypeEnum::REGULAR . "' OR (`order`.`type` = '" . OrderTypeEnum::CUSTOM . "' and `order`.`sen_fulfill_status`='" . OrderSenFulfillStatus::YES . "') then `order_product`.`quantity` else 0 end) as sold_items");
    }

    public function scopeGetFulfillItems($query)
    {
        return $query->selectRaw("sum(case when `order`.`type` = '" . OrderTypeEnum::FULFILLMENT . "' OR `order`.`type` = '" . OrderTypeEnum::FBA . "' then `order_product`.`quantity` else 0 end) as fulfill_items");
    }

    public function scopeGetSenPoints($query)
    {
        return $query->selectRaw("sum(`order_product`.`sen_points`) as sen_points");
    }

    public function scopeIsValidPaidOrder($query)
    {
        return $query
            ->whereIn('order.payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->whereNotIn('order.status', [
                OrderStatus::CANCELLED,
                OrderStatus::REFUNDED,
            ]);
    }

    public function scopeAnalyticReferral($query, $dateRange, $startDate, $endDate, $refId = null, $columnGroupBy = '')
    {
        return $query
            ->getSoldItems()
            ->getFulfillItems()
            ->getSenPoints()
            ->filterDateRange($dateRange, $startDate, $endDate, self::FILTER_COLUMN_DATE, $refId)
            ->join('order_product', 'order.id', 'order_product.order_id')
            ->whereNull('order_product.deleted_at')
            ->when(!is_null($refId), function ($q) use ($refId) {
                $q->where('order.ref_id', $refId);
            })
            ->isValidPaidOrder()
            ->when(!empty($columnGroupBy), function ($q) use ($columnGroupBy) {
                $q->addSelect('order.' . $columnGroupBy);
                $q->groupBy('order.' . $columnGroupBy);
            });
    }

    public function scopeExcludeTest($query)
    {
        return $query->where(function ($q) {
            $q->where('order.customer_email', '!=', '<EMAIL>');
            $q->where('order.customer_email', '!=', '<EMAIL>');
        })->orWhereNull('order.customer_email');
    }

    public function scopeInvalidAddressOrderConditions($query)
    {
        return $query
            ->where('order.address_verified', OrderAddressVerifiedEnum::INVALID)
            ->where('order.type', OrderTypeEnum::CUSTOM)
            ->where(function ($query) {
                $query->whereDoesntHave('request_cancel')
                    ->orWhereHas('request_cancel', function ($query) {
                        $query->whereNotIn('status', [OrderCancelRequestStatus::PROCESSING, OrderCancelRequestStatus::CONFIRMED, OrderCancelRequestStatus::COMPLETED]);
                    });
            });
    }

    public function scopeAnalytic($query, $columns = null)
    {
        if (!is_string($columns)) {
            $columns ??= CampaignSortByAllowEnum::getArrayForAnalyticOrder();

            foreach ($columns as $each) {
                $query->analytic($each);
            }
            return $query;
        }
        return match ($columns) {
            CampaignSortByAllowEnum::TOTAL_ITEMS => $query->selectRaw('sum(order.total_quantity) as ' . $columns),
            CampaignSortByAllowEnum::TOTAL_ORDERS => $query->selectRaw('count(order.id) as ' . $columns),
            CampaignSortByAllowEnum::TOTAL_PROFITS => $query->selectRaw('sum(order.total_seller_profit) as ' . $columns),
            CampaignSortByAllowEnum::TOTAL_SALES => $query->selectRaw('sum(order.total_amount) as ' . $columns),
            default => $query,
        };
    }

    public function scopeAnalyticWithFilterDateRangeBefore($query, $columns = null, $dateRange = null)
    {
        if (!is_string($columns)) {
            $columns ??= CampaignSortByAllowEnum::getArrayForAnalyticOrder();

            foreach ($columns as $each) {
                $query->AnalyticWithFilterDateRangeBefore($each, $dateRange);
            }
            return $query;
        }
        if ($columns === CampaignSortByAllowEnum::TOTAL_SALES_BEFORE_RANGE) {
            return $query->selectRaw('sum(order.total_amount) as ' . $columns);
        }

        $query->filterDateRangeWithSameDiffInPass($dateRange);
        return $query;
    }

    public function scopeCountActiveSellers($query)
    {
        $query->selectRaw('count(DISTINCT(`seller_id`)) as active_sellers');
        $query->whereIn('type', [
            OrderTypeEnum::CUSTOM,
            OrderTypeEnum::REGULAR,
        ]);

        return $query;
    }

    public function scopeGetLastPaidOrder($q, $startTime, $endTime)
    {
        return $q->select('id', 'seller_id', 'paid_at')
            //most recent orders
            ->where(function ($q) {
                $q->where('paid_at', function ($q) {
                    $q->selectRaw('MAX(t2.paid_at)')
                        ->from('order as t2')
                        ->whereColumn('t2.seller_id', 'order.seller_id');
                });
            })
            ->whereBetween('paid_at', [$endTime, $startTime])
            ->groupBy('seller_id')
            ->orderBy('paid_at', 'desc');
    }

    /**
     * @param $query
     *
     * @return Builder
     */
    public function scopePlatformOrder($query): Builder
    {
        return $query->whereIn('type', self::PLATFORM_ORDER_TYPES);
    }

    /**
     * @param $query
     * @param string $accessToken
     * @param bool $withTrashed
     * @return Builder
     */
    public function scopeFindByAccessToken($query, string $accessToken, bool $withTrashed = false): Builder
    {
        return $query->where('access_token', $accessToken)
            ->when($withTrashed, function ($q) {
                $q->withTrashed();
            });
    }

    public function getEstimateDeliveryDates(): array
    {
        try {
            $estimatedDelivery = $this->getShippingTime();
            $printingDay = $this->getPrintingTime();
            $printingDate = Carbon::parse($this->paid_at)->addDays($printingDay);
            $printDate = $printingDate;
            $fromDate = $printingDate->copy()->addDays($estimatedDelivery[0]);
            $toDate = $printingDate->copy()->addDays($estimatedDelivery[1]);
            return [
                'print_date' => $printDate,
                'from_date' => $fromDate,
                'to_date' => $toDate
            ];
        } catch (\Throwable $e) {
            logException($e);
        }
        return [
            'print_date' => '',
            'from_date' => '',
            'to_date' => ''
        ];
    }

    public function checkDiscountAmountBundleDiscount($orderProduct): bool
    {
        $opPromotionRule = $orderProduct->promotionRule;

        if (isset($opPromotionRule) && ($opPromotionRule->type == PromotionTypeEnum::BUNDLE_DISCOUNT)) {
            return false;
        }

        return true;
    }

    public function scopeUpdateOrderCondition($query, array $whereCondition = [])
    {
        return $query
            ->when(!empty($whereCondition), function ($q) use ($whereCondition) {
                $q->where($whereCondition);
            })
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT
            ])
            ->with(['products', 'products.product']);
    }

    public function withProducts($region = null, $withRelations = [])
    {
        $orderProducts = RegionOrderProducts::onRegion($region ?? $this->region ?? config('app.region') ?? config('app.region_master'))
            ->when(!empty($withRelations), function ($q) use ($withRelations) {
                $q->with($withRelations);
            })
            ->where('order_id', $this->id)
            ->get();
        $this->setRelation('products', $orderProducts);
    }
}
