<?php

namespace Modules\OrderService\Helpers;

class RegionDbConnectionManager
{
    /**
     * @param $regionName
     * @return string
     */
    public static function getConnectionNameFromRegion($regionName): string
    {
        if (!$regionName) {
            return env('DB_REGION_SG_CONNECTION', 'mysql_sg');
        }
        $connectionName = config("region.regions.{$regionName}.database_connection");
        if (!$connectionName) {
            return env('DB_REGION_SG_CONNECTION', 'mysql_sg');
        }
        return $connectionName;
    }
}
