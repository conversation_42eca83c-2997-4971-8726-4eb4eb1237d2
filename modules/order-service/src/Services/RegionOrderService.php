<?php

namespace Modules\OrderService\Services;

use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use Illuminate\Support\Arr;
use Modules\OrderService\Models\RegionOrderHistory;
use Modules\OrderService\Models\RegionOrderProducts;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Traits\HasRegionConnection;

class RegionOrderService
{
    use HasRegionConnection;

    /**
     * @param $regionAccessToken
     * @param $region
     * @return array
     */
    public static function regionOrderModelInstance($regionAccessToken = null, $region = null): array
    {
        $masterRegion = config('app.region_master');
        try {
            $region = empty($region) || $region === 'null' ? config('app.region') : $region;
            if ($region === $masterRegion) {
                $orderQuery = Order::onRegion($region);
                $orderProductQuery = OrderProduct::onRegion($region);
                $orderHistoryQuery = OrderHistory::onRegion($region);
                return [
                    'order' => $orderQuery,
                    'order_products' => $orderProductQuery,
                    'order_history' => $orderHistoryQuery,
                    'region' => $region,
                    'case' => 1
                ];
            }
            if ($regionAccessToken) {
                $checkOrder = RegionOrders::onRegion($region)->whereAccessToken($regionAccessToken)->exists();
                if ($checkOrder) {
                    $orderQuery = RegionOrders::onRegion($region);
                    $orderProductQuery = RegionOrderProducts::onRegion($region);
                    $orderHistoryQuery = RegionOrderHistory::onRegion($region);
                    return [
                        'order' => $orderQuery,
                        'order_products' => $orderProductQuery,
                        'order_history' => $orderHistoryQuery,
                        'region' => $region,
                        'case' => 2
                    ];
                }
                $orderQuery = Order::onRegion($masterRegion);
                $orderProductQuery = OrderProduct::onRegion($masterRegion);
                $orderHistoryQuery = OrderHistory::onRegion($masterRegion);
                return [
                    'order' => $orderQuery,
                    'order_products' => $orderProductQuery,
                    'order_history' => $orderHistoryQuery,
                    'region' => $region,
                    'case' => 3
                ];
            }
            $orderQuery = RegionOrders::onRegion($region);
            $orderProductQuery = RegionOrderProducts::onRegion($region);
            $orderHistoryQuery = RegionOrderHistory::onRegion($region);
            return [
                'order' => $orderQuery,
                'order_products' => $orderProductQuery,
                'order_history' => $orderHistoryQuery,
                'region' => $region,
                'case' => 4
            ];
        } catch (\Throwable $e) {
            $orderQuery = Order::onRegion($masterRegion);
            $orderProductQuery = OrderProduct::onRegion($masterRegion);
            $orderHistoryQuery = OrderHistory::onRegion($masterRegion);
            return [
                'order' => $orderQuery,
                'order_products' => $orderProductQuery,
                'order_history' => $orderHistoryQuery,
                'region' => $region,
                'case' => 5
            ];
        }
    }

    /**
     * @param null $token
     * @param null $region
     * @return string
     */
    public static function getAppRegion($token = null, $region = null): string
    {
        if ($region) {
            return $region;
        }
        if ($token) {
            $orderMaster = Order::query()->select(['id', 'region'])->whereAccessToken($token)->first();
            if ($orderMaster && $orderMaster->region) {
                return $orderMaster->region;
            }
        }
        return config('app.region') ?? config('app.region_master');
    }

    /**
     * @param $storeInfo
     * @return void
     */
    public static function forceDistributedCheckout(&$storeInfo): void
    {
        try {
            $regionsAvailable = ['eu'];
            $currentRegion = strtolower(config('app.region'));
            if (in_array($currentRegion, $regionsAvailable)) {
                $storeInfo->enable_distributed_checkout = 1;
            }
        } catch (\Exception $e) {
            logToDiscord('Error in forceDistributedCheckout: ' . $e->getMessage());
        }
    }

    /**
     * @param $region
     * @return int|string
     */
    public static function getRegionId($region)
    {
        try {
            $configs = config('region.regions');
            $regionsAvailable = array_keys($configs);
            if (empty($regionsAvailable) || !in_array($region, $regionsAvailable, true)) {
                return '1';
            }
            $regionConfig = Arr::get($configs, $region);
            return (int)$regionConfig['id'];
        } catch (\Exception $e) {
            logToDiscord('Error in region order getRegionId: ' . $e->getMessage());
            return '1';
        }
    }

    /**
     * @param $order
     * @return bool
     * @throws \Exception
     */
    public static function needSyncToRegion($order)
    {
        try {
            $region = $order->region;
            $currentRegion = config('app.region');
            if (empty($region) && $currentRegion !== config('app.region_master')) {
                $conditionQuery = ['access_token' => $order->access_token];
                $regionOrderExist = RegionOrders::onRegion($currentRegion)->where($conditionQuery)->first();

                if (!$regionOrderExist) {
                    return true;
                }
            }
        } catch (\Exception $e) {
            logToDiscord('Error in needSyncToRegion: ' . $e->getMessage());
        }
        return false;
    }
}
