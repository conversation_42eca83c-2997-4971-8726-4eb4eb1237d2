<?php

namespace Modules\OrderService\Jobs\DatabaseSync;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\OrderService\Data\CreateRegionOrderProductData;
use Modules\OrderService\Models\RegionOrderProducts;
use Modules\OrderService\Models\RegionOrders;


/**
 * Class SyncOrderToRegion
 *
 * This job will sync region order and their relationship to master database
 */
class SyncOrderToRegion implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public function __construct(public Order $orderMaster, public string $region)
    {
    }

    private function shouldSync(): bool
    {
        /**
         * We only sync if order in master is not have any region and it is not existed on region db
         */
        $regionOrder = RegionOrders::onRegion($this->region)->where('access_token', $this->orderMaster->access_token)->first();
        if ($regionOrder) {
            return false;
        }
        return true;
    }

    public function handle()
    {
        try {
            /**
             * ----------------------------------
             * PREPARE DATA
             * ----------------------------------
             */
            $startTime = round(microtime(true) * 1000);
            $orderMaster = $this->orderMaster;
            $orderMasterRegion = $orderMaster->region;
            $accessToken = $orderMaster->access_token;
            if (!$orderMaster) {
                graylogError('Order master is not found', [
                    'category' => 'sync_order_to_region_fatal_error',
                    'access_token' => $accessToken,
                    'region_master' => $orderMasterRegion,
                    'current_region' => $this->region
                ]);
                return;
            }

            if (!empty($orderMasterRegion)) {
                graylogError('Order region in master is not null', [
                    'category' => 'sync_order_to_region_fatal_error',
                    'access_token' => $accessToken,
                    'region_master' => $orderMasterRegion,
                    'current_region' => $this->region
                ]);
                return;
            }

            if (!$this->shouldSync()) {
                graylogError('Sync Order To Region Error', [
                    'category' => 'sync_order_to_region_fatal_error',
                    'access_token' => $accessToken,
                    'region_master' => $orderMasterRegion,
                    'current_region' => $this->region
                ]);
                return;
            }
            $now = now();
            $regionOrder = RegionOrders::onRegion($this->region)->create([
                'access_token' => $accessToken,
                'order_number' => $orderMaster->getOrderNumber(),
                'region' => $this->region,
            ]);


            /**
             * ----------------------------------
             * SYNC ORDER DATA TO REGION
             * ----------------------------------
             */

            /**
             * 1. Sync order info
             */
            $regionOrder->setAdCampaign($orderMaster->getAdCampaign());
            $regionOrder->setAdId($orderMaster->getAdId());
            $regionOrder->setAdMedium($orderMaster->getAdMedium());
            $regionOrder->setAdSource($orderMaster->getAdSource());
            $regionOrder->setCompanyId($orderMaster->getCompanyId());
            $regionOrder->setCountry($orderMaster->getCountry());
            $regionOrder->setCurrencyCode($orderMaster->getCurrencyCode());
            $regionOrder->setDevice($orderMaster->getDevice());
            $regionOrder->setDeviceDetail($orderMaster->getDeviceDetail());
            $regionOrder->setDeviceId($orderMaster->getDeviceId());
            $regionOrder->setFulfillStatus($orderMaster->getFulfillStatus());
            $regionOrder->setIpAddress($orderMaster->getIpAddress());
            $regionOrder->setIpLocation($orderMaster->getIpLocation());
            $regionOrder->setPromotionRule($orderMaster->getPromotionRule());
            $regionOrder->setPromotionRuleId($orderMaster->getPromotionRuleId());
            $regionOrder->setRefId($orderMaster->getRefId());
            $regionOrder->setSessionId($orderMaster->getSessionId());
            $regionOrder->setShippingMethod($orderMaster->getShippingMethod());
            $regionOrder->setStoreDomain($orderMaster->getStoreDomain());
            $regionOrder->setStoreId($orderMaster->getStoreId());
            $regionOrder->setSellerId($orderMaster->getSellerId());
            $regionOrder->setStoreName($orderMaster->getStoreName());
            $regionOrder->setTotalAmount($orderMaster->getTotalAmount());
            $regionOrder->setTotalDiscount($orderMaster->getTotalDiscount());
            $regionOrder->setDiscountCode($orderMaster->getDiscountCode());
            $regionOrder->setTotalPaid($orderMaster->getTotalPaid());
            $regionOrder->setRegion($this->region);
            $regionOrder->setOrderNumber($orderMaster->getOrderNumber());
            $regionOrder->setTransactionId($orderMaster->getTransactionId());
            $regionOrder->setPaymentGatewayId($orderMaster->getPaymentGatewayId());
            $regionOrder->setPaymentMethod($orderMaster->getPaymentMethod());
            $regionOrder->setPaymentStatus($orderMaster->getPaymentStatus());
            $regionOrder->setPaymentFee($orderMaster->getPaymentFee());
            $regionOrder->setProcessingFee($orderMaster->getProcessingFee());
            $regionOrder->setInsuranceFee($orderMaster->getRawInsuranceFee());
            $regionOrder->setTotalFulfillFee($orderMaster->getTotalFulfillFee());
            $regionOrder->setTotalQuantity($orderMaster->getTotalQuantity());
            $regionOrder->setTotalProductAmount($orderMaster->getTotalProductAmount());
            $regionOrder->setTotalShippingAmount($orderMaster->getTotalShippingAmount());
            $regionOrder->setTipAmount($orderMaster->getTipAmount());
            $regionOrder->setTotalTaxAmount($orderMaster->getTotalTaxAmount());
            $regionOrder->setTotalRefund($orderMaster->getTotalRefund());
            $regionOrder->setTotalProductCost($orderMaster->getTotalProductCost());
            $regionOrder->setTotalProductBaseCost($orderMaster->getTotalProductBaseCost());
            $regionOrder->setTotalShippingCost($orderMaster->getTotalShippingCost());
            $regionOrder->setTotalSellerProfit($orderMaster->getTotalSellerProfit());
            $regionOrder->setTotalProfit($orderMaster->getTotalProfit());
            $regionOrder->setBillingAddress($orderMaster->getBillingAddress());
            $regionOrder->setShippingAddress($orderMaster->getShippingAddress());
            $regionOrder->setProcessingFeePaid($orderMaster->getProcessingFeePaid());
            $regionOrder->setFulfillFeePaid($orderMaster->getFulfillFeePaid());
            $regionOrder->setRemarketingStatus($orderMaster->getRemarketingStatus());
            $regionOrder->setStatus($orderMaster->getStatus());
            $regionOrder->setStatusUrl($orderMaster->getStatusUrl());
            $regionOrder->setPaymentLog($orderMaster->getPaymentLog());
            $regionOrder->setFraudStatus($orderMaster->getFraudStatus());
            $regionOrder->setStatsStatus($orderMaster->getStatsStatus());
            $regionOrder->setType($orderMaster->getType());
            $regionOrder->setAddressVerified($orderMaster->getAddressVerified());
            $regionOrder->setOrderNote($orderMaster->getOrderNote());
            $regionOrder->setCreatedAt($orderMaster->getCreatedAt());
            $regionOrder->setUpdatedAt($orderMaster->getUpdatedAt());
            $regionOrder->setFulfilledAt($orderMaster->getFulfilledAt());
            $regionOrder->setPaidAt($orderMaster->getPaidAt());
            $regionOrder->setDeliveredAt($orderMaster->getDeliveredAt());
            $regionOrder->setReceivedAt($orderMaster->getReceivedAt());
            $regionOrder->setCurrencyRate($orderMaster->getCurrencyRate());
            $regionOrder->setFulfillLog($orderMaster->getFulfillLog());
            $regionOrder->setFlagLog($orderMaster->getFlagLog());
            $regionOrder->setSenFulfillStatus($orderMaster->getSenFulfillStatus());
            $regionOrder->setDatestamp($orderMaster->getDatestamp());
            $regionOrder->setOrderNumber($orderMaster->getOrderNumber());
            $regionOrder->setOrderNumber2($orderMaster->getOrderNumber2());
            $regionOrder->setPersonalized($orderMaster->getPersonalized());
            $regionOrder->setLast4CardNumber($orderMaster->getLast4CardNumber());
            $regionOrder->setVisitInfo($orderMaster->getVisitInfo());
            $regionOrder->setAddress($orderMaster->getAddress());
            $regionOrder->setAddress2($orderMaster->getAddress2());
            $regionOrder->setCity($orderMaster->getCity());
            $regionOrder->setState($orderMaster->getState());
            $regionOrder->setPostcode($orderMaster->getPostcode());
            $regionOrder->setCustomerEmail($orderMaster->getCustomerEmail());
            $regionOrder->setCustomerName($orderMaster->getCustomerName());
            $regionOrder->setCustomerPhone($orderMaster->getCustomerPhone());
            $regionOrder->setHouseNumber($orderMaster->getHouseNumber());
            $regionOrder->setMailboxNumber($orderMaster->getMailboxNumber());
            $regionOrder->setEstimateDeliveryDate($orderMaster->getEstimateDeliveryDate());
            $regionOrder->setIossNumber($orderMaster->getIossNumber());
            $regionOrder->setIsCornerPlacement($orderMaster->getIsCornerPlacement());
            $regionOrder->setApprovedAt($orderMaster->getApprovedAt());
            $regionOrder->setUpdatedAt($now);
            $regionOrder->save();
            /**
             * 2. Sync order products
             *
             * But we only sync order products if order is not paid
             */
            foreach ($orderMaster->products as $orderProductMaster) {
                $orderProductMaster->order_id = $regionOrder->getId();
                $data = CreateRegionOrderProductData::from($orderProductMaster->toArray());
                $newOrderProductData = $data->toArray();
                unset($newOrderProductData['id'], $newOrderProductData['additional_attributes']);
                RegionOrderProducts::onRegion($this->region)->where('order_id', $regionOrder->getId())->delete();
                RegionOrderProducts::onRegion($this->region)->create($newOrderProductData);
            }

            /**
             * Do not need to sync order history
             */

            /**
             * 3. We mark region order as synced
             */
            $regionOrder->setSyncAt($now);
            $regionOrder->setUpdatedAt($now);
            $regionOrder->saveQuietly();

            /**
             * 4. We mark order as region order
             */
            $orderMaster->setRegion($this->region);
            $orderMaster->save();
            $endTime = round(microtime(true) * 1000);
            $time = $endTime - $startTime;
            graylogInfo('Synced region to region order done: ' . $regionOrder->getAccessToken() . ' in ' . $time . ' ms', [
                'category' => 'sync_order_to_region_job',
                'region_order' => $regionOrder,
                'access_token' => $accessToken,
            ]);
            return;
        } catch (\Exception $e) {
            logToDiscord('Sync Order To Region Job Error: ' . $e->getMessage(), 'error', true);
            graylogError('Sync Order To Region Job Error', [
                'category' => 'sync_order_to_region_job_fatal_error',
                'data' => $e
            ]);
            return;
        }
    }

    /**
     * @param $message
     * @param string $fieldName
     * @return bool
     */
    private function validateDuplicateFieldData($message, string $fieldName = 'PRIMARY'): bool
    {
        $pattern = "/Duplicate entry '([^']+)' for key '$fieldName'/";
        if (preg_match($pattern, $message, $matches)) {
            return true;
        }
        return false;
    }
}
