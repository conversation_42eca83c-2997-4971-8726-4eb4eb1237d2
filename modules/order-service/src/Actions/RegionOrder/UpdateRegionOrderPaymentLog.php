<?php
namespace Modules\OrderService\Actions\RegionOrder;

use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Services\StoreService;
use Illuminate\Http\Request;
use LogicException;
use Modules\OrderService\Models\RegionOrderHistory;
use Modules\OrderService\Services\RegionOrderService;
use UnexpectedValueException;
use App\Models\Order;
class UpdateRegionOrderPaymentLog
{
    private $regionName;

    public function handle(Request $request) {
        try {
            $this->regionName = config('app.region');
            $storeInfo = StoreService::getCurrentStoreInfo();
            if (!$storeInfo) {
                throw new LogicException("Region: " . $this->regionName . " / Store is not found");
            }
            $orderToken = $request->post('token');
            $message = $request->post('message');
            $orderQuery = RegionOrderService::regionOrderModelInstance($orderToken, $this->regionName)['order'];
            $regionOrder = $orderQuery->firstWhere([
                'store_id' => $storeInfo->id,
                'access_token' => $orderToken
            ]);
            if (is_null($regionOrder)) {
                throw new UnexpectedValueException("Region: " . $this->regionName . " / Order not found.");
            }

            if (empty($message)) {
                return true;
            }

            if (is_array($message)) {
                $message = implodeWithKeys($message);
            }

            if (str_starts_with($message, 'Log::')) {
                graylogInfo($message, [
                    'category' => 'payment_exception',
                    'order_token' => $orderToken,
                    'order_id' => $regionOrder->id,
                ]);
                logToDiscord($message . " / Region: " . $this->regionName . " / Order Id: " . $regionOrder->id, 'error_checkout');
                return true;
            }

            if ($regionOrder->isPaidAfter(24)) {
                return true;
            }
            if ($regionOrder->getModel() instanceof Order) {
                $regionOrder = $regionOrder->setConnection(env('DB_REGION_SG_CONNECTION', 'mysql_sg'));
            }
            $regionOrder->update(['payment_log' => $message]);
            RegionOrderHistory::insertLog(
                $regionOrder,
                OrderHistoryActionEnum::PAYMENT_FAILED,
                $message,
                OrderHistoryDisplayLevelEnum::CUSTOMER,
                $this->regionName
            );
            return true;
        } catch (\Throwable $e) {
            throw new LogicException("Region: " . $this->regionName . " / " . $e->getMessage());
        }
    }
}
