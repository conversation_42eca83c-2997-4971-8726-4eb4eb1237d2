<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store_domains', function (Blueprint $table) {
            $table->string('sender_name')->nullable()->after('status');
            $table->string('sender_email')->nullable()->after('sender_name');
            $table->boolean('sender_verified')->default(0)->after('sender_email');
            $table->dateTime('sender_next_verify_at')->nullable()->after('sender_verified')
                ->comment('Next time to verify DNS records');
            $table->json('sender_dns_records')->nullable()->after('sender_next_verify_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('store_domains', function (Blueprint $table) {
            //
        });
    }
};
