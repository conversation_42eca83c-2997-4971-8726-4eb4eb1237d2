<?php

namespace App\Exports\Supplier;

use App\Enums\OrderProductFulfillStatus;
use App\Models\OrderProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class ProductsExport implements FromArray, WithHeadings, WithColumnFormatting, ShouldAutoSize
{
    protected collection $products;
    protected bool $isMarkProcessing;
    protected array $fieldOptions = [];
    protected array $fieldPrintSpaces = [];

    public function __construct($products, $isMarkProcessing = false)
    {
        $this->products = $products;
        $this->isMarkProcessing = $isMarkProcessing;
    }

    public function columnFormats(): array
    {
        return [
            'B' => '@',
            'S' => '@',
            'T' => '@',
        ];
    }

    public function array(): array
    {
        $arr = [];
        /**
         * @var OrderProduct $orderProduct
         */
        foreach ($this->products as $orderProduct) {
            $each = [
                $orderProduct->order->id,
                $orderProduct->order->order_number,
                '\'' . $orderProduct->id,
                $orderProduct->product_name,
            ];
            // insert fields options, matching with fields
            foreach ($this->fieldOptions as $key) {
                $each[] = $orderProduct->options[strtolower($key)] ?? '';
            }

            // insert fields files, matching with fields
            foreach ($this->fieldPrintSpaces as $printSpace) {
                $file   = $orderProduct->files->firstWhere('print_space', $printSpace);
                $each[] = optional($file)->mockup_url ?? '';
                $each[] = optional($file)->design_url ?? '';
            }
            $sku = $orderProduct->sku ?? '';
            $trackingCode = $orderProduct->tracking_code ?? '';
            $shippingLabelUrl = $orderProduct->order->shipping_label ? s3Url($orderProduct->order->shipping_label) : '';
            $barcodeUrl = $orderProduct->barcode ? s3Url($orderProduct->barcode) : '';

            $fulfillStatus = $orderProduct->fulfill_status;
            if ($this->isMarkProcessing && $fulfillStatus === OrderProductFulfillStatus::UNFULFILLED) {
                $fulfillStatus = OrderProductFulfillStatus::PROCESSING;
            }

            $customOptions = '';
            foreach ($orderProduct->custom_options as $group) {
                if (!is_array($group)) {
                    $customOptions .= $group . PHP_EOL;
                    continue;
                }
                foreach ($group as $op) {
                    $value = $op['value'];
                    if (isset($op['type'], $value['imagePath']) && $op['type'] === 'image' && (!is_string($value) || empty($value))) {
                        $value = $value['imagePath'];
                    }
                    if (is_array($value)) {
                        $customOptions .= $op['label'] . ': ' . json_encode($value, JSON_THROW_ON_ERROR) . PHP_EOL;
                    } else {
                        $customOptions .= $op['label'] . ': ' . $value . PHP_EOL;
                    }
                }
            }

            array_push(
                $each,
                $orderProduct->quantity,
                $fulfillStatus,
                $orderProduct->order->customer_name,
                $orderProduct->order->customer_email,
                $orderProduct->order->customer_phone,
                $orderProduct->order->address,
                $orderProduct->order->address_2,
                $orderProduct->order->city,
                $orderProduct->order->state,
                $orderProduct->order->postcode,
                $orderProduct->order->country,
                $shippingLabelUrl,
                $barcodeUrl,
                is_numeric($sku) ? '\'' . $sku : $sku,
                is_numeric($trackingCode) ? '\'' . $trackingCode : $trackingCode,
                $orderProduct->order->shipping_method,
                $orderProduct->shipping_carrier,
                $orderProduct->tracking_url,
                empty($orderProduct->supplier_exported_at) ? '' : Carbon::parse($orderProduct->supplier_exported_at)->timezone('Asia/Ho_Chi_Minh'),
                $customOptions
            );
            $arr[] = $each;
        }
        return $arr;
    }

    /**
     * @return string[]
     * @throws \JsonException
     */
    public function headings(): array
    {
        // first fields
        $arr = [
            'Order ID',
            'Order Number',
            'Order Product ID',
            'Product Name',
        ];
        // add fields
        foreach ($this->products as $orderProduct) {
            $options        = json_decode($orderProduct->options, true, 512, JSON_THROW_ON_ERROR);
            $custom_options = json_decode($orderProduct->custom_options, true, 512, JSON_THROW_ON_ERROR);
            $orderProduct->options = $options;
            $orderProduct->custom_options = $custom_options;
            // get all field options available
            $optionsKeys = array_keys($options);
            foreach ($optionsKeys as $key) {
                if (!in_array($key, $this->fieldOptions, true)) {
                    $this->fieldOptions[$key] = Str::ucfirst($key);
                }
            }

            foreach ($orderProduct->files as $file) {
                $printSpace = $file->print_space;
                if (
                    $printSpace !== 'front'
                    &&
                    $printSpace !== 'back'
                    &&
                    $orderProduct->files->count() === 1
                ) {
                    $printSpace = 'default';
                }

                if (!in_array($printSpace, $this->fieldPrintSpaces, true)) {
                    $this->fieldPrintSpaces[$printSpace] = $printSpace;
                }
            }
        }
        foreach ($this->fieldOptions as $options) {
            $arr[] = $options;
        }
        foreach ($this->fieldPrintSpaces as $printSpace) {
            $arr[] = 'Mockup_' . $printSpace;
            $arr[] = 'Artwork_' . $printSpace;
        }

        // last fields
        array_push(
            $arr,
            'Quantity',
            'Fulfill Status',
            'Customer Name',
            'Customer Email',
            'Customer Phone',
            'Address',
            'Address 2',
            'City',
            'State',
            'Postcode',
            'Country',
            'Shipping Label',
            'Barcode',
            'SKU',
            'Shipping Method',
            'Shipping Carrier',
            'Tracking Code',
            'Tracking URL',
            'Processing At',
            'Custom Options',
        );

        return $arr;
    }
}
