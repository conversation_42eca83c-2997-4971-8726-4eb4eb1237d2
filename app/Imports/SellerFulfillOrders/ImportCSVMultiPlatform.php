<?php

namespace App\Imports\SellerFulfillOrders;

use App\Actions\Commons\ImgDirectLinkAction;
use App\Enums\AddressEnum;
use App\Enums\DesignByEnum;
use App\Enums\FbaFulfillBy;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ShippingMethodEnum;
use App\Http\Controllers\UploadController;
use App\Jobs\CheckOrderAtRisk;
use App\Jobs\ValidateSellerFulfillOrder;
use App\Models\File;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\SystemConfig;
use App\Models\Template;
use App\Models\User;
use App\Services\MediaService;
use App\Services\OrderService;
use ErrorException;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

// No format header row
HeadingRowFormatter::default('none');

class ImportCSVMultiPlatform implements ToCollection, WithHeadingRow
{
    public function __construct($skipValidateAddress)
    {
        $this->skipValidateAddress = $skipValidateAddress;
    }
    private function parseBeforeMapping(&$row): void
    {
        $row = $row->toArray();
        $row = array_map('trim', $row);
        $row = array_change_key_case($row);
    }

    /**
     * @param string $find
     * @param $row
     * @return array|string
     */
    private function findFieldMapping(string $find, $row)
    {
        $fieldsMapping = [
            'order_number' => ['order_number', 'order number', 'id', 'order_id', 'order id'],
            'store_domain' => ['store_domain', 'shop domain'],
            'store_name' => ['store_name'],
            'order_note' => ['order_note', 'notes'],
            'customer_name' => ['customer_name', 'first name (shipping)', 'last name (shipping)', 'shipping name', 'ship name'],
            'customer_email' => ['customer_email', 'email (billing)', 'email'],
            'customer_phone' => ['customer_phone', 'phone (billing)', 'shipping_phone'],
            'address' => ['address', 'address 1&2 (shipping)', 'shipping address1', 'ship address1'],
            'address_2' => ['address_2', 'address2', 'shipping address2', 'ship address2'],
            'city' => ['city', 'city (shipping)', 'shipping city', 'ship city'],
            'state' => ['state', 'state code (shipping)', 'shipping province', 'ship state'],
            'postcode' => ['postcode', 'postcode (shipping)', 'shipping zip', 'ship zipcode'],
            'country' => ['country_code', 'country code (shipping)', 'shipping country', 'ship country'],
            'product_sku' => ['product_sku', 'sp_ff_sku', 'line item sku'],
            'product_name' => ['product_name', 'item name', 'lineitem name', 'line item name'],
            'quantity' => ['quantity', 'lineitem quantity', 'line item quantity'],
            'shipping_method' => ['shipping_method'],
            'shipping_label' => ['shipping_label', 'shipping label'],
            'barcode' => ['barcode'],
            'platform' => ['platform'],
            'ioss_number' => ['ioss_number', 'ioss number'],
            'sen_design' => ['sen_design', 'senprints design', 'sen design'],
        ];
        $data = '';
        if ($find === 'options') {
            $data = [];
            if (isset($row['variations'])) {
                $variations = $row['variations'];
                $breakDown = explode(',', $variations);
                foreach ($breakDown as $option) {
                    $optionSplit = explode(':', $option);
                    $key = strtolower(trim($optionSplit[0]));
                    $validKey = '';
                    $value = strtolower($optionSplit[1]);
                    switch ($key) {
                        case 'colour':
                            $validKey = 'color';
                            break;
                        case 'styles &amp; sizes':
                            $validKey = 'size';
                            $valueBreakDown = explode('-', $value);
                            $value = strtolower(trim($valueBreakDown[1]));
                            break;
                    }
                    $data[$validKey] = $value;
                }
            } else {
                if (isset($row['line item size'])) {
                    $data['size'] = $row['line item size'];
                }
                $optionsFilter = array_filter($row, function ($value, $key) {
                    return str_starts_with($key, 'option:') && !empty($value);
                }, ARRAY_FILTER_USE_BOTH);
                if (count($optionsFilter) > 0) {
                    foreach ($optionsFilter as $key => $value) {
                        $keyExplode = explode(':', $key);
                        $validKey = strtolower(trim($keyExplode[1]));
                        $data[$validKey] = strtolower($value);
                    }
                }
            }
        } else if (isset($row['first name (shipping)'], $row['last name (shipping)']) && $find === 'customer_name') {
            $data = $row['first name (shipping)'] . ' ' . $row['last name (shipping)'];
        } else if ($find === 'mockups') {
            $data = array_filter($row, function ($value, $key) {
                return str_starts_with($key, 'mockup:') && !empty($value);
            }, ARRAY_FILTER_USE_BOTH);
        } else if ($find === 'artworks') {
            $data = array_filter($row, function ($value, $key) {
                return str_starts_with($key, 'artwork:') && !empty($value);
            }, ARRAY_FILTER_USE_BOTH);

            if (empty($data)) {
                $artworks = array_values(array_filter($row, function ($value, $key) {
                    return str_starts_with($key, 'artwork') && !empty($value);
                }, ARRAY_FILTER_USE_BOTH));
                $data = [];
                if (count($artworks) === 1) {
                    $data['artwork:default'] = $artworks[0];
                } else if (count($artworks) === 2) {
                    $data['artwork:front'] = $artworks[0];
                    $data['artwork:back'] = $artworks[1];
                }
            }
        }  else if ($find === 'custom_options') {
            $data = array_filter($row, function ($value, $key) {
                return str_starts_with($key, 'custom_options:');
            }, ARRAY_FILTER_USE_BOTH);
        } else {
            $fields = $fieldsMapping[$find] ?? null;
            if (empty($fields)) {
                return $data;
            }
            foreach ($fields as $field) {
                $field = strtolower($field);
                if (!isset($row[$field])) {
                    continue;
                }

                $data = $row[$field];
                break;
            }
        }

        return $data;
    }

    /**
     * @throws ErrorException
     * @throws Exception
     */
    public function collection(Collection $collection): void
    {
        $currentUserId = currentUser()->getUserId();
        $seller = User::query()->whereKey($currentUserId)->first();
        $refId = $seller?->refId;
        $messages = [];
        $orderExist = [];
        $importedOrders = [];
        $productTemplates = [];
        $isEmbroidery = false;

        DB::beginTransaction();
        try {
            $hasAmazonProduct = $collection->filter(function ($row) {
                $fulfillFbaBy = $this->findFieldMapping('platform', $row);
                $shippingLabel = $this->findFieldMapping('shipping_label', $row);
                return !empty($fulfillFbaBy) && !empty($shippingLabel) && $fulfillFbaBy === FbaFulfillBy::BY_AMAZON;
            })->isNotEmpty();
            if ($hasAmazonProduct && $collection->count() > 1) {
                DB::commit();
                throw new ErrorException('Please only add one product with fulfill by Amazon', 403);
            }
            foreach ($collection as $rowIndex => $row) {
                if (!$row->filter()->isNotEmpty()) {
                    continue;
                }

                $this->parseBeforeMapping($row);

                $orderNumber = $this->findFieldMapping('order_number', $row);

                $rowCount = ((int)$rowIndex + 2);
                $invalid = false;

                if (empty($orderNumber)) {
                    $messages[] = 'Row #' . $rowCount . ': Order #' . $orderNumber . ' is invalid';
                    $invalid = true;
                }

                if (isset($orderExist[$orderNumber])) {
                    if ($orderExist[$orderNumber]) {
                        $messages[] = 'Row #' . $rowCount . ': Order #' . $orderNumber . ' is duplicated';
                        $invalid = true;
                    }
                } else {
                    $orderQuery = Order::query()->where([
                        'seller_id' => $currentUserId,
                        'order_number_2' => $orderNumber,
                    ]);

                    $order = $orderQuery->first();

                    $orderExist[$orderNumber] = false;
                    if ($order) {
                        if (in_array($order->status, [OrderStatus::DRAFT, OrderStatus::CANCELLED], true)) {
                            File::query()->where('order_id', $order->id)->delete();
                            OrderProduct::query()->where('order_id', $order->id)->delete();
                            Order::query()->whereKey($order->id)->delete();
                        } else {
                            $messages[] = 'Row #' . $rowCount . ': Order #' . $orderNumber . ' is duplicated';
                            $orderExist[$orderNumber] = true;
                            $invalid = true;
                        }
                    }
                }

                $fulfillFbaBy = $this->findFieldMapping('platform', $row);
                $quantity = $this->findFieldMapping('quantity', $row);
                if (empty($quantity) || $quantity < 1) {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Product quantity is invalid';
                    $invalid = true;
                } else if ($fulfillFbaBy === FbaFulfillBy::BY_AMAZON) {
                    $steps = [30, 60, 90, 120, 150, 180, 210, 240, 270, 300];
                    if (!in_array((int) $quantity, $steps, true)) {
                        $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Please enter quantity again. Ex: 30 60 90 120, ...';
                        $invalid = true;
                    }
                }

                // get template product from sku
                $productSku = $this->findFieldMapping('product_sku', $row);
                $productName = $this->findFieldMapping('product_name', $row);
                if (!empty($productTemplates[$productSku])) {
                    $productTemplate = $productTemplates[$productSku];
                } else {
                    $productTemplate = Template::query()
                        ->whereSku($productSku)
                        ->whereStatus(ProductStatus::ACTIVE)
                        ->first();
                    if ($productTemplate) {
                        if ((int) $productTemplate->full_printed === ProductPrintType::EMBROIDERY && !$isEmbroidery) {
                            if (count($productTemplates) === 0) {
                                $isEmbroidery = true;
                            } else {
                                $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Embroidery product must be in separate file';
                                $invalid = true;
                            }
                        } elseif ((int) $productTemplate->full_printed !== ProductPrintType::EMBROIDERY && $isEmbroidery) {
                            $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Embroidery product must be in separate file';
                            $invalid = true;
                        }
                    }
                    $productTemplates[$productSku] = $productTemplate;
                }

                if (!$productTemplate) {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Product sku ' . $productSku . ' is invalid';
                    continue;
                }

                $customerName = $this->findFieldMapping('customer_name', $row);
                $customerEmail = $this->findFieldMapping('customer_email', $row);
                $customerPhone = $this->findFieldMapping('customer_phone', $row);
                $address = $this->findFieldMapping('address', $row);
                $address2 = $this->findFieldMapping('address_2', $row);
                $city = $this->findFieldMapping('city', $row);
                $state = $this->findFieldMapping('state', $row);
                $postcode = $this->findFieldMapping('postcode', $row);
                $country = $this->findFieldMapping('country', $row);
                $orderNote = $this->findFieldMapping('order_note', $row);
                $storeDomain = $this->findFieldMapping('store_domain', $row);
                $storeName = $this->findFieldMapping('store_name', $row);
                $shippingMethod = $this->findFieldMapping('shipping_method', $row);
                $shippingLabel = $this->findFieldMapping('shipping_label', $row);
                $barcode = $this->findFieldMapping('barcode', $row);
                $iossNumber = $this->findFieldMapping('ioss_number', $row);

                if (empty($fulfillFbaBy)) {
                    $fulfillFbaBy = FbaFulfillBy::BY_POSHMARK;
                }
                $fulfillFbaBy = strtolower($fulfillFbaBy);
                $orderType = OrderTypeEnum::FULFILLMENT;
                $orderPrefix = 'FF-';

                if (empty($customerName) && empty($shippingLabel)) { //skip validate if order is fba
                    $messages[] = 'Customer name required on row #' . $rowCount;
                    $invalid = true;
                }

                if (empty($address) || empty($city) || empty($postcode) || empty($country)) {
                    if (empty($shippingLabel)) {
                        $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Address is invalid';
                        $invalid = true;
                    } else {
                        $address = AddressEnum::ADDRESS;
                        $city = AddressEnum::CITY;
                        $postcode = AddressEnum::POSTCODE;
                        $country = AddressEnum::COUNTRY;
                    }
                }

                if (
                    (!empty($barcode) && empty($shippingLabel)) || //Only fba order has barcode field
                    (!empty($shippingLabel) && !empty($shippingMethod)) // FBA order has not shipping method
                )
                {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Order is not fulfill fba format';
                    $invalid = true;
                }

                //handle fba order
                if (!empty($shippingLabel)) {
                    $orderType = OrderTypeEnum::FBA;
                    $orderPrefix = 'FBA-';
                    if($productTemplate->system_type !== ProductSystemTypeEnum::FULFILL_FBA) {
                        $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Template is not fulfill fba product';
                        $invalid = true;
                    }
                    if (!empty($barcode)) {
                        $fulfillFbaBy = FbaFulfillBy::BY_AMAZON;
                    }
                } elseif($productTemplate->system_type === ProductSystemTypeEnum::FULFILL_FBA) {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Template is not fulfill fba product';
                    $invalid = true;
                }

                if (!empty($importedOrders) && $orderType !== array_values($importedOrders)[0]->type) {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': All orders must be same type';
                    $invalid = true;
                }

                if(empty($shippingMethod) || !in_array($shippingMethod, ShippingMethodEnum::getValues(), true)) {
                    $shippingMethod = ShippingMethodEnum::STANDARD;
                }

                $locationByCode = getLocationByCode($country);
                $isSenPrintsDesign = $this->findFieldMapping('sen_design', $row) === 'yes' || $this->findFieldMapping('sen_design', $row) === 'true';
                if (empty($locationByCode) && empty($shippingLabel)) { //skip validate if order is fba
                    $locationByName = getLocationByName($country);

                    if (!empty($locationByName)) {
                        $country = $locationByName->code;
                    } else {
                        $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Country code is invalid';
                        $invalid = true;
                    }
                }
                $disabledCountry = SystemConfig::getConfig('disable_country', '');
                if (!empty($disabledCountry) && str_contains($disabledCountry, $country)) {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Country code is invalid';
                    $invalid = true;
                }
                // search artworks in file
                $artworks = $this->findFieldMapping('artworks', $row);

                if (!$isSenPrintsDesign && $productTemplate->isNotHandMade() && count($artworks) === 0) {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': No artworks found';
                    $invalid = true;
                }

                $mockups = $this->findFieldMapping('mockups', $row);
                if (!$isSenPrintsDesign && $isEmbroidery) {
                     if(empty($mockups)) {
                         $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': No mockups found';
                         $invalid = true;
                     } else {
                         foreach ($artworks as $key => $value) {
                             $mockupKey = str_replace('artwork', 'mockup', $key);
                             if (!isset($mockups[$mockupKey])) {
                                 $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': No mockup found for ' . $key;
                                 $invalid = true;
                             }
                         }
                     }
                }

                $artworks = array_filter($artworks, function ($design) {
                    return !empty($design);
                });
                $mockups = array_filter($mockups, function ($mockup) {
                    return !empty($mockup);
                });
                if ($isSenPrintsDesign && count($mockups) === 0) {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ' - Product ' . $productTemplate->name . ' require the mockups if need SenPrints clone the design';
                    $invalid = true;
                }

                $productOptions = $this->findFieldMapping('options', $row);

                $optionLogs = '';
                $productOptions = OrderService::correctOptions($productOptions, $productTemplate, $optionLogs);
                if ($productOptions === false) {
                    $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': ' . $optionLogs;
                    $invalid = true;
                }

                $optionLogs = '';
                $productCustomOptions = [];
                if ((int) $productTemplate->full_printed === ProductPrintType::HANDMADE || $productTemplate->personalized === PersonalizedType::CUSTOM_OPTION) {
                    $productCustomOptions = $this->findFieldMapping('custom_options', $row);
                    $productCustomOptions = OrderService::correctCustomOptions($productCustomOptions, $productTemplate, $optionLogs);
                    if ($productCustomOptions === false) {
                        $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': ' . $optionLogs;
                        $invalid = true;
                    }
                }
                $shippingLabelUploaded = [];
                $barcodeUploaded = [];
                if (!empty($shippingLabel)) {
                    $shippingLabel = ImgDirectLinkAction::singleton()->handle($shippingLabel);
                    $maxRetried = 1;
                    while (true) {
                        $shippingLabelUploaded = UploadController::uploadS3FromDirectLink($shippingLabel, 'tmp/');
                        if (!empty($shippingLabelUploaded) || $maxRetried > 5) {
                            break;
                        }
                        $maxRetried++;
                    }
                    if (empty($shippingLabelUploaded)) {
                        $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Shipping label is invalid';
                        $invalid = true;
                    }
                }
                if (!empty($barcode)) {
                    $maxRetried = 1;
                    while (true) {
                        $barcode = ImgDirectLinkAction::singleton()->handle($barcode);
                        $barcodeUploaded = UploadController::uploadS3FromDirectLink($barcode, 'tmp/');
                        if (!empty($barcodeUploaded) || $maxRetried > 5) {
                            break;
                        }
                        $maxRetried++;
                    }
                    if (empty($barcodeUploaded)) {
                        $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Barcode is invalid';
                        $invalid = true;
                    }
                }
                if (!$invalid) {
                    // get order data
                    $orderData = [
                        'seller_id' => $currentUserId,
                        'ref_id' => $refId,
                        'type' => $orderType,
                        'order_number_2' => $orderNumber,
                        'customer_name' => $customerName,
                        'customer_email' => $customerEmail,
                        'customer_phone' => $customerPhone,
                        'address' => $address,
                        'address_2' => $address2,
                        'city' => $city,
                        'state' => $state,
                        'postcode' => $postcode,
                        'country' => $country,
                        'order_note' => $orderNote,
                        'store_domain' => $storeDomain,
                        'store_name' => $storeName,
                        'status' => OrderStatus::DRAFT,
                        'fulfill_status' => OrderFulfillStatus::UNFULFILLED,
                        'shipping_method' => $shippingMethod,
                        'address_verified' => empty($shippingLabel) ? OrderAddressVerifiedEnum::UNVERIFIED : OrderAddressVerifiedEnum::VERIFIED,
                        'ioss_number' => $iossNumber,
                    ];

                    // update if order already exists
                    $newOrder = Order::query()->updateOrCreate([
                        'seller_id' => $currentUserId,
                        'order_number_2' => $orderNumber,
                    ], $orderData);
                    $newOrder->order_number = $orderPrefix . $newOrder->id;
                    $shippingLabelPath = null;
                    if (!empty($shippingLabelUploaded['path'])) {
                        $shippingLabelPath = 'o/' . $newOrder->id . '/' . basename($shippingLabelUploaded['path']);
                        $newOrder->shipping_label = saveTempFileAws($shippingLabelUploaded['path'], $shippingLabelPath);
                    }
                    $newOrder->save();
                    $designBy = count($mockups) > 0 && $isSenPrintsDesign ? DesignByEnum::SENPRINTS : DesignByEnum::SELLER;
                    // get order product data
                    $orderProductData = [
                        'order_id' => $newOrder->id,
                        'sku' => $productTemplate->sku, // sku of the template product
                        'template_id' => $productTemplate->id, // id of the template product
                        'seller_id' => $currentUserId,
                        'ref_id' => $refId,
                        'campaign_title' => $productName,
                        'product_name' => $productTemplate->name,
                        'quantity' => $quantity,
                        'full_printed' => $productTemplate->full_printed,
                        'fulfill_fba_by' => $fulfillFbaBy,
                        'campaign_type' => ProductSystemTypeEnum::FULFILL,
                        'design_by' => $designBy,
                    ];

                    $productCustomOptions = self::handleCustomOptions($newOrder, $productCustomOptions);

                    $orderProductData['options'] = json_encode($productOptions, JSON_THROW_ON_ERROR);
                    $orderProductData['custom_options'] = !empty($productCustomOptions) ? json_encode($productCustomOptions, JSON_THROW_ON_ERROR) : null;

                    // each line is unique product
                    $orderProduct = OrderProduct::query()->create($orderProductData);
                    $orderProduct->setRelation('template', $productTemplate);

                    $printSpaceNames = $orderProduct->getPrintSpacesName();
                    $optionSize = $orderProduct->getOptionSize();
                    $defaultPrintSpace = $orderProduct->getDefaultPrintSpace();

                    $barcodePath = null;
                    if (!empty($barcodeUploaded['path'])) {
                        $barcodePath = 'o/' . $newOrder->id . '/' . basename($barcodeUploaded['path']);
                        $orderProduct->barcode = saveTempFileAws($barcodeUploaded['path'], $barcodePath);
                        $orderProduct->save();
                    }
                    $artworkData = [];
                    $mockupData = [];
                    if ((int) $productTemplate->full_printed !== ProductPrintType::HANDMADE) {
                        foreach ($artworks as $key => $artwork) {
                            $keyExplode = explode(':', $key);
                            $printSpace = strtolower(trim($keyExplode[1]));
                            if ($printSpace === PrintSpaceEnum::DEFAULT) {
                                $printSpace = $defaultPrintSpace;
                            }

                            if (!in_array($printSpace, $printSpaceNames, true)) {
                                $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Artwork printspace ' . $printSpace . ' invalid';
                                $invalid = true;
                                continue;
                            }
                            if ($productTemplate->isFullPrintedType() && $optionSize && ($printSpace !== PrintSpaceEnum::DEFAULT) && (!str_contains($optionSize, $printSpace))) {
                                $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Artwork printspace ' . $printSpace . " doesn't match product size " . $optionSize;
                                $invalid = true;
                                continue;
                            }

                            $data = [
                                'order_id' => $newOrder->id,
                                'type' => FileTypeEnum::DESIGN,
                                'file_url' => $artwork,
                                'file_url_2' => null,
                                'order_product_id' => $orderProduct->id,
                                'seller_id' => $currentUserId,
                                'option' => FileRenderType::PRINT,
                                'print_space' => $printSpace,
                                'file_name' => MediaService::getFileName($artwork),
                            ];

                            $artworkData[] = $data;
                        }
                        foreach ($mockups as $key => $mockup) {
                            $keyExplode = explode(':', $key);
                            $printSpace = strtolower(trim($keyExplode[1]));
                            if ($printSpace === PrintSpaceEnum::DEFAULT) {
                                $printSpace = $defaultPrintSpace;
                            }

                            if (!in_array($printSpace, $printSpaceNames, true)) {
                                $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Mockup printspace ' . $printSpace . ' invalid';
                                $invalid = true;
                                continue;
                            }
                            if ($productTemplate->isFullPrintedType() && $optionSize && ($printSpace !== PrintSpaceEnum::DEFAULT) && (!str_contains($optionSize, $printSpace))) {
                                $messages[] = 'Row #' . $rowCount . ' - Order #' . $orderNumber . ': Mockup printspace ' . $printSpace . " doesn't match product size " . $optionSize;
                                $invalid = true;
                                continue;
                            }

                            $data = [
                                'order_id' => $newOrder->id,
                                'type' => FileTypeEnum::IMAGE,
                                'file_url' => $mockup,
                                'file_url_2' => null,
                                'order_product_id' => $orderProduct->id,
                                'seller_id' => $currentUserId,
                                'option' => null,
                                'print_space' => $printSpace,
                                'file_name' => ''
                            ];

                            $mockupData[] = $data;
                        }
                    }
                    $files = array_merge($artworkData, $mockupData);
                    if(!empty($barcode)) {
                        $files[] = [
                            'order_id' => $newOrder->id,
                            'type' => FileTypeEnum::BARCODE,
                            'file_url' => $barcode,
                            'file_url_2' => $barcodePath,
                            'order_product_id' => $orderProduct->id,
                            'seller_id' => $currentUserId,
                            'option' => null,
                            'print_space' => null,
                            'file_name' => ''
                        ];
                    }
                    if (!empty($shippingLabel)) {
                        $files[] = [
                            'order_id' => $newOrder->id,
                            'type' => FileTypeEnum::SHIPPING_LABEL,
                            'file_url' => $shippingLabel,
                            'file_url_2' => $shippingLabelPath,
                            'order_product_id' => $orderProduct->id,
                            'seller_id' => $currentUserId,
                            'option' => null,
                            'print_space' => null,
                            'file_name' => ''
                        ];
                    }
                    if (!$invalid) {
                        File::query()->where([
                                'order_id' => $newOrder->id,
                                'order_product_id' => $orderProduct->id,
                                'seller_id' => $currentUserId,
                            ])
                            ->update([
                                'status' => FileStatusEnum::INACTIVE
                            ]);
                        File::query()->insert($files);
                    }

                    $importedOrders[$newOrder->id] = $newOrder;
                    CheckOrderAtRisk::dispatch($newOrder->id);
                }
            }
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw new ErrorException($exception->getMessage(), 403);
        }

        if (count($messages) > 0) {
            DB::rollBack();
            throw new ErrorException(implode(';', $messages), 403);
        }

        DB::commit();

        foreach ($importedOrders as $order) {
            ValidateSellerFulfillOrder::dispatch($order, $this->skipValidateAddress);
        }
    }

    public static function handleCustomOptions(Order $order, array $customOptions): array
    {
        foreach ($customOptions as &$group) {
            foreach ($group as &$customOption) {
                if (!is_array($customOption)) {
                    // ask: should log this?
                    continue;
                }

                if ($customOption['type'] === 'image' && !empty($customOption['value'])) {
                    $imagePath = 'o/' . $order->id . '/' . basename($customOption['value']);
                    $movedPath = saveTempFileAws($customOption['value'], $imagePath);
                    $customOption['value'] = $movedPath;
                    $customOption['imagePath'] = $movedPath;
                }
            }
        }

        return $customOptions;
    }
}
