<?php

namespace App\Actions\Admin\Analytic3;

use App\Enums\DateRangeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Models\IndexEventLogs;
use App\Models\IndexOrder;
use App\Models\IndexOrderProduct;
use App\Models\SaleReport;
use App\Models\User;
use Carbon\Carbon;

class GetSaleReportDetailAction
{
    private $controller;
    private $year;
    private $month;

    public function __construct($controller, $year, $month)
    {
        $this->controller = $controller;
        $this->year = $year;
        $this->month = $month;
    }

    public function handle($compare)
    {
        $arr = [];
        $year = $this->year;
        $month = $this->month;
        $time = self::getTime($year, $month);
        $currentYM = Carbon::now()->format('Ym');
        $queryYM = $time->format('Ym');
        if ($queryYM < $currentYM) {
            $data = SaleReport::query()->where('time', $time)->get();
            if ($data->isNotEmpty()) {
                foreach ($data as $item) {
                    $arr[$item->type] = $item->value;
                }
                return $arr;
            }
        }
        $this->controller->dateRanges = self::getDateRanges($year, $month);

        $visits = IndexEventLogs::query()
            ->calculateCount(dateRanges: $this->controller->dateRanges)
            ->addFilterAnalytic($this->controller->arrFilter, $this->controller->dateRanges)
            ->addExcludeAnalytic($this->controller->arrExclude)
            ->filterVisit()
            ->first();
        $arr['visit'] = $visits?->count ?? 0 + $visits?->total_sum ?? 0;

        $statsOrder = IndexOrder::query()
            ->getAnalyticOverview()
            ->countActiveSellers()
            ->addFilterAnalytic($this->controller->arrFilter, $this->controller->dateRanges)
            ->addExcludeAnalytic($this->controller->arrExclude)
            ->first();

        if ($statsOrder) {
            foreach ($statsOrder->getAttributes() as $key => $val) {
                $arr[$key] = $val;
            }
        }

        $orderQuery = IndexOrder::query()
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->whereNotIn('status', [
                OrderStatus::CANCELLED,
                OrderStatus::REFUNDED,
            ])
            ->addFilterAnalytic($this->controller->arrFilter, $this->controller->dateRanges)
            ->addExcludeAnalytic($this->controller->arrExclude);
        $sellerIds = $orderQuery->clone()
            ->select('seller_id')
            ->distinct()
            ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
            ->pluck('seller_id')
            ->toArray();
        $fulfillSellerIds = $this->getFulfillSellers($sellerIds)->pluck('id')->toArray();

        $fulfillment = $orderQuery->clone()
            ->selectRaw('COUNT(DISTINCT `seller_id`) as fulfill_sellers')
            ->selectRaw('COUNT(*) as fulfill_orders')
            ->selectRaw('SUM(`total_quantity`) as fulfill_items')
            ->selectRaw('SUM(`total_amount`) as fulfill_total')
            ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
            ->whereIn('seller_id', $fulfillSellerIds)
            ->first();

        if ($fulfillment) {
            foreach ($fulfillment->getAttributes() as $key => $val) {
                $arr[$key] = $val;
            }
        }

        $arr['sales'] = $orderQuery->clone()
            ->selectRaw('SUM(`total_amount`) as sales')
            ->whereIn('type', [
                OrderTypeEnum::REGULAR,
                OrderTypeEnum::CUSTOM,
            ])
            ->value('sales');

        $arr['ref_commission'] = 0;
        $refCommissions = IndexOrderProduct::query()
            ->selectRaw('SUM(`quantity`) as items')
            ->whereHas('order', function ($q) use ($orderQuery) {
                $q->whereIn('type', [
                    OrderTypeEnum::REGULAR,
                    OrderTypeEnum::CUSTOM,
                    OrderTypeEnum::FULFILLMENT,
                    OrderTypeEnum::FBA,
                ]);
                cloneQueryWhere($q, $orderQuery);
            })
            ->groupBy('ref_id')
            ->get();

        if ($refCommissions->count() > 0) {
            foreach ($refCommissions as $refCommission) {
                $items = $refCommission->items;
                $rate = 0.3;

                if ($items >= 50000) {
                    $rate = 0.6;
                } elseif ($items >= 20000) {
                    $rate = 0.45;
                }

                $arr['ref_commission'] += $items * $rate;
            }
        }

        $arr['cr'] = 0;
        $arr['upsell'] = 0;

        if (!empty($arr['orders'])) {
            if (!empty($arr['visit'])) {
                $arr['cr'] = $arr['orders'] / $arr['visit'];
            }
            if (!empty($arr['items'])) {
                $arr['upsell'] = $arr['items'] / $arr['orders'];
            }
        }
        $arr['total_sales'] = $arr['fulfill_total'] + $arr['sales'];
        if ($compare < 0) {
            $this->insertToSaleReport($arr, $year, $month);
        }
        return $arr;
    }

    public static function getDateRanges($year, $month): array
    {
        $startDate = Carbon::createFromDate($year, $month, 1);

        $dateRanges['range'][0] = $startDate->format('Y-m-d');
        $dateRanges['range'][1] = $startDate->copy()->endOfMonth()->format('Y-m-d');
        $dateRanges['type'] = DateRangeEnum::CUSTOM;

        return $dateRanges;
    }

    public static function getTime($year, $month): Carbon
    {
        return Carbon::createFromDate($year, $month, 1)->startOfDay();
    }

    public function getFulfillSellers($fulfillSellersIds)
    {
        return User::query()
            ->select('id')
            ->whereIn('id', $fulfillSellersIds)
            ->filterFulfillSeller()
            ->get();
    }

    private function insertToSaleReport($arr, $year, $month): void
    {
        $data = [];
        $time = self::getTime($year, $month);
        foreach ($arr as $key => $value) {
            $data[] = [
                'time' => $time,
                'type' => $key,
                'value' => $value,
            ];
        }
        SaleReport::query()->insert($data);
    }
}
