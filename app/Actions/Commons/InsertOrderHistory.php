<?php

namespace App\Actions\Commons;

use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderSupportStatusEnum;
use App\Http\Controllers\SystemConfigController;
use App\Models\Order;
use App\Models\OrderHistory;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\OrderService\Models\RegionOrders;

class InsertOrderHistory
{
    use Dispatchable, SerializesModels;

    private Order|RegionOrders|null $order;
    private string $action;
    private $details;
    private string $displayLevel;
    private ?string $email;

    public function __construct(
        Order|RegionOrders|null $order,
        string                  $action,
                                $details,
        string                  $displayLevel,
        ?string                 $email
    )
    {
        $this->order = $order;
        $this->action = $action;
        $this->details = $details;
        $this->displayLevel = $displayLevel;
        $this->email = $email;
    }

    public function handle(): void
    {
        if (isEnvTesting()) {
            return;
        }

        $order = $this->order;
        if (is_null($order)) {
            return;
        }
        if (!is_array($this->details)) {
            if ($this->displayLevel === OrderHistoryDisplayLevelEnum::ADMIN) {
                $detail = null;
                $adminDetail = $this->details;
            } else {
                $detail = $this->details;
                $adminDetail = null;
            }
        } else {
            [$detail, $adminDetail] = $this->details;
        }
        $assigneeEmail = optional(SystemConfigController::supporters()->firstWhere('id', $order->assignee))->email;
        $supportStatus = !is_null($order->support_status) ? OrderSupportStatusEnum::getKey($order->support_status) : null;
        if (
            empty($supportStatus)
            || $order->support_status === OrderSupportStatusEnum::NORMAL
        ) {
            $supportStatus = null;
        }

        OrderHistory::query()
            ->insert(
                [
                    'id' => generateUUID(),
                    'order_id' => $order->id,
                    'action' => $this->action,
                    'order_status' => $order->status,
                    'fulfill_status' => $order->fulfill_status,
                    'assignee' => $assigneeEmail,
                    'support_status' => $supportStatus,
                    'detail' => $detail,
                    'admin_detail' => $adminDetail,
                    'updated_by' => $this->email,
                    'display_level' => $this->displayLevel,
                ]
            );
    }
}
