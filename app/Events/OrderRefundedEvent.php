<?php
namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderRefundedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Order $order;

    public function __construct($orderId)
    {
        $this->order = Order::query()->whereKey($orderId)->first();
    }
}
