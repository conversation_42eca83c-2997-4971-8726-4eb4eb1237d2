<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Providers\FulfillAPI\Nltp\Model\ResponseModel;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class NltpWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    // statues must lower key
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING => [
            ResponseModel::DRAFT,
            ResponseModel::DOWNLOADED,
            ResponseModel::ORDERED,
            ResponseModel::NEW,
            ResponseModel::TEST
        ],
        FulfillmentStatusEnum::PROCESSING => [
            ResponseModel::IN_PRODUCTION,
            ResponseModel::PRINTED,
            ResponseModel::REPRINT,
            ResponseModel::LABEL_PRINTED,
            ResponseModel::PRESSED,
            ResponseModel::ON_HOLD
        ],
        FulfillmentStatusEnum::FULFILLED => [
            ResponseModel::DELIVERED,
            ResponseModel::TRANSIT,
            ResponseModel::SHIPPED,
        ],
        FulfillmentStatusEnum::REJECTED => [
            ResponseModel::CANCELED,
            ResponseModel::REFUNDED,
        ],
    ];

    protected array $statusesCancelled = [
        ResponseModel::CANCELED,
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response;
    }

//    {
//        "createdAt": "2024-12-18T01:05:34.850Z",
//        "updatedAt": "2024-12-18T01:59:50.000Z",
//        "code": "so4961_576581331130",
//        "customerName": "jony smith",
//        "customerEmail": "<EMAIL>",
//        "customerPhone": "+1931*****45",
//        "country": "United States",
//        "region": "Tennessee",
//        "city": "Cl*********",
//        "addressLine1": "12** ****** *** *** *(B)",
//        "addressLine2": null,
//        "zip": "37***",
//        "labelUrl": "https://zipimgs.com/supover/label/576581331130880690.pdf",
//        "cost": 7.16,
//        "status": "cancelled",
//        "shippingMethod": "standard",
//        "strOrderId": "BF00000016WF",
//        "trackingId": null,
//        "chargeShippingFee": 0.5,
//        "items": [
//            {
//                "quantity": 1,
//                "sku": "YT-3001Y-NAL-M",
//                "mockupUrl": "https://example.com/mockup.png",
//                "mockupBackUrl": "https://example.com/mockup-back.png",
//                "frontDesignUrl": "https://example.com/front-design.png",
//                "backDesignUrl": null,
//                "sleeveLeft": null,
//                "sleeveRight": null
//            }
//        ]
//    }
public function crawlHandle(): self
    {
        $fulfillStatus = Arr::get($this->data, 'status');
        $this->setFulfillmentStatus(
            $this->fulfillStatusMapping($this->arrStatusMapping, $fulfillStatus)
        );

        if ($this->getFulfillmentStatus() === FulfillmentStatusEnum::EXCEPTION) {
            $this->setExceptionMessage(
                implode(', ', data_get($this->data, 'error', []))
            );
        }

        $this->setSenPrintsOrderId(
            Arr::get($this->data, 'code')
        );
        $this->setSupplierOrderId(
            Arr::get($this->data, 'code')
        );

        $items = Arr::get($this->data, 'items');
        $items = array_map(
            function ($item) {
                $skuItem = Arr::get($item, 'variantId');
                $item['sku'] = $skuItem;
                unset($item['id']);
                return $item;
            },
            $items
        );

        $data = [
            'tracking' => [
                'tracking_code' => Arr::get($this->data, 'trackingId') ?? '',
                'shipping_carrier' => Arr::get($this->data, 'shippingProvider') ?? '',
            ]
        ];

        if (empty($items)) {
            $data['no_items'] = true;
        } else {
            $data['items'] = $items;
        }
        $this->setFulfillments([$data]);
        return $this;

    }
}
