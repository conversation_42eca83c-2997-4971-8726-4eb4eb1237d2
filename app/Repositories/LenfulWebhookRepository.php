<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class LenfulWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    protected array $data;
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PROCESSING => [
            'Processing', 'Hold', 'Picked'
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'Fulfillment'
        ],
        FulfillmentStatusEnum::REJECTED   => [
            'Cancel', 'Refund'
        ],
        FulfillmentStatusEnum::PENDING    => [
            'Pending', 'Updating', 'Resend'
        ],
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response;
    }

    public function webhookHandle(): self
    {
        // don't have
        return $this;
    }

    public function crawlHandle(): self
    {
        $orderId = Arr::get($this->data, 'id');
        $this->setSupplierOrderId($orderId);

        $items = array_map(
            static fn($v) => ['sku' => data_get($v, 'variant.sku')],
            data_get($this->data, 'fulfillments.0.line_items', [])
        );

        $trackings = data_get($this->data, 'fulfillments.0.trackings', []);
        $tracking = Arr::last($trackings);
        $data = [
            'tracking' => [
                'tracking_code'    => $number = Arr::get($tracking, 'number'),
                'shipping_carrier' => Arr::get($tracking, 'company'),
                'tracking_url'     => 'https://t.17track.net/en#nums=' . $number,
            ]
        ];

        $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $this->data['status']);
        if ($number && $fulfillStatus === FulfillmentStatusEnum::PROCESSING) {
            $fulfillStatus = FulfillmentStatusEnum::FULFILLED;
        }

        $this->setFulfillmentStatus($fulfillStatus);

        if (empty($items)) {
            $data['no_items'] = true;
        } else {
            $data['items'] = $items;
        }

        $this->setFulfillments([$data]);

        return $this;
    }
}
