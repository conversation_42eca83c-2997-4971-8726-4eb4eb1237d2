<?php
/** @noinspection PhpPointlessBooleanExpressionInConditionInspection */

namespace App\Repositories;

use App\Enums\DiscordChannel;
use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;
use Throwable;

class MonsterDigitalWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    public array $data;
    private array $fulfillmentStatusMap = [
        FulfillmentStatusEnum::PROCESSING => [
            'received',
            'released',
            'committed',
            'packaged',
        ],
        FulfillmentStatusEnum::FULFILLED => [
            'shipped',
            'delivered to carrier',
        ],
    ];

    protected array $statusesOnHold = [
        'onhold',
    ];
    protected array $statusesCancelled = [
        'cancelled',
        'cancelled item',
    ];

    public string $receiptId = '';

    public function __construct($data, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $data;
    }

    /**
     * Parse request
     * @return self
     */
    public function webhookHandle(): self
    {
        try {
            // Fulfillment Status
            $status = Arr::get($this->data, 'event.name', self::DEFAULT_FULFILL_STATUS_FAILED);
            if ($this->handleStatusException($status)) {
                $status = lookupArrayMapping($this->fulfillmentStatusMap, strtolower($status))
                    ?? self::DEFAULT_FULFILL_STATUS_FAILED;
                $this->setFulfillmentStatus($status);
            }

            $orderId = Arr::get($this->data, 'xid');
            $this->setSupplierOrderId($orderId);
            $this->receiptId = Arr::get($this->data, 'receipt_id');
            $this->setSenPrintsOrderId($orderId);

            // Parse fulfillments
            $supFulfills = Arr::get($this->data, 'event.meta.packages', []);
            if (count($supFulfills) > 1) {
                $this->setExceptionMessage('Multiple trackings found', false);
            }

            $senFulfills = $this->parseFulfillments($supFulfills[0]);
            $this->setFulfillments($senFulfills);
        } catch (Throwable $e) {
            $this->setFulfillmentStatus(FulfillmentStatusEnum::EXCEPTION);
            $this->setExceptionMessage($e->getMessage());
            logException($e, __FUNCTION__, DiscordChannel::FULFILL_ORDER, true);
        }
        return $this;
    }

    public function crawlHandle(): self
    {
        $currentStatus = self::DEFAULT_FULFILL_STATUS_FAILED;
        $fulfillment = [];

        $events = Arr::get($this->data, 'events');
        if (is_array($events)) {
            array_pop($events);

            array_map(function ($event) use (&$fulfillment, &$currentStatus) {
                /** @noinspection TypeUnsafeComparisonInspection */
                if ($event['occurred'] == true || $event['occurred'] == 1) {
                    $currentStatus = strtolower($event['name']);
                    if ($event['name'] === 'shipped') {
                        $supFulfills = Arr::get($event, 'meta.packages', []);
                        if (count($supFulfills) > 1) {
                            $this->setExceptionMessage('Multiple trackings found', false);
                        }

                        $fulfillment = $supFulfills[0];
                    }
                }
            }, $events);
        } else {
            $this->setExceptionMessage(Arr::get($this->data, 'message'));
        }

        $status = $this->fulfillStatusMapping($this->fulfillmentStatusMap, $currentStatus);

        $senFulfills = [];
        switch ($status) {
            case FulfillmentStatusEnum::FULFILLED:
                $senFulfills = $this->parseFulfillments($fulfillment);
                break;
            case FulfillmentStatusEnum::PROCESSING:
            case FulfillmentStatusEnum::EXCEPTION:
                if (empty($fulfillment)) {
                    $fulfillment = $this->data;
                }
                $senFulfills = $this->parseFulfillments($fulfillment);
                break;
        }
        $this->setFulfillmentStatus($status);

        $orderId = Arr::get($this->data, 'xid');
        $this->setSupplierOrderId($orderId);
        $this->setSenPrintsOrderId($orderId);

        $cancelled = Arr::get($this->data, 'events.cancelleditem');
        /** @noinspection TypeUnsafeComparisonInspection */
        if (Arr::get($cancelled, 'occurred') == true) {
            $supFulfills  = Arr::get($cancelled, 'meta.items');
            $fulfillments = $this->parseFulfillmentsCancelled($supFulfills);
            $this->setFulfillmentsCancelled($fulfillments);
        }

        $this->setFulfillments($senFulfills);

        return $this;
    }

    private function parseFulfillments(array $supFulfill): array
    {
        $fulfillments   = [];
        $tracking       = [
            'tracking_code'    => Arr::get($supFulfill, 'tracking_number'),
            'shipping_carrier' => Arr::get($supFulfill, 'shipping_carrier'),
            'tracking_url'     => Arr::get($supFulfill, 'tracking_url'),
        ];
        $fulfillments[] = [
            'no_items' => true,
            'tracking' => $tracking,
        ];

        return $fulfillments;
    }

    private function parseFulfillmentsCancelled(array $supFulfills): array
    {
        $fulfillments = [];
        foreach ($supFulfills as $lineItem) {
            $log = '';
            if ($lineItem['cancel_quantity'] !== $lineItem['quantity']) {
                $log .= 'Canceled quantity different send fulfill quantity';
            }
            $fulfillments[] = [
                'sku' => $lineItem['sku'],
                'log' => $log . Arr::get($lineItem, 'cancel_reason'),
            ];
        }

        return $fulfillments;
    }
}
