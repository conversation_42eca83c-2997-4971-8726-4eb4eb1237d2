<?php

namespace App\Providers\FulfillAPI\CustomCatLabel;

class Provider extends \App\Providers\FulfillAPI\CustomCat\Provider
{
    /**
     * @param $products
     * @return array
     */
    public function validateProductsPrepareToSupplier ($products) {
        $productsCouldNotBeInSameOrder = [
            'airframe',
            'canvas',
            'poster',
            'pillow'
        ];
        $response = [
            'validate' => true,
            'message' => '',
            'data' => []
        ];

        $templateProducts = [
            'key_words' => [],
            'list_product' => []
        ];
        foreach ($products as $product) {
            $template = $product->template;
            if (empty($template)) {
                continue;
            }
            $templateName = strtolower($template->name);

            foreach ($productsCouldNotBeInSameOrder as $word) {
                if (str_contains($templateName, $word) && !in_array($word, $templateProducts['key_words'])) {
                    $templateProducts['key_words'][] = $word;
                    $templateProducts['list_product'][] = $template->name . " ({$template->sku}) - {$product->supplier_name}";
                    break;
                }
            }
        }
        if (count($templateProducts) > 1) {
            $response = [
                'validate' => false,
                'message' => 'Supplier Customcat Label does not allow Canvas, Posters, Airframes, Pillows in same label : ' . implode('.', $templateProducts['list_product']),
                'data' => $templateProducts['list_product']
            ];
        }

        return $response;
    }
}
