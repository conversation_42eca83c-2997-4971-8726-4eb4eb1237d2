<?php

namespace App\Services;

use App\Enums\CacheKeys;
use App\Enums\ChargeOrderTypeEnum;
use App\Enums\CurrencyEnum;
use App\Enums\DiscordChannel;
use App\Enums\DiscordUserIdEnum;
use App\Enums\OrderDisputeActionTypeEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\PersonalizedType;
use App\Enums\PricingModeEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\PromotionTypeEnum;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Enums\ShippingMethodEnum;
use App\Enums\StorePaymentGatewayTypeEnum;
use App\Enums\StoreStatusEnum;
use App\Enums\SupplierEnum;
use App\Enums\TrackingStatusEnum;
use App\Http\Controllers\SystemConfigController;
use App\Http\Controllers\TrackingController;
use App\Http\Controllers\UploadController;
use App\Jobs\OrderCostStatisticsJob;
use App\Jobs\ProcessChargeCustomOrderJob;
use App\Jobs\ProcessChargeFulfillOrderJob;
use App\Jobs\ProcessUpdateOrderTrackingStatus;
use App\Models\Design;
use App\Models\Order;
use App\Models\OrderDispute;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\PaymentGateway;
use App\Models\Product;
use App\Models\ProductCollection;
use App\Models\ProductVariant;
use App\Models\PromotionRule;
use App\Models\Store;
use App\Models\StorePaymentGateway;
use App\Models\SupplierShippingLateRule;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Models\Template;
use App\Models\TrackingStatus;
use App\Models\User;
use App\Traits\ElasticClient;
use App\Traits\Encrypter;
use App\Traits\GetStoreDomain;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\OrderService\Helpers\OrderHelper;
use Modules\OrderService\Jobs\DatabaseSync\SyncOrderToRegion;
use Modules\OrderService\Models\RegionOrders;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;
use RuntimeException;
use Throwable;

class OrderService
{
    use ElasticClient;
    use Encrypter, GetStoreDomain;

    protected static OrderService $os;

    /**
     * @return self
     */
    public static function singleton()
    {
        return self::$os ??= new self();
    }

    /**
     * @param $destination
     * @param $joining
     * @param $fields
     * @return mixed
     */
    public function mappingOrderCountedByStatus($destination, $joining, $fields)
    {
        foreach ($fields as $field) {
            $destination[$field] = $joining[$field];
        }
        return $destination;

    }

    /**
     * @param $trackingCodes
     * @param bool $allowGetTrackInfo
     * @return array|bool
     * @throws RuntimeException
     */
    public static function updateTrackingStatusForTrackingCodes($trackingCodes, $allowGetTrackInfo = false)
    {
        try {
            $now = now();
            $trackingCodesNotDelivered = [];
            $trackingCodesDelivered = [];
            $orderIds = $trackingCodes->pluck('order_id')->unique()->toArray();
            $orders = Order::query()->select(['id', 'shipping_method'])->whereIn('id', $orderIds)->get();
            $trackingCodes = $trackingCodes->map(function ($trackingCode) use ($orders) {
                if (is_object($trackingCode)) {
                    $trackingCode = json_decode(json_encode($trackingCode, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR);
                }
                if (empty($trackingCode['shipping_carrier'])) {
                    $cfg = suppliers()->where('supplier_id', $trackingCode['supplier_id'])->first();
                    $order = $orders->where('id', $trackingCode['order_id'])->first();
                    if ($cfg && $order) {
                        $shipping_carriers = data_get($cfg, 'shipping_carriers', []);
                        $carrier = data_get($shipping_carriers, $order->shipping_method);
                        $trackingCode['shipping_carrier'] = $carrier;
                    }
                }
                $arrTrackingCode = Str::of($trackingCode['tracking_code'])->explode(',')->filter()->toArray();
                $trackingCode['tracking_code'] = data_get($arrTrackingCode, 0);
                return $trackingCode;
            });
            $trackingCodes = $trackingCodes->values();
            $codes = $trackingCodes->pluck('tracking_code')->unique()->filter()->toArray();
            $needRegister = [];
            if (!empty($codes)) {
                $getTrackInfo = SeventeenTrack::getTrackInfo($codes);
                if (!empty($getTrackInfo['rejected'])) {
                    foreach ($getTrackInfo['rejected'] as $rejectedCode) {
                        if ($rejectedCode['error']['code'] === -18019909) {
                            continue;
                        }
                        if ($rejectedCode['error']['code'] === -18019902) {
                            $code = self::getObjectTrackingCode($trackingCodes, $rejectedCode['number']);
                            if (!empty($code)) {
                                $needRegister[] = $code;
                            }
                            continue;
                        }
                        graylogInfo('[17Track] Get tracking info failed - Number: ' . $rejectedCode['number'] . ' - Code: ' . $rejectedCode['error']['code'] . ' - Message: ' . $rejectedCode['error']['message'], [
                            'category' => 'tracking_status_logs',
                        ]);
                    }
                }
                if (!empty($needRegister)) {
                    self::registerOrderTrackingStatus(collect($needRegister));
                }
                if (!empty($getTrackInfo['accepted'])) {
                    $delivered = [];
                    foreach ($getTrackInfo['accepted'] as $acceptedCode) {
                        $status = SeventeenTrack::convertTrackingStatus($acceptedCode['track_info']['latest_status']['status']);
                        if (!$status) {
                            graylogInfo('[17Track] Can not detect tracking status code - Number: ' . $acceptedCode['number'] . ' - Code: ' . $acceptedCode['track_info']['latest_status']['status'], [
                                'category' => 'tracking_status_logs',
                            ]);
                            continue;
                        }
                        $code = self::getObjectTrackingCode($trackingCodes, $acceptedCode['number']);
                        if (empty($code)) {
                            continue;
                        }
                        if (in_array($status, [TrackingStatusEnum::NEW, TrackingStatusEnum::NOTFOUND], true)) {
                            ProcessUpdateOrderTrackingStatus::dispatch([$code])->onQueue('order')->delay(now()->addMinutes(5));
                        }
                        if ($status !== $code->status) {
                            $updates = [
                                'status' => $status
                            ];

                            if (empty($code->shipping_carrier) && !empty($acceptedCode['carrier'])) {
                                $updates['shipping_carrier'] = $acceptedCode['carrier'];
                            }

                            TrackingStatus::query()->where('tracking_code', $acceptedCode['number'])->update($updates);
                            OrderProduct::query()
                                ->where('tracking_code', 'like', $acceptedCode['number'] . '%')
                                ->update(['tracking_status' => $status]);
                        }
                        if ($status === TrackingStatusEnum::DELIVERED) {
                            $received_at = $now;
                            if (!empty($acceptedCode['track_info']['latest_event']['time_utc'])) {
                                $received_at = Carbon::parse($acceptedCode['track_info']['latest_event']['time_utc']);
                            }
                            $delivered[] = [
                                'received_at' => $received_at,
                                'order_id' => $code->order_id,
                                'tracking_code' => $code->tracking_code
                            ];
                            $trackingCodesDelivered[] = $code->tracking_code;
                        } else {
                            $trackingCodesNotDelivered[] = $code->tracking_code;
                            if (in_array($status, TrackingStatusEnum::inTransitStatuses(), true)) {
                                $orderProducts = OrderProduct::query()
                                    ->with('order')
                                    ->when($code->supplier_id === SupplierEnum::GEARMENT_TWO_DAYS, function ($q) {
                                        $q->whereHas('order', function ($q) {
                                            $q->where('type', OrderTypeEnum::FBA);
                                            $q->whereNotNull('shipping_label');
                                        });
                                    })
                                    ->where([
                                        'order_id' => $code->order_id,
                                        'fulfill_status' => OrderProductFulfillStatus::PROCESSING,
                                    ])
                                    ->where('tracking_code', 'like', $code->tracking_code . '%')
                                    ->whereIn('tracking_status', [
                                        TrackingStatusEnum::TRANSIT,
                                        TrackingStatusEnum::OUT_FOR_DELIVERY,
                                        TrackingStatusEnum::INFO_RECEIVED,
                                        TrackingStatusEnum::NEW,
                                        TrackingStatusEnum::EXCEPTION,
                                        TrackingStatusEnum::ALERT,
                                        TrackingStatusEnum::NOTFOUND
                                    ])
                                    ->where('supplier_id', $code->supplier_id)
                                    ->whereNotNull('tracking_code')
                                    ->get();
                                if ($orderProducts->isNotEmpty()) {
                                    $orderProducts->each(function (OrderProduct $orderProduct) {
                                        $orderProduct->delivered_at = now();
                                        $orderProduct->processing_day = diffProcessingDay($orderProduct->fulfilled_at);
                                        $orderProduct->fulfill_status = OrderProductFulfillStatus::ON_DELIVERY;
                                        $orderProduct->save();
                                        $countProductPreShipment = OrderProduct::query()
                                            ->where('order_id', $orderProduct->order_id)
                                            ->whereIn('tracking_status', [
                                                TrackingStatusEnum::INFO_RECEIVED,
                                                TrackingStatusEnum::NEW,
                                                TrackingStatusEnum::NOTFOUND
                                            ])
                                            ->whereNotNull('tracking_code')
                                            ->count();
                                        $countUnfulfilled = OrderProduct::query()
                                            ->where('order_id', $orderProduct->order_id)
                                            ->whereNotIn('fulfill_status', [
                                                OrderProductFulfillStatus::FULFILLED,
                                                OrderProductFulfillStatus::CANCELLED,
                                                OrderProductFulfillStatus::ON_DELIVERY,
                                            ])
                                            ->count();
                                        if ($countUnfulfilled === 0) {
                                            $data = [];
                                            if (in_array($orderProduct->order->status, [OrderStatus::PENDING, OrderStatus::PROCESSING], true)) {
                                                $data['status'] = OrderStatus::COMPLETED;
                                            }
                                            $data['fulfill_status'] = OrderFulfillStatus::ON_DELIVERY;
                                            if ($countProductPreShipment > 0) {
                                                $data['fulfill_status'] = OrderFulfillStatus::PROCESSING;
                                            }
                                            Order::query()->whereKey($orderProduct->order_id)->update($data);
                                        }
                                    });
                                }
                            }
                        }
                    }
                    self::updateOrderFulfilledFromDataApi($delivered);
                }
                TrackingStatus::query()->whereIn('tracking_code', $codes)->update(['updated_at' => $now]);
            }
            if ($allowGetTrackInfo) {
                return [
                    'not_delivered' => array_unique($trackingCodesNotDelivered),
                    'delivered' => array_unique($trackingCodesDelivered)
                ];
            }
            return true;
        } catch (Throwable $e) {
            graylogError('[17Track] Update tracking info failed, error: ' . $e->getMessage(), [
                'category' => 'tracking_status_logs',
                'tracking_codes' => $trackingCodes,
                'trace' => $e->getTraceAsString()
            ]);
            throw new RuntimeException($e->getMessage());
        }
    }

    /**
     * @param $delivered
     * @return bool
     */
    public static function updateOrderFulfilledFromDataApi($delivered)
    {
        if (empty($delivered)) {
            return false;
        }
        $delivered = collect($delivered);
        $orderIds = $delivered->pluck('order_id');
        $trackingCodes = $delivered->pluck('tracking_code');
        foreach ($delivered as $item) {
            $received_at = !empty($item['received_at']) ? $item['received_at'] : now();
            Order::query()->where('id', $item['order_id'])->update(['received_at' => $received_at]);
            OrderProduct::query()->where('order_id', $item['order_id'])->where('tracking_code', 'like', $item['tracking_code'] . '%')->update([
                'received_at' => $received_at,
                'fulfill_status' => OrderProductFulfillStatus::FULFILLED,
                'shipping_day' => DB::raw('timestampdiff(second, fulfilled_at, now()) / (24 * 60 * 60)'),
            ]);
        }
        $productsOfDeliveredOrders = OrderProduct::query()->whereIn('order_id', $orderIds)->get();
        if ($productsOfDeliveredOrders->isNotEmpty()) {
            $orderIdsHaveAllFulfilledProducts = [];
            $productsOfDeliveredOrders->groupBy('order_id')
                ->each(function ($order, $orderId) use (&$orderIdsHaveAllFulfilledProducts) {
                    $fulfillStatuses = $order->pluck('fulfill_status')->unique();

                    if ($fulfillStatuses->count() === 1 && $fulfillStatuses->first() === OrderProductFulfillStatus::FULFILLED) {
                        $orderIdsHaveAllFulfilledProducts[] = $orderId;
                    }
                });

            if (!empty($orderIdsHaveAllFulfilledProducts)) {
                Order::query()->whereIn('id', $orderIdsHaveAllFulfilledProducts)->update(['fulfill_status' => OrderFulfillStatus::FULFILLED, 'status' => OrderStatus::COMPLETED]);
            }
        }

        $orders = Order::query()
            ->with('order_products', function ($query) use ($trackingCodes) {
                $query->where(function ($query) use ($trackingCodes) {
                    foreach ($trackingCodes as $trackingCode) {
                        $query->orWhere('tracking_code', 'like', $trackingCode . '%');
                    }
                });
            })
            ->whereHas('seller', function ($query) {
                $query->where('smart_remarketing', true);
            })
            ->whereNotNull(['customer_name', 'customer_phone'])
            ->whereIn('id', $orderIds)
            ->get();

        if ($orders->isNotEmpty()) {
            $trackingController = new TrackingController();
            $orders->each(function ($order) use ($trackingController) {
                $trackingController->sendUpsellSms($order);
            });
        }
        return true;
    }

    /**
     * @param $trackingCodes
     * @return bool
     */
    public static function registerOrderTrackingStatus($trackingCodes)
    {
        $insertCodes = [];
        $updateCodes = [];
        try {
            $orderProducts = $trackingCodes->toArray();
            SeventeenTrack::overrideRegister($orderProducts);
            $register = SeventeenTrack::register($orderProducts);
            if (!empty($register['errors'])) {
                foreach ($register['errors'] as $error) {
                    graylogInfo('[17Track] Register tracking number failed - Code: ' . $error['code'] . ' - Message: ' . $error['message'], [
                        'category' => 'tracking_status_logs',
                    ]);
                }
                return false;
            }
            if (!empty($register['rejected'])) {
                foreach ($register['rejected'] as $rejectedCode) {
                    if (in_array($rejectedCode['error']['code'], ['-18019908', '-18019907'])) {
                        if (Cache::get('17track_register_error')) {
                            continue;
                        }
                        Cache::add('17track_register_error', $rejectedCode['error']['message'], CacheKeys::CACHE_1H);
                    }

                    graylogInfo('[17Track] Register tracking number failed - Number: ' . $rejectedCode['number'] . ' - Code: ' . $rejectedCode['error']['code'] . ' - Message: ' . $rejectedCode['error']['message'], [
                        'category' => 'tracking_status_logs',
                        'tracking_code' => $rejectedCode['number'],
                    ]);

                    if ($rejectedCode['error']['code'] != SeventeenTrack::UNDETECTED && $rejectedCode['error']['code'] != SeventeenTrack::REGISTERED) {
                        $trackingStatus = TrackingStatus::query()->where([
                            'tracking_code' => $rejectedCode['number'],
                        ])->first();
                        $updateCodes[] = [
                            [
                                'tracking_code' => $rejectedCode['number']
                            ],
                            [
                                'tracking_status' => $trackingStatus->status ?? TrackingStatusEnum::NOTFOUND
                            ]
                        ];
                        continue;
                    }
                    $code = self::getObjectTrackingCode($trackingCodes, $rejectedCode['number']);
                    if (empty($code)) {
                        continue;
                    }
                    $status = SeventeenTrack::convertTrackingStatus($rejectedCode['error']['code']);
                    $updateCodes[] = [
                        [
                            'tracking_code' => $code->tracking_code
                        ],
                        [
                            'tracking_status' => $status
                        ]
                    ];
                    $insertCodes[$code->tracking_code] = [
                        [
                            'tracking_code' => $code->tracking_code,
                            'order_number' => $code->order->order_number ?? $code->order_id,
                        ],
                        [
                            'order_id' => $code->order_id ?? $code->order->order_number,
                            'status' => $status,
                        ]
                    ];
                }
            }

            if (!empty($register['accepted'])) {
                foreach ($register['accepted'] as $acceptedCode) {
                    $code = self::getObjectTrackingCode($trackingCodes, $acceptedCode['number']);
                    if (empty($code)) {
                        continue;
                    }
                    $insertCodes[$code->tracking_code] = [
                        [
                            'tracking_code' => $code->tracking_code,
                            'order_number' => $code->order->order_number ?? $code->order_id,
                        ],
                        [
                            'order_id' => $code->order_id ?? $code->order->order_number,
                            'status' => TrackingStatusEnum::NEW,
                            'shipping_carrier' => $acceptedCode['carrier']
                        ]
                    ];
                    $updateCodes[] = [
                        [
                            'tracking_code' => $code->tracking_code
                        ],
                        [
                            'tracking_status' => TrackingStatusEnum::NEW
                        ]
                    ];
                }
            }
            if (!empty($insertCodes)) {
                foreach ($insertCodes as $insertCode) {
                    TrackingStatus::query()->updateOrCreate($insertCode[0], $insertCode[1]);
                }
            }
            if (!empty($updateCodes)) {
                foreach ($updateCodes as $updateCode) {
                    OrderProduct::query()->where('tracking_code', 'like', $updateCode[0]['tracking_code'] . '%')->update($updateCode[1]);
                }
            }
            graylogInfo('End register tracking code on the 17track.', [
                'category' => 'tracking_status_logs',
                'updateCodes' => !empty($updateCodes) ? json_encode($updateCodes, JSON_THROW_ON_ERROR) : '',
                'insertCodes' => !empty($insertCodes) ? json_encode($insertCodes, JSON_THROW_ON_ERROR) : '',
            ]);
            return true;
        } catch (Throwable $e) {
            logToDiscord('[17Track] Register tracking number failed - Message: ' . $e->getMessage(), 'tracking_status_logs', true);
            graylogError('Error in register tracking_code.', [
                'category' => 'error_register_tracking_code',
                'error' => $e,
                'param_input' => $trackingCodes,
            ]);
            return false;
        }
    }

    /**
     * @param $trackingCodes
     * @param $tracking_code
     * @return object|null
     */
    public static function getObjectTrackingCode($trackingCodes, $tracking_code)
    {
        $code = null;
        try {
            foreach ($trackingCodes as $trackingCode) {
                if (!is_array($trackingCode)) {
                    $trackingCode = json_decode(json_encode($trackingCode, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR);
                }
                if ((string)$trackingCode['tracking_code'] === (string)$tracking_code) {
                    $code = $trackingCode;
                    break;
                }
            }
        } catch (Exception $e) {
            logToDiscord('[17Track] get object tracking code error ' . $e . 'input tracking codes : ' . $trackingCodes, 'tracking_status_logs', true);
            return null;
        }
        return $code ? (object)$code : null;
    }

    /**
     * @return void
     */
    public static function scanRegisterTrackingCode()
    {
        try {
            foreach (['not_scanned', 'scanned'] as $typeScan) {
                $orderProducts = OrderProduct::query()->select([
                    'id',
                    'order_id',
                    'tracking_code',
                    'shipping_carrier',
                    'tracking_url',
                    'fulfilled_at',
                    'received_at',
                    'delivered_at',
                    'tracking_status as status',
                    'supplier_name',
                    'supplier_id',
                    'fulfill_status',
                ])
                    ->where('fulfill_status', OrderFulfillStatus::ON_DELIVERY)
                    ->where(function ($q) {
                        $q->where('tracking_status', '!=', TrackingStatusEnum::DELIVERED)->orWhereNull('tracking_status');
                    })
                    ->whereNull('received_at')
                    ->whereNotNull('order_product.tracking_code')
                    ->where(function ($q) use ($typeScan) {
                        if ($typeScan === 'not_scanned') {
                            $q->whereNull('next_scan_tracking_code_at');
                        } else {
                            $q->where('next_scan_tracking_code_at', '<=', now());
                        }
                    })
                    ->shippingLate()
                    ->where('delivered_at', '>=', now()->subDays(90)->toDateTimeString())
                    ->with(['order:id,order_number,country', 'supplier:id,location'])
                    ->orderBy('fulfilled_at', 'desc')
                    ->groupBy('tracking_code')
                    ->limit(300)
                    ->get();
                foreach ($orderProducts->chunk(30) as $group) {
                    self::registerOrderTrackingStatus($group);
                    $trackingCodeInfo = self::updateTrackingStatusForTrackingCodes($group, true);
                    $trackingCodeScanned = $group->pluck('tracking_code')->toArray();
                    $trackingCodeUntracked = array_diff($trackingCodeScanned, !empty($trackingCodeInfo['delivered']) ? $trackingCodeInfo['delivered'] : []);
                    $trackingCodeForNextScan = array_unique(array_merge($trackingCodeUntracked, !empty($trackingCodeInfo['not_delivered']) ? $trackingCodeInfo['not_delivered'] : []));
                    OrderProduct::query()
                        ->whereIn('tracking_code', $trackingCodeForNextScan)
                        ->where(function ($q) {
                            $q->where('tracking_status', '!=', TrackingStatusEnum::DELIVERED)->orWhereNull('tracking_status');
                        })
                        ->where('fulfill_status', OrderFulfillStatus::ON_DELIVERY)
                        ->update([
                            'next_scan_tracking_code_at' => now()->addDay()->toDateTimeString(),
                        ]);
                }
            }

            $orderProductsOutPrevious90Days = OrderProduct::query()
                ->join('order', 'order_product.order_id', '=', 'order.id')
                ->select([
                    'order_product.id',
                    'order_product.order_id',
                    'order_product.tracking_code',
                    'order_product.shipping_carrier',
                    'order_product.fulfilled_at',
                    'order_product.received_at',
                    'order_product.delivered_at as created_at',
                    'order_product.tracking_status as status',
                    'order_product.supplier_name',
                    'order_product.tracking_url',
                    'order.order_number'
                ])
                ->where('order_product.fulfill_status', OrderFulfillStatus::ON_DELIVERY)
                ->where(function ($q) {
                    $q->where('order_product.tracking_status', '!=', TrackingStatusEnum::DELIVERED)
                        ->orWhereNull('order_product.tracking_status');
                })
                ->whereNull('order_product.received_at')
                ->whereNotNull('order_product.tracking_code')
                ->shippingLate()
                ->where('order_product.delivered_at', '<', now()->subDays(90)->toDateTimeString())
                ->orderBy('order_product.fulfilled_at')
                ->limit(100)
                ->get()
                ->toArray();
            foreach ($orderProductsOutPrevious90Days as $trackingStatus) {
                TrackingStatus::query()->updateOrCreate([
                    'tracking_code' => $trackingStatus['tracking_code']
                ], [
                    'order_id' => $trackingStatus['order_id'],
                    'order_number' => $trackingStatus['order_number'],
                    'status' => $trackingStatus['status'],
                    'shipping_carrier' => SeventeenTrack::detectCarrier($trackingStatus['tracking_code'], $trackingStatus['shipping_carrier'], $trackingStatus['supplier_name'] ?? null, $trackingStatus['tracking_url'] ?? null),
                    'created_at' => $trackingStatus['created_at'],
                ]);
            }
        } catch (Exception $e) {
            graylogError('Error in scanRegisterTrackingCode.', [
                'category' => 'scanRegisterTrackingCode_log',
                'error' => $e,
            ]);
        }
    }

    /**
     * @param $order
     * @return void
     */
    public static function setUpLateDaysTagForOrder(&$order)
    {
        try {
            $orderProducts = [];
            foreach ($order->items() as $orderItem) {
                foreach ($orderItem->order_products as $product) {
                    $product = $product->toArray();
                    $product['country'] = $orderItem->country;
                    $product['shipping_method'] = $orderItem->shipping_method;
                    $orderProducts [$product['id']] = $product;
                }
            }

            $countries = [];
            $shippingMethods = [];
            $supplierIds = [];
            foreach ($orderProducts as $product) {
                $countries [] = $product['country'];
                $shippingMethods [] = $product['shipping_method'];
                $supplierIds [] = $product['supplier_id'];
            }

            $countries = array_unique($countries);
            $shippingMethods = array_unique($shippingMethods);
            $supplierIds = array_unique($supplierIds);
            $countriesRegionCodes = SystemLocation::query()
                ->select([
                    'code',
                    'region_code',
                    'type'
                ])
                ->whereIn('code', $countries)
                ->get()
                ->keyBy('code')
                ->toArray();

            $regions = array_map(function ($country) {
                return $country['region_code'];
            }, $countriesRegionCodes);
            $regions = array_unique($regions);

            $regionsPattern = implode('|', array_map(function ($region) {
                return preg_quote($region, '/');
            }, $regions));

            $countriesPattern = implode('|', array_map(function ($country) {
                return preg_quote($country, '/');
            }, $countries));

            $rules = SupplierShippingLateRule::query()
                ->whereIn('supplier_id', $supplierIds)
                ->whereIn('shipping_method', $shippingMethods)
                ->where(function ($q) use ($regionsPattern, $countriesPattern) {
                    $q->whereRaw("supplier_shipping_late_rule.region REGEXP '(^|,)($regionsPattern)(,|$)'")
                        ->orWhereRaw("supplier_shipping_late_rule.include_location REGEXP '(^|,)($countriesPattern)(,|$)'")
                        ->orWhereRaw("supplier_shipping_late_rule.region REGEXP '[*]'");
                })
                ->get();

            $ruleAsSupplier = [];
            foreach ($rules as $rule) {
                $ruleAsSupplier [$rule->supplier_id] [$rule->shipping_method] [] = $rule->toArray();
            }

            foreach ($orderProducts as &$product) {
                if (isset($ruleAsSupplier[$product['supplier_id']][$product['shipping_method']])) {
                    $regionCode = isset($countriesRegionCodes[$product['country']]) ? $countriesRegionCodes[$product['country']]['region_code'] : '*';
                    $rulesMatched = $ruleAsSupplier[$product['supplier_id']][$product['shipping_method']];
                    $rulesMatchedForProduct = null;
                    foreach ($rulesMatched as $rule) {
                        if (($rule['region'] == $regionCode && !str_contains($rule['no_location'], $product['country'])) || (str_contains($rule['include_location'], $product['country']) && !str_contains($rule['no_location'], $product['country']))) {
                            $rulesMatchedForProduct = $rule;
                        }
                    }
                    if (isset($rulesMatchedForProduct)) {
                        $dateLateRule = $rulesMatchedForProduct['date_late'];
                        if ($product['late_time'] >= $dateLateRule) {
                            $lateTagString = "over $dateLateRule days";
                        } else {
                            $lateTagString = "unknown";
                        }
                    } else {
                        if ($product['late_time'] >= 29) {
                            $lateTagString = 'over 29 days';
                        } else {
                            $lateTagString = 'unknown';
                        }
                    }
                    $product['late_tag'] = $lateTagString;

                } else if ($product['late_time'] >= 29) {
                    $product['late_tag'] = 'over 29 days';
                } else {
                    $product['late_tag'] = 'unknown';
                }
            }
            unset($product);
            foreach ($order->items() as $orderItem) {
                foreach ($orderItem->order_products as $product) {
                    if (isset($orderProducts[$product->id])) {
                        $product->late_tag = $orderProducts[$product->id]['late_tag'];
                        $product->country = $orderProducts[$product->id]['country'];
                        $product->shipping_method = $orderProducts[$product->id]['shipping_method'];
                    }
                }
            }
        } catch (Exception $e) {
            graylogError('Error in shipping late tag query ', [
                'category' => 'shipping_late_tag_error',
                'error' => $e,
            ]);
        }
    }

    /**
     * @return void
     */
    public static function scanOnDeliveryOrderProductOver90Days()
    {
        try {
            $orderProducts = OrderProduct::query()
                ->where('fulfill_status', OrderProductFulfillStatus::ON_DELIVERY)
                ->whereNotNull('delivered_at')
                ->where('delivered_at', '<', now()->subDays(90)->toDateTimeString())
                ->limit(1000)
                ->get()
                ->chunk(100);
            foreach ($orderProducts as $productChunked) {
                $productIds = [];
                $trackingCodes = [];

                foreach ($productChunked as $product) {
                    $productIds[] = $product->id;
                    $trackingCodes[] = $product->tracking_code;
                }

                $trackingCodes = array_unique($trackingCodes);

                TrackingStatus::query()->whereIn('tracking_code', $trackingCodes)
                    ->whereNotIn('status', [TrackingStatusEnum::DELIVERED, TrackingStatusEnum::EXPIRED])
                    ->update([
                        'status' => TrackingStatusEnum::EXPIRED
                    ]);

                OrderProduct::query()->whereIn('id', $productIds)
                    ->where('fulfill_status', OrderProductFulfillStatus::ON_DELIVERY)
                    ->update([
                        'fulfill_status' => OrderProductFulfillStatus::FULFILLED,
                        'tracking_status' => TrackingStatusEnum::EXPIRED,
                    ]);

                $productsOfDeliveredOrders = OrderProduct::query()->whereIn('id', $productIds)->get();
                if ($productsOfDeliveredOrders->isNotEmpty()) {
                    $orderIdsHaveAllFulfilledProducts = [];
                    $productsOfDeliveredOrders->groupBy('order_id')->each(function ($order, $orderId) use (&$orderIdsHaveAllFulfilledProducts) {
                        $fulfillStatuses = $order->pluck('fulfill_status')->unique();
                        if ($fulfillStatuses->count() === 1 && $fulfillStatuses->first() === OrderProductFulfillStatus::FULFILLED) {
                            $orderIdsHaveAllFulfilledProducts[] = $orderId;
                        }
                    });
                    if (!empty($orderIdsHaveAllFulfilledProducts)) {
                        Order::query()->whereIn('id', $orderIdsHaveAllFulfilledProducts)->update(['fulfill_status' => OrderFulfillStatus::FULFILLED]);
                    }
                }
            }
        } catch (Exception $e) {
            graylogError('Error in scan on delivery order product over 90 days ', [
                'category' => 'error_scan_on_delivery_order_over_90_days',
                'error' => $e,
            ]);
        }
    }

    /**
     * @return void
     */
    public static function scanRegisterTrackingCodeWithCustomCondition()
    {
        try {
            $orderProductQueryLateTimeCondition = OrderProduct::QUERY_LATE_TIME_CONDITION;
            $startDate = new Carbon('2024-03-16');
            $endDate = new Carbon('2024-04-13');
            foreach (['not_scanned', 'scanned'] as $typeScan) {
                $orderProducts = OrderProduct::query()
                    ->select([
                        'id',
                        'order_id',
                        'tracking_code',
                        'shipping_carrier',
                        'tracking_url',
                        'fulfilled_at',
                        'received_at',
                        'delivered_at',
                        'tracking_status as status',
                        'supplier_name',
                        'supplier_id',
                        'fulfill_status',
                        DB::raw("$orderProductQueryLateTimeCondition AS late_time"),
                    ])
                    ->where('fulfill_status', OrderFulfillStatus::ON_DELIVERY)
                    ->where(function ($q) {
                        $q->where('tracking_status', '!=', TrackingStatusEnum::DELIVERED)
                            ->orWhere('tracking_status', '=', null);
                    })
                    // ->where('received_at', '=', null)
                    ->where('order_product.tracking_code', '!=', null)
                    ->where(function ($q) use ($typeScan) {
                        if ($typeScan == 'not_scanned') {
                            $q->where('next_scan_tracking_code_at', '=', null);
                        } else {
                            $q->orWhere('next_scan_tracking_code_at', '<=', now());
                        }
                    })
                    ->shippingLate()
                    ->whereBetween('fulfilled_at', [$startDate->toDateTimeString(), $endDate->toDateTimeString()])
                    // ->whereRaw("$orderProductQueryLateTimeCondition > 29")
                    ->whereIn('supplier_id', [2, 29, 10, 15, 6, 14, 25])
                    ->orderByRaw("FIELD(supplier_id, 2, 29, 10, 15, 6, 14, 25)")
                    ->with(['order:id,country,order_number', 'supplier:id,location'])
                    ->distinct('tracking_code')
                    ->limit(20)
                    ->get()
                    ->groupBy('supplier_id')
                    ->map(function ($group) {
                        return $group->all();
                    })
                    ->flatten();
                foreach ($orderProducts->chunk(30) as $group) {
                    self::registerOrderTrackingStatus($group);
                    $trackingCodeInfo = self::updateTrackingStatusForTrackingCodes($group, true);
                    $trackingCodeScanned = $group->pluck('tracking_code')->toArray();
                    $trackingCodeUntrack = array_diff($trackingCodeScanned, !empty($trackingCodeInfo['delivered']) ? $trackingCodeInfo['delivered'] : []);
                    $trackingCodeForNextScan = array_unique(array_merge($trackingCodeUntrack, !empty($trackingCodeInfo['not_delivered']) ? $trackingCodeInfo['not_delivered'] : []));
                    OrderProduct::query()
                        ->whereIn('tracking_code', $trackingCodeForNextScan)
                        ->where(function ($q) {
                            $q->where('tracking_status', '!=', TrackingStatusEnum::DELIVERED)
                                ->orWhere('tracking_status', '=', null);
                        })
                        ->where('fulfill_status', OrderFulfillStatus::ON_DELIVERY)
                        ->update([
                            'next_scan_tracking_code_at' => now()->addDay()->toDateTimeString(),
                        ]);
                }
            }
        } catch (Exception $e) {
            graylogError('Error in scanRegisterTrackingCode with custom condition.', [
                'category' => 'scanRegisterTrackingCode_log_with_custom_condition',
                'error' => $e,
            ]);
        }
    }


    public static function checkSubmitCaseAndReachout($type, $orderDispute): bool
    {
        $response = true;
        if ($type === OrderDisputeActionTypeEnum::SUBMIT_CASE) {
            $gateway = $orderDispute->order?->paymemt_method ?? $orderDispute?->order?->payment_gateway?->gateway;
            if (($gateway === 'stripe' && count($orderDispute->submit_cases) >= 1) || ($gateway === 'paypal' && count($orderDispute->submit_cases) >= 3)) {
                $response = false;
            } else {
                foreach ($orderDispute->submit_cases as $disputeSubmitCase) {
                    if (!isset($disputeSubmitCase->datestamps)) continue;
                    $disputeDate = Carbon::parse($disputeSubmitCase->datestamps);
                    $currentDate = Carbon::today();
                    if ($currentDate->isSameDay($disputeDate)) {
                        $response = false;
                    }
                }
            }
        } else if ($type === OrderDisputeActionTypeEnum::REACH_OUT) {
            if (count($orderDispute->reachouts) >= 3) {
                $response = false;
            } else {
                foreach ($orderDispute->reachouts as $disputeReachout) {
                    if (!isset($disputeReachout->datestamps)) continue;
                    $disputeDate = Carbon::parse($disputeReachout->datestamps);
                    $currentDate = Carbon::today();
                    if ($currentDate->isSameDay($disputeDate)) {
                        $response = false;
                    }
                }
            }
        }
        return $response;

    }

    /**
     * @param $orderDispute
     * @return void
     */
    public static function checkAllowAddDisputeAction(&$orderDispute)
    {
        $orderSubmitCaseAllow = self::checkSubmitCaseAndReachout(OrderDisputeActionTypeEnum::SUBMIT_CASE, $orderDispute);
        $orderReachOutAllow = self::checkSubmitCaseAndReachout(OrderDisputeActionTypeEnum::REACH_OUT, $orderDispute);
        $orderDispute->allow_submit_case = $orderSubmitCaseAllow;
        $orderDispute->allow_reachout = $orderReachOutAllow;
    }

    /**
     * @param $disputeId
     * @return array
     */
    public static function validateDispute($disputeId)
    {
        $dispute = OrderDispute::query()
            ->with([
                'order:id,payment_gateway_id,created_at,seller_id,total_amount',
                'order.payment_gateway:id,account_id,name,gateway,note',
                'submit_cases:id,order_dispute_id,is_submitted,email,sms,datestamps',
                'reachouts:id,order_dispute_id,is_submitted,email,sms,datestamps'
            ])
            ->where('id', $disputeId)
            ->first();
        if (!isset($dispute)) {
            return [
                'accept' => false,
                'message' => 'Dispute not found'
            ];
        }
        return [
            'accept' => true,
            'message' => '',
            'data' => $dispute
        ];
    }

    public static function validateDisputeOrderAction($disputeId, $type = null)
    {
        $response = [
            'accept' => true,
            'message' => ''
        ];

        $validateDispute = self::validateDispute($disputeId);
        if (!$validateDispute['accept']) {
            $response = [
                'accept' => false,
                'message' => $validateDispute['message']
            ];
            return $response;
        }

        $dispute = $validateDispute['data'];

        if ($type == OrderDisputeActionTypeEnum::SUBMIT_CASE) {
            $allowSubmitCase = self::checkSubmitCaseAndReachout(OrderDisputeActionTypeEnum::SUBMIT_CASE, $dispute);
            $response = [
                'accept' => $allowSubmitCase,
                'message' => $allowSubmitCase ? '' : 'Submit case not allow'
            ];
        } else if ($type == OrderDisputeActionTypeEnum::REACH_OUT) {
            $allowReachout = self::checkSubmitCaseAndReachout(OrderDisputeActionTypeEnum::REACH_OUT, $dispute);
            $response = [
                'accept' => $allowReachout,
                'message' => $allowReachout ? '' : 'Reachout not allow'
            ];
        }

        return $response;
    }

    public static function scanEstimateDeliveryDateForExpress()
    {
        $orders = Order::query()
            ->where(function ($q) {
                $q->whereNull('estimate_delivery_date')
                    ->orWhere('estimate_delivery_date', '');
            })
            ->where('shipping_method', ShippingMethodEnum::EXPRESS)
            ->whereIn('status', [
                OrderStatus::PROCESSING,
                OrderStatus::ON_HOLD,
                OrderStatus::PENDING_PAYMENT,
                OrderStatus::COMPLETED
            ])
            ->whereIn('fulfill_status', [
                OrderFulfillStatus::UNFULFILLED,
                OrderFulfillStatus::CANCELLED,
                OrderFulfillStatus::PROCESSING,
                OrderFulfillStatus::ON_HOLD,
                OrderFulfillStatus::INVALID,
                OrderFulfillStatus::NO_SHIP,
                OrderFulfillStatus::REVIEWING,
                OrderFulfillStatus::DESIGNING,
                OrderFulfillStatus::ON_DELIVERY
            ])
            ->where('created_at', '>', now()->startOfYear()->toDateTimeString())
            ->limit(500)
            ->get();
        foreach ($orders as $order) {
            $estimatedDeliveryDates = $order->getEstimateDeliveryDates();
            $estimatedDeliveryTo = $estimatedDeliveryDates['to_date']->startOfDay()->toDateTimeString();
            $order->estimate_delivery_date = $estimatedDeliveryTo;
            $order->save();
        }
    }

    public static function mapDataToMergeInOrderDataExport($result, $sheetColumns)
    {
        $response = [];
        $mappedData = [];
        $sheetColumnData = [];
        foreach ($result as $index => $dataCell) {
            $mappedData[$dataCell['order_id']][] = $index;
        }

        foreach ($mappedData as $data) {
            $sheetColumnData [] = [
                'start' => min($data),
                'end' => max($data)
            ];
        }
        foreach ($sheetColumns as $column) {
            $response[$column] = $sheetColumnData;
        }
        return $response;

    }

    public static function mapHandlefeeOnExportOrder(&$result, $sheetColumns)
    {
        $orderLoaded = [];
        foreach ($result as $index => &$item) {
            try {
                if (in_array($item['order_id'], $orderLoaded)) {
                    foreach ($sheetColumns as $column) {
                        $item[$column] = '';
                    }
                } else {
                    $orderLoaded[] = $item['order_id'];
                }
            } catch (Exception $e) {
                continue;
            }
        }
    }

    /**
     * @param $templateByCampLocation
     * @param $templateByOrderLocation
     * @param $dynamicBaseCostIndex
     * @return void
     */
    public static function getDynamicBaseCostIndex($templateByCampLocation, $templateByOrderLocation, &$dynamicBaseCostIndex)
    {
        try {
            if (!isset($templateByCampLocation, $templateByOrderLocation) || !is_numeric($templateByCampLocation->base_cost) || !is_numeric($templateByOrderLocation->base_Cost)) {
                $dynamicBaseCostIndex = 0;
            }
            $index = $templateByOrderLocation->base_cost - $templateByCampLocation->base_cost;
            $dynamicBaseCostIndex = roundToHalf($index);

        } catch (Exception $e) {
            $dynamicBaseCostIndex = 0;
        }
    }

    /**
     * @param $orderProducts
     * @return void
     * */
    public static function handleOrderProductPromotionBundle(&$orderProducts): void
    {
        try {

            $promotionBundles = [];
            $promotionBundleIds = [];
            foreach ($orderProducts as $index => $orderProduct) {
                if (!isset($orderProduct['promotion']) ||
                    !isset($orderProduct['campaignBundleId']) ||
                    !isset($orderProduct['product_id']) ||
                    !isset($orderProduct['productBundleId'])) {
                    continue;
                }

                $existedPromotionBundle = array_filter($promotionBundles, function ($each) use ($orderProduct) {
                    return $each['promotion_rule_id'] == $orderProduct['promotion']['id'];
                });

                if (empty($existedPromotionBundle)) {
                    $existedBaseCampaign = array_filter($orderProducts, function ($each) use ($orderProduct) {
                        return $each['campaign_id'] == $orderProduct['campaignBundleId'] && $each['product_id'] == $orderProduct['productBundleId'];
                    });

                    if (empty($existedBaseCampaign)) {
                        continue;
                    }
                    $promotionBundles [] = [
                        'promotion_rule_id' => $orderProduct['promotion']['id'],
                        'related_campaign_id' => $orderProduct['campaignBundleId'],
                        'product_ids' => [$orderProduct['product_id']],
                        'related_product_id' => $orderProduct['productBundleId'],
                    ];
                    $promotionBundleIds [] = $orderProduct['promotion']['id'];
                } else {
                    $promotionBundles = array_map(function ($bundle) use ($orderProduct) {
                        if ($bundle['promotion_rule_id'] == $orderProduct['promotion']['id']) {
                            $bundle ['product_ids'] [] = $orderProduct['product_id'];
                        }
                        return $bundle;
                    }, $promotionBundles);
                }
            }

            foreach ($promotionBundles as &$promotionBundle) {
                $orderProduct = collect($orderProducts)->filter(function ($op) use ($promotionBundle) {
                    return !isset($op['promotion']) && isset($op['campaign_id']) &&
                        $op['campaign_id'] == $promotionBundle['related_campaign_id'] &&
                        $op['product_id'] == $promotionBundle['related_product_id'];
                })->first();
                if (isset($orderProduct)) {
                    $promotionBundle['related_product_id'] = Arr::get($orderProduct, 'product_id');
                }
            }

            $promotionBundleIds = array_unique($promotionBundleIds);
            $promotionDatas = PromotionRule::query()->whereIn('id', $promotionBundleIds)->get();
            foreach ($promotionBundles as $index => &$bundle) {
                if (isset($bundle['promotion_rule_id'])) {
                    $bundleData = $promotionDatas->where('id', $bundle['promotion_rule_id'])->first();
                    $bundleRule = json_decode($bundleData?->rules);
                    $limitBundleProducts = self::orderPromotionGetLimitBdProduct($bundleData);
                    if (empty($bundleRule)) {
                        unset($promotionBundles[$index]);
                    }

                    if ((isset($bundleRule?->type) && $bundleRule?->type != PromotionTypeEnum::BUNDLE_DISCOUNT)) {
                        unset($promotionBundles[$index]);
                        continue;
                    }

                    if (!isset($bundleRule?->get->quantity)) {
                        continue;
                    }
                    if (isset($limitBundleProducts) && count($bundle['product_ids']) > $limitBundleProducts && $limitBundleProducts > 0) {
                        $productIds = array_slice($bundle['product_ids'], 0, $limitBundleProducts);
                        $bundle['product_ids'] = $productIds;
                    }

                    if (!self::validateRelatedCampaignAndProductIdsInBundle($bundle, $orderProducts, $bundleRule)) {
                        unset($promotionBundles[$index]);
                    }
                } else {
                    unset($promotionBundles[$index]);
                }
            }
            $orderProducts = array_map(function ($op) use ($promotionBundles) {
                if (isset($op['product_id']) && isset($op['campaign_id'])) {
                    $campaignBundleExisted = array_filter($promotionBundles, function ($bundle) use ($op) {
                        return in_array($op['product_id'], $bundle['product_ids']) || ($op['campaign_id'] == $bundle['related_campaign_id'] && $op['product_id'] == $bundle['related_product_id']);
                    });
                    $campaignBundleExisted = reset($campaignBundleExisted) ?: [];
                    if (!empty($campaignBundleExisted)) {
                        $op['promotion_rule_id'] = $campaignBundleExisted['promotion_rule_id'];
                        $op['related_campaign_id'] = $campaignBundleExisted['related_campaign_id'];
                        $op['related_product_id'] = $campaignBundleExisted['related_product_id'];
                    }
                }
                return $op;
            }, $orderProducts);
        } catch (Exception $e) {
            logToDiscord('Error in order service handleOrderProductPromotionBundle', $e->getMessage(), 'error', true);
        }
    }

    public static function validateCouponFormByOrderProducts($orderProducts)
    {
        $allowAdditionDiscountCode = true;
        $orderProductPromotionRules = [];
        foreach ($orderProducts as $op) {
            if (isset($op->promotion_rule_id) && !in_array($op?->promotion_rule_id, $orderProductPromotionRules)) {
                $orderProductPromotionRules [] = $op->promotion_rule_id;
            }
        }
        if (count($orderProductPromotionRules) > 1) {
            $allowAdditionDiscountCode = false;
        }
        return $allowAdditionDiscountCode;
    }

    /**
     * @param $bundleDiscount
     * @param $orderProducts
     * $param $promotionRule
     * @param $promotionRule
     * @return bool
     */
    private static function validateRelatedCampaignAndProductIdsInBundle($bundleDiscount, $orderProducts, $promotionRule): bool
    {
        $confirmRelatedCampaign = false;
        $confirmNumberProductId = 0;
        foreach ($orderProducts as $product) {
            if (isset($promotionRule?->get?->same_campaign) &&
                $promotionRule?->get?->same_campaign) {
                if ($product['product_id'] == $bundleDiscount['related_product_id']) {
                    $confirmRelatedCampaign = true;
                    continue;
                }
            } else {
                if ($product['campaign_id'] == $bundleDiscount['related_campaign_id']) {
                    $confirmRelatedCampaign = true;
                    continue;
                }
            }

            if (in_array($product['product_id'], $bundleDiscount['product_ids'])) {
                $confirmNumberProductId += 1;
            }
        }

        if ($confirmNumberProductId != count($bundleDiscount['product_ids'])) {
            return false;
        }
        if ($confirmRelatedCampaign) {
            return true;
        }
        return false;
    }

    /**
     * @param $promotion
     * @param array $orderProducts
     * @param int $productIndex
     * @return array
     * @throws Throwable
     */
    public function mapBundleDiscountInCheckout($promotion, $orderProducts, $productIndex)
    {
        $rules = json_decode($promotion->rules);
        if (empty($rules) ||
            empty($orderProducts[$productIndex]) ||
            empty($orderProducts[$productIndex]['campaign_id'])) {
            return [];
        }

        $orderProduct = $orderProducts[$productIndex];
        $campaignId = $orderProduct['campaign_id'];
        $productId = Arr::get($orderProduct, 'product_id');
        $storeId = Arr::get($orderProduct, 'store_id');
        $limit = count($orderProducts);
        $arrFilter = [];
        $isSameCollection = false;

        //Check if this order product fitted for this promotion, if not - choose others product for targeting
        $targetProductMatched = true;
        if (isset($promotion->campaign_id) && $promotion->campaign_id != $campaignId) {
            $targetProductMatched = false;
        }

        $productCollectionIds = ProductCollection::query()->where(function ($q) use ($campaignId, $productId) {
            $q->where('product_id', $campaignId)
                ->orWhere('product_id', $productId);
        })
            ->pluck('collection_id')
            ->toArray();
        if (isset($promotion->collection_id) &&
            !empty($productCollectionIds) &&
            !in_array($promotion->collection_id, $productCollectionIds)
        ) {
            $targetProductMatched = false;
        }
        if (isset($promotion->store_id) && isset($storeId) && $promotion->store_id != $storeId) {
            $targetProductMatched = false;
        }

        if (!$targetProductMatched) {
            return $this->mapBundleDiscountInCheckout($promotion, $orderProducts, $productIndex + 1);
        }


        if (!empty($rules->get->same_campaign)) {
            $arrFilter['campaign_id'] = $campaignId;
            // unset exclude
            unset($arrFilter['exclude_campaign_ids']);
        } elseif (!empty($rules->get->campaign_id)) {
            $arrFilter['campaign_id'] = $rules->get->campaign_id;
        } elseif (!empty($rules->get->collection_id)) {
            $arrFilter['collection_id'] = $rules->get->collection_id;
        } elseif (!empty($rules->get->same_collection)) {
            $isSameCollection = true;
            $collectionIds = ProductCollection::query()
                ->where('product_id', $campaignId)
                ->get('collection_id');
            $arrFilter['collection_ids'] = $collectionIds->pluck('collection_id')->toArray();
        }

        $templateIds = Template::query()
            ->where('status', ProductStatus::ACTIVE)
            ->pluck('id')
            ->toArray();

        if (!empty($rules->get->template_ids)) {
            $templateIds = array_merge($rules->get->template_ids, $templateIds);
        }

        $templateIds = array_unique($templateIds);
        if (!empty($templateIds)) {
            $arrFilter['template_ids'] = $templateIds;
        }
        $arrFilter ['product_type'] = ProductType::PRODUCT;
        $sort = $rules->get->order_by;
        $arrFilter['product_ids'] = array_column($orderProducts, 'product_id');
        $products = $this->elasticGetProductByBundleDiscount([$campaignId], $arrFilter, $sort, $limit, true, $isSameCollection);
        if (empty($products) && isset($orderProducts[$productIndex + 1])) {
            return $this->mapBundleDiscountInCheckout($promotion, $orderProducts, $productIndex + 1);
        }

        if (empty($products)) {
            return [];
        }

        return [
            'order_product_index' => $productIndex,
            'related_campaign_id' => $campaignId,
            'related_product_id' => $orderProduct['product_id'],
            'products' => $products
        ];
    }

    /**
     * @param $order
     * @param PromotionRule $promotion
     * @param string $discountCode
     * @return int
     * */
    public static function bundleDiscountCodeOnOrderHandler(&$order, $promotion, $discountCode)
    {
        $allowDiscountCode = false;
        $amountProductsNotAppliedBD = 0;
        foreach ($order?->products as $product) {
            $promotionRule = PromotionRule::find($product?->promotion_rule_id);
            $product->setRelation('promotionRule', $promotionRule);
            if (app(Order::class)->checkDiscountAmountBundleDiscount($product)) {
                $allowDiscountCode = true;
                $amountProductsNotAppliedBD += 1;
            }
        }

        $order->discount_code = $discountCode;
        $order->promotion_rule_id = $promotion->id;

        return $amountProductsNotAppliedBD;
    }

    /**
     * @param $order
     * @return void
     * */
    public function bundleDiscountCodeInOrderCreating(&$order)
    {
        $bundleDiscounts = collect([]);
        foreach ($order->products as $op) {
            $promotionRuleId = $op?->promotion?->id;
            if (!isset($promotionRuleId)) {
                continue;
            }
            $bundleExisted = $bundleDiscounts->filter(function ($each) use ($promotionRuleId) {
                return $each?->promotion_rule_id == $promotionRuleId;
            });
            if (isset($bundleExisted)) {
                continue;
            }
            $bundleDiscounts->push([
                'campaign_related_id' => $op?->campaignBundleId,
                'promotion_rule_id' => $op->promotion->id
            ]);
        }

        foreach ($order->products as $op) {
            $productCampaignBundleExisted = $bundleDiscounts->filter(function ($each) use ($op) {
                return ($op->campaign_id == $each->campaign_related_id) ||
                    ($op->campaignBundleId == $each->campaign_related_id && $op->promotion->id == $each->promotion_rule_id);
            });
            if (isset($productCampaignBundleExisted)) {
                $op->related_campaign_id = $productCampaignBundleExisted->campaign_related_id;
                $op->promotion_rule_id = $productCampaignBundleExisted->promotion_rule_id;
            }
        }
    }

    /**
     * @param $storeInfo
     * @param Order|RegionOrders $order
     * @return array
     * @throws Throwable
     */
    public function getPaymentGateways($storeInfo, Order|RegionOrders $order): array
    {
        $gateways = [];
        $orderLocation = SystemLocation::findByCountryCode($order->country);
        $orderType = $order->type;
        if ($orderType === OrderTypeEnum::SERVICE) {
            $storeInfo = StoreService::getCurrentStoreInfo(getMarketPlaceDomain());
        }
        $deviceType = $order->device ?? null;
        $visitInfo = !is_null($order->visit_info) ? json_decode($order->visit_info, true, 512, JSON_THROW_ON_ERROR) : [];
        $visitInfo['checkout_city'] = '';
        if (!empty($order->city)) {
            $visitInfo['checkout_city'] = $order->city;
        }
        $disabledStripe = (int)$storeInfo->stripe_gateway_id < 0 && $deviceType !== 'mobile';
        $disabledPaypal = (int)$storeInfo->paypal_gateway_id < 0 && $deviceType !== 'mobile';
        $stripe = null;
        if (!$disabledStripe) {
            $stripe = $this->getPaymentGatewaysByType(PaymentMethodEnum::STRIPE, $storeInfo, $orderLocation, $deviceType, $orderType, $visitInfo);
        }
        if ($stripe) {
            unset($stripe['config']);
            $this->modifyCheckoutDomain($stripe);
            $gateways[] = $stripe;
        }

        $paypalCheckoutDomain = null;
        $paypal = null;
        if (!$disabledPaypal) {
            $paypal = $this->getPaymentGatewaysByType(PaymentMethodEnum::PAYPAL, $storeInfo, $orderLocation, $deviceType, $orderType, $visitInfo);
        }
        if ($paypal) {
            // check if is a connected account
            if (!empty($paypal['paypal_merchant_id'])) {
                // Client ID is SenPrints Client ID
                $paypal['clientId'] = config('services.paypal.client_id');
            } else if (isset($paypal['config'])) {
                $tempConfig = $paypal['config'];

                if ($paypal['encrypted']) {
                    try {
                        $tempConfig = self::encrypter()->decrypt($tempConfig);
                    } catch (DecryptException $e) {
                        $tempConfig = '';
                    }
                }

                if ($tempConfig) {
                    $paypalConfig = json_decode($tempConfig, true, 512, JSON_THROW_ON_ERROR);
                    $paypal['clientId'] = $paypalConfig['clientId'];
                    unset($paypal['config']);
                }
            }

            $this->modifyCheckoutDomain($paypal);
            $paypalCheckoutDomain = $paypal['checkout_domain'];
            $gateways[] = $paypal;
        }

        // only load TazaPay for SenPrints
        // will enable for other stores later
        if ($storeInfo->id === Store::SENPRINTS_STORE_ID && $order->type !== OrderTypeEnum::SERVICE) {
            $tazapay = $this->getPaymentGatewaysByType(PaymentMethodEnum::TAZAPAY, $storeInfo, $orderLocation, $deviceType, $orderType, $visitInfo);
            if ($tazapay) {
                unset($tazapay['config']);
                array_unshift($gateways, $tazapay);
            }
        }

        return ['gateways' => $gateways, 'paypalCheckoutDomain' => $paypalCheckoutDomain];
    }

    /**
     * @return \Illuminate\Support\Collection
     * @throws Throwable
     */
    public function getCheckoutPaymentGateways(): \Illuminate\Support\Collection
    {
        return SystemConfig::getCheckoutPaymentGateways();
    }

    /**
     * @param string $gatewayType
     * @param $storeInfo
     * @param $orderLocation
     * @param null $deviceType
     * @param null $orderType
     * @param array $visitInfo
     * @return \Illuminate\Support\Collection|mixed|null
     * @throws Throwable
     */
    public function getPaymentGatewaysByType(string $gatewayType, $storeInfo, $orderLocation, $deviceType = null, $orderType = null, array $visitInfo = []): mixed
    {
        $paymentGatewaysByType = $this->allGatewayOfSeller($storeInfo)->where('gateway', $gatewayType);
        if ($paymentGatewaysByType->isEmpty()) {
            return null;
        }
        $isForceLoad = false;
        if ($orderType && in_array($orderType, [OrderTypeEnum::REGULAR, OrderTypeEnum::SERVICE], true)) {
            $visitCity = data_get($visitInfo, 'city');
            $checkoutCity = data_get($visitInfo, 'checkout_city');
            $visitIp = data_get($visitInfo, 'clientIp');
            $paymentGatewaysByTypeOrigin = $paymentGatewaysByType;
            $configs = $this->getCheckoutPaymentGateways();
            $settings = collect($configs)->filter(fn($item) => isset($item['gateway'], $item['device'], $item['payment_gateway_id']) && $item['gateway'] === $gatewayType)->sortBy(function ($item) {
                return [!isset($item['ips']), !isset($item['city'])]; // sort by ips first, then city
            })->values();
            $setting = null;
            if (!is_null($visitCity) || !is_null($visitIp) || !is_null($checkoutCity)) {
                foreach ($settings as $item) {
                    $hasCity = !empty($item['city']);
                    $hasIps = !empty($item['ips']);
                    $cityMatch = $hasCity && !empty($visitCity) && Str::of($item['city'])->explode(',')->map(fn($item) => trim($item))->some(fn($city) => strtolower($visitCity) === strtolower($city));
                    if (!$cityMatch && $hasCity && !empty($checkoutCity)) {
                        $cityMatch = Str::of($item['city'])->explode(',')->map(fn($item) => trim($item))->some(fn($city) => strtolower($checkoutCity) === strtolower($city));
                    }
                    $ipMatch = $hasIps && !empty($visitIp) && Str::of($item['ips'])->explode(',')->some(fn($ip) => str_starts_with($visitIp, $ip));
                    if ($cityMatch && $ipMatch) {
                        $setting = $item;
                        break;
                    }
                    if (($cityMatch && !$hasIps) || ($ipMatch && !$hasCity)) {
                        $setting = $item;
                        break;
                    }
                    if ($cityMatch || $ipMatch) {
                        $setting = $item;
                    }
                }
            }
            if (is_null($setting) && $settings->isNotEmpty()) {
                $setting = $settings->first();
            }
            if ($setting && !empty($setting['payment_gateway_id']) && $deviceType) {
                if (trim($setting['device']) === $deviceType) {
                    $paymentGatewaysByType = $paymentGatewaysByType->where('id', $setting['payment_gateway_id']);
                    $isForceLoad = true;
                } else {
                    $paymentGatewaysByType = $paymentGatewaysByType->where('id', '!=', $setting['payment_gateway_id']);
                }
            }
            if ($paymentGatewaysByType->isEmpty()) {
                $paymentGatewaysByType = $paymentGatewaysByTypeOrigin;
            }
        }
        $paymentGatewaysBySeller = $paymentGatewaysByType->where('seller_id', $storeInfo->seller_id);
        if ($paymentGatewaysBySeller->isEmpty()) {
            $paymentGatewaysBySeller = $paymentGatewaysByType;
        }
        if (!$isForceLoad) {
            $paymentGatewaysBySeller = $this->filterGatewaysBySaleLimit($paymentGatewaysBySeller);
        }
        $paymentGatewaysByStore = $this->getPaymentGatewayByStore($paymentGatewaysBySeller, $storeInfo->id, $deviceType);
        if ($storeInfo->custom_payment) {
            if ($deviceType !== 'mobile') {
                $activeGatewaysIds = [];
                if (!empty($storeInfo->stripe_gateway_id) || !empty($storeInfo->paypal_gateway_id)) {
                    $activeGatewaysIds = $paymentGatewaysByStore->pluck('id')->map(fn($item) => (int)$item)->toArray();
                }
                if (!empty($storeInfo->stripe_gateway_id) && $gatewayType === PaymentMethodEnum::STRIPE && in_array((int)$storeInfo->stripe_gateway_id, $activeGatewaysIds, true)) {
                    $paymentGatewaysByStore = $paymentGatewaysByStore->where('id', $storeInfo->stripe_gateway_id);
                }
                if (!empty($storeInfo->paypal_gateway_id) && $gatewayType === PaymentMethodEnum::PAYPAL && in_array((int)$storeInfo->paypal_gateway_id, $activeGatewaysIds, true)) {
                    $paymentGatewaysByStore = $paymentGatewaysByStore->where('id', $storeInfo->paypal_gateway_id);
                }
            } else if ($paymentGatewaysByStore->isNotEmpty()) {
                if ($gatewayType === PaymentMethodEnum::STRIPE && !empty($storeInfo->stripe_gateway_id)) {
                    $paymentGatewaysByStore = $paymentGatewaysByStore->where('id', '!=', $storeInfo->stripe_gateway_id);
                }
                if ($gatewayType === PaymentMethodEnum::PAYPAL && !empty($storeInfo->paypal_gateway_id)) {
                    $paymentGatewaysByStore = $paymentGatewaysByStore->where('id', '!=', $storeInfo->paypal_gateway_id);
                }
            }
        }

        if (!$isForceLoad) {
            $paymentGatewaysByTrustedSeller = $paymentGatewaysByStore->whereIn('seller_status', [$storeInfo->seller_status, '*']);
            if ($paymentGatewaysByStore->whereIn('seller_status', $storeInfo->seller_status)->isEmpty()) {
                $paymentGatewaysByTrustedSeller = $paymentGatewaysByStore->filter(fn($paymentGateway) => is_null($paymentGateway->seller_status) || $paymentGateway->seller_status === '*');
            }
            $paymentGatewaysByLocation = $paymentGatewaysByTrustedSeller->where('location', $orderLocation->code);
            if ($orderLocation->code !== 'GB' && $paymentGatewaysByLocation->isEmpty()) {
                $paymentGatewaysByLocation = $paymentGatewaysByTrustedSeller->where('location', $orderLocation->region_code);
            }
            if ($paymentGatewaysByLocation->isEmpty()) {
                $paymentGatewaysByLocation = $paymentGatewaysByTrustedSeller->whereNull('location');
            }
            $paymentGateway = !$paymentGatewaysByLocation->isEmpty() ? $this->getGatewayByWeight($paymentGatewaysByLocation) : null;
            if (is_null($paymentGateway) || ($paymentGateway->rate_limit && random_int(0, 100) > $paymentGateway->rate_limit)) {
                $paymentId = !is_null($paymentGateway) ? $paymentGateway->id : 0;
                $paymentGatewaysDefault = $paymentGatewaysByLocation->where('id', '!=', $paymentId);

                if ($paymentGatewaysDefault->isEmpty()) {
                    $paymentGatewaysDefault = $paymentGatewaysByTrustedSeller->where('id', '!=', $paymentId)->whereNull('location');
                }

                if ($paymentGatewaysDefault->isEmpty()) {
                    $paymentGatewaysDefault = $paymentGatewaysByStore->where('id', '!=', $paymentId)->whereNull('location');
                }

                if (!$paymentGatewaysDefault->isEmpty()) {
                    $paymentGateway = $paymentGatewaysDefault->random();
                }
            }
        } else {
            $paymentGateway = !$paymentGatewaysByStore->isEmpty() ? $this->getGatewayByWeight($paymentGatewaysByStore) : null;
        }
        return $paymentGateway;
    }

    /**
     * @param $store
     *
     * @return Collection
     * @throws Exception
     */
    public function allGatewayOfSeller($store): Collection
    {
        return PaymentGateway::getPaymentGatewaysBySeller($store);
    }

    /**
     * @param Collection $gateways
     * @return Collection
     */
    private function filterGatewaysBySaleLimit(Collection $gateways): Collection
    {
        try {
            $result = $gateways->filter(
                fn($gateway) => !$gateway->hold_at || Carbon::parse($gateway->hold_at)->lt(now())
            );

            // if no result, return all gateways to prevent error
            if ($result->isEmpty()) {
                return $gateways;
            }

            return $result;
        } catch (Throwable $e) {
            return $gateways;
        }
    }

    /**
     * @param $paymentGatewaysBySeller
     * @param $storeInfoId
     * @param $deviceType
     * @return Collection
     */
    public function getPaymentGatewayByStore($paymentGatewaysBySeller, $storeInfoId, $deviceType): Collection
    {
        $paymentGatewayBySellerIds = $paymentGatewaysBySeller->pluck('id')->toArray();
        $gatewayExistsByType = $this->getStorePaymentGatewayById($paymentGatewayBySellerIds);
        $gatewayExistsIncluded = !empty($gatewayExistsByType[StorePaymentGatewayTypeEnum::INCLUDE]) ? $gatewayExistsByType[StorePaymentGatewayTypeEnum::INCLUDE] : collect();
        $gatewayExistsExcluded = !empty($gatewayExistsByType[StorePaymentGatewayTypeEnum::EXCLUDE]) ? $gatewayExistsByType[StorePaymentGatewayTypeEnum::EXCLUDE]->where('store_id', $storeInfoId) : collect();
        $paymentGatewayFilteredIds = $paymentGatewayBySellerIds;
        if ($gatewayExistsIncluded->isNotEmpty()) {
            $paymentGatewayFilteredIds = $gatewayExistsIncluded->where('store_id', $storeInfoId)->pluck('payment_gateway_id')->toArray();
            if (empty($paymentGatewayFilteredIds)) {
                $paymentGatewayFilteredIds = array_values(array_diff($paymentGatewayBySellerIds, $gatewayExistsIncluded->pluck('payment_gateway_id')->toArray()));
            }
        }
        if ($gatewayExistsExcluded->isNotEmpty()) {
            $paymentGatewayFilteredIds = array_values(array_diff($paymentGatewayFilteredIds, $gatewayExistsExcluded->pluck('payment_gateway_id')->toArray()));
        }
        if ($deviceType === 'mobile') {
            $paymentGatewayInUsed = $this->getPaymentGatewaysAlreadyUsed($paymentGatewayBySellerIds, $storeInfoId);
            if (count($paymentGatewayInUsed) > 0) {
                $paymentGatewayFilteredIds = array_values(array_diff($paymentGatewayFilteredIds, $paymentGatewayInUsed));
            }
        }
        return $paymentGatewaysBySeller->whereIn('id', $paymentGatewayFilteredIds);
    }

    /**
     * @param $paymentGatewayBySellerIds
     * @return Collection
     */
    public function getStorePaymentGatewayById($paymentGatewayBySellerIds)
    {
        return StorePaymentGateway::query()
            ->select('payment_gateway_id', 'store_id', 'type')
            ->whereIn('payment_gateway_id', $paymentGatewayBySellerIds)
            ->get()
            ->groupBy('type');
    }

    /**
     * @param $paymentGatewayBySellerIds
     * @param $storeInfoId
     * @return array
     */
    public function getPaymentGatewaysAlreadyUsed($paymentGatewayBySellerIds, $storeInfoId): array
    {
        $gatewaysWasUsed = Store::query()
            ->selectRaw("CONCAT(GROUP_CONCAT(DISTINCT stripe_gateway_id), ',', GROUP_CONCAT(DISTINCT paypal_gateway_id)) AS gateway_ids")
            ->where(function ($query) use ($paymentGatewayBySellerIds) {
                $query->whereIn('stripe_gateway_id', $paymentGatewayBySellerIds)->orWhereIn('paypal_gateway_id', $paymentGatewayBySellerIds);
            })
            ->where('status', StoreStatusEnum::ACTIVE)
            ->where('id', '!=', $storeInfoId)
            ->value('gateway_ids');
        if (!$gatewaysWasUsed) {
            return [];
        }
        return Str::of($gatewaysWasUsed)->explode(',')->filter(fn($item) => (int)$item > 0)->map(fn($item) => (int)$item)->unique()->toArray();
    }

    /**
     * Get one gateway by weight
     *
     * @param Collection $gateways
     * @return Collection|null
     */
    public function getGatewayByWeight(Collection $gateways)
    {
        try {
            if (!$gateways->count()) {
                return null;
            }

            $totalWeight = $gateways->sum('weight');

            // check if no gateway has weight
            if ($totalWeight === 0) {
                return $gateways->random();
            }

            $randomWeight = random_int(1, $totalWeight);

            foreach ($gateways as $gateway) {
                $randomWeight -= $gateway->weight;

                if ($randomWeight <= 0) {
                    return $gateway;
                }
            }

            return $gateways->random();
        } catch (Throwable $e) {
            return $gateways->random();
        }
    }

    private function modifyCheckoutDomain(&$gateway): void
    {
        if (!empty($gateway['checkout_domain'])) {
            $gateway['checkout_domain'] = self::getDomainByStoreId($gateway['checkout_domain']);
        }
    }

    /**
     * @param $orderOptions
     * @param $product
     * @param $logs
     * @return false|mixed
     */
    public static function correctOptions($orderOptions, $product, &$logs = '')
    {
        $options = json_decode($product->options, true);
        if ($product->full_printed === ProductPrintType::HANDMADE && !empty($options['custom_options'])) {
            unset($options['custom_options']);
        }
        if ($product->full_printed === ProductPrintType::HANDMADE && !empty($options['common_options'])) {
            unset($options['common_options']);
        }
        if (empty($options)) {
            if (empty($orderOptions)) {
                return $orderOptions;
            }

            $logs .= 'No option found';
            return false;
        }

        if (empty($orderOptions)) {
            return false;
        }

        $correct = true;
        foreach ($orderOptions as $key => $value) {
            if (isset($options[$key])) {
                $checkValue = correctOptionValue($value);
                if (!in_array($checkValue, $options[$key])) {
                    $logs .= 'Option ' . $key . '=' . $checkValue . ' is invalid, ';
                    $correct = false;
                }
                $options[$key] = $value;
                unset($orderOptions[$key]);
            }
        }
        foreach ($options as $key => $value) {
            if (is_array($value)) {
                if (count($value) === 1 || ($product->isFullPrintedType() && $key === 'color') || $key === 'pack') {
                    $options[$key] = $value[0];
                } else {
                    $correctOption = false;
                    foreach ($orderOptions as $key2 => $value2) {
                        if (in_array($value2, $value)) {
                            $options[$key] = $value2;
                            unset($orderOptions[$key2]);
                            $correctOption = true;
                            break;
                        }
                    }
                    if (!$correctOption) {
                        $logs .= 'Option ' . $key . ' is required, ';
                        $correct = false;
                    }
                }
            }
        }
        return $correct ? $options : false;
    }

    /**
     * @param $orderCustomOptions
     * @param $product
     * @param string $logs
     * @return bool|array|null
     * @throws Throwable
     */
    public static function correctCustomOptions($orderCustomOptions, $product, string &$logs = ''): bool|null|array
    {
        $options = json_decode($product->options, true, 512, JSON_THROW_ON_ERROR);
        if (empty($options) || empty($options['custom_options']) || empty($orderCustomOptions)) {
            return false;
        }
        $customOptionsGroup = $options['custom_options'];
        $limit = 1;
        if (isset($customOptionsGroup['group'])) {
            $limit = (int)($customOptionsGroup['group']['limit'] ?? 1);
            unset($customOptionsGroup['group']);
        }
        $realCustomOptions = [];
        $customOptions = [];
        if (isset($customOptionsGroup['options'])) {
            $realCustomOptions = $customOptionsGroup['options'];
        }
        if (empty($realCustomOptions)) {
            return false;
        }
        foreach ($realCustomOptions as $option) {
            $customOptions[Str::lower($option['type'] . ':' . $option['label'])] = [
                'type' => Str::lower($option['type']),
                'label' => $option['label'],
                'value' => $option['value'],
                'price' => (float)($option['price'] ?? 0),
                'unrequired' => (bool)($option['unrequired'] ?? 0),
                'max_length' => (int)($option['max_length'] ?? 255),
            ];
        }
        $correct = true;
        $actualCustomOptions = [];
        $validCustomKeys = [];
        foreach ($orderCustomOptions as $key => $value) {
            $_option = [];
            $key = Str::lower($key);
            $group = 1;
            if (preg_match('/custom_options:(\d+):/', $key, $matches)) {
                $group = (int)$matches[1];
            }
            if ($group > $limit) {
                $logs .= 'Option ' . $key . ' of group ' . $group . ' is invalid, ';
                $correct = false;
                continue;
            }
            $checkKey = str_replace(['custom_options:' . $group . ':', 'custom_options:'], '', $key);
            if (array_key_exists($checkKey, $customOptions)) {
                $optionType = $customOptions[$checkKey]['type'];
                $optionValue = $customOptions[$checkKey]['value'];
                $optionLabel = $customOptions[$checkKey]['label'];
                $optionPrice = $customOptions[$checkKey]['price'];
                $optionUnRequired = $customOptions[$checkKey]['unrequired'];
                $optionMaxLength = $customOptions[$checkKey]['max_length'];
                $checkValue = correctOptionValue($value);
                if (is_array($optionValue) && $value !== '' && !is_null($value) && !in_array($checkValue, $optionValue)) {
                    $logs .= 'Option ' . $key . '=' . $checkValue . ' is invalid, ';
                    $correct = false;
                }
                if (!$optionUnRequired && ($value === '' || is_null($value))) {
                    $logs .= 'Option ' . $key . ' is required, ';
                    $correct = false;
                }
                if (!empty($value) && $optionType === 'text' && strlen($value) > $optionMaxLength) {
                    $logs .= 'Option ' . $key . ' is too long, max length is ' . $optionMaxLength . ', ';
                    $correct = false;
                }
                $_option['type'] = $optionType;
                $_option['label'] = $optionLabel;
                $_option['price'] = $optionPrice;
                $_option['value'] = $value;
                $_option['unrequired'] = $optionUnRequired;
                $_option['max_length'] = $optionType === 'text' ? $optionMaxLength : null;
                if ($optionType === 'text') {
                    $_option['placeholder'] = $optionValue;
                }
                if ($optionType === 'dropdown') {
                    $_option['options'] = $optionValue;
                }
                if ($optionType === 'image') {
                    $_option['value'] = '';
                    $_option['imagePath'] = '';
                    $imageUrl = $value;
                    $maxRetried = 1;
                    while (true) {
                        $imageUploaded = UploadController::uploadS3FromDirectLink($imageUrl, 'tmp/');
                        if (!empty($imageUploaded) || $maxRetried > 5) {
                            break;
                        }
                        $maxRetried++;
                    }
                    if (empty($imageUploaded)) {
                        $logs .= 'Option ' . $key . ' has invalid image or can not download, ';
                        $correct = false;
                        continue;
                    }
                    $_option['value'] = $imageUploaded['path'];
                    $_option['imagePath'] = $imageUploaded['path'];
                }
                $actualCustomOptions[$group - 1][] = $_option;
                $validCustomKeys[] = $checkKey;
                unset($orderCustomOptions[$key]);
            }
        }
        foreach ($customOptions as $key => $value) {
            if (!$value['unrequired'] && !in_array($key, $validCustomKeys, true)) {
                $logs .= 'Option ' . $key . ' is required, ';
                $correct = false;
            }
        }
        return $correct && !empty($actualCustomOptions) ? $actualCustomOptions : false;
    }

    /**
     * @param $orderId
     * @param $log
     * @param $supplier
     * @param string $color
     * @param string $channel_log
     * @return void
     */
    public static function logToDiscordHouseNumberInAddress($orderId, $supplier, $log, string $color = '5763719', string $channel_log = 'remove_house_number_in_address')
    {
        $message = "Order ID: #" . $orderId . '. Supplier id: ' . $supplier . PHP_EOL;
        $embedDesc = [
            [
                'description' => $message . $log,
                'color' => $color
            ]
        ];
        $msg = '';
        if ($color === '15548997' && app()->isProduction()) {
            $msg .= mentionDiscord(DiscordUserIdEnum::BIZDEV);
        }
        logToDiscord($msg, $channel_log, embeds: $embedDesc);
    }

    /**
     * @param PromotionRule|null $promotion
     * @return int|null
     */
    public static function orderPromotionGetLimitBdProduct(PromotionRule|null $promotion): int|null
    {
        try {
            if (!isset($promotion)) {
                return null;
            }
            $promotionRules = json_decode($promotion?->rules);
            $limitBundleProducts = $promotionRules?->get?->number_cart_bundle_product_limit ?? null;
            $limitBundleProducts = (!isset($limitBundleProducts) || $limitBundleProducts === 'null') ? null : intval($limitBundleProducts);
            return $limitBundleProducts;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * @param $promotionRule
     * @param $orderProducts
     * @param array $productIdsInBD
     * @param int $relatedCampaignId
     * @param int|null $relatedProductId
     * @return int
     */
    public static function calculateNumberProductsFollowBundle($promotionRule, $orderProducts, array $productIdsInBD, int $relatedCampaignId, int|null $relatedProductId = null): int
    {
        $numProductsFollowBundle = 0;
        foreach ($orderProducts as $op) {
            if (!empty($promotionRule->get->same_campaign)) {
                if (in_array($op->product_id, $productIdsInBD) && $op->campaign_id == $relatedCampaignId && $op->product_id != $relatedProductId) {
                    $numProductsFollowBundle += 1;
                }
            } else if (in_array($op->product_id, $productIdsInBD) && $op->campaign_id != $relatedCampaignId) {
                $numProductsFollowBundle += 1;
            }
        }
        return $numProductsFollowBundle;
    }

    /**
     * @param PromotionRule $promotion
     * @param $orderProducts
     * @param array $productIdsInBD
     * @param int $relatedCampaignId
     * @param int|null $relatedProductId
     * @return bool
     */
    public static function checkSatifyMinNumberProductInBd(PromotionRule $promotion, $orderProducts, array $productIdsInBD, int $relatedCampaignId, int|null $relatedProductId = null): bool
    {
        $promotionRules = json_decode($promotion?->rules);
        $numberProductFollowBd = self::calculateNumberProductsFollowBundle($promotionRules, $orderProducts, $productIdsInBD, $relatedCampaignId, $relatedProductId);
        $minBundleProducts = $promotionRules?->get?->quantity ?? 0;
        return !isset($minBundleProducts) || (int)$minBundleProducts <= (int)$numberProductFollowBd;
    }

    /**
     * @param OrderProduct $order_product
     * @param $order_type
     * @return OrderProduct
     */
    public static function getAndRemoveUnusedCustomOptions(OrderProduct $order_product, $order_type)
    {
        try {
            if (empty($order_product->custom_options) || $order_product->custom_options === 'null') {
                $order_product->custom_options = null;
                return $order_product;
            }
            if (in_array($order_type, ['fulfillment', 'service', 'fba'], true)) {
                return $order_product;
            }
            if (isset($order_product->personalized, $order_product->full_printed) && ($order_product->personalized === PersonalizedType::CUSTOM_OPTION || $order_product->full_printed === ProductPrintType::HANDMADE)) {
                if (!Str::isJson($order_product->custom_options)) {
                    return $order_product;
                }
                $options = json_decode($order_product->custom_options, true, 512, JSON_THROW_ON_ERROR);
                if (!is_array($options)) {
                    return $order_product;
                }
                if (empty($options)) {
                    return $order_product;
                }
                self::removeUnusedCustomOptions($options);
                $order_product->custom_options = json_encode($options, JSON_THROW_ON_ERROR);
                return $order_product;
            }
            return $order_product;
        } catch (Throwable $e) {
            return $order_product;
        }
    }

    /**
     * @param $options
     */
    private static function removeUnusedCustomOptions(&$options): void
    {
        $removeKeys = [
            'price',
            'unrequired',
            'placeholder',
            'max_length',
            'g_index',
            'g_type',
        ];
        if (is_array($options)) {
            foreach ($options as &$value) {
                if (is_array($value)) {
                    self::removeUnusedCustomOptions($value);
                }
                foreach ($removeKeys as $key) {
                    data_forget($value, $key);
                }
            }
        }
    }

    public static function keywordsParser($keywords): array
    {
        $raws = explode(',', $keywords);
        $orderIds = [];
        $orderNumbers = [];
        $orderIdRegex = '/^\d+$/';
        $orderNumberRegex = '/^[a-zA-Z0-9]+-\d+$/';
        foreach ($raws as $raw) {
            $raw = trim($raw);
            if (preg_match($orderIdRegex, $raw)) {
                $orderIds[] = (int)$raw;
                continue;
            }

            if (preg_match($orderNumberRegex, $raw)) {
                $orderNumbers[] = $raw;
            }
        }

        return [$orderIds, $orderNumbers];
    }

    /**
     * Trả profit cho seller và artist seller sau khi order được xử lý các vấn đề liên quan đến design xong
     *
     * @param Order $order
     * @return bool
     */
    public static function payProfitsForSellerArtist(Order $order)
    {
        if ($order->type !== OrderTypeEnum::REGULAR) {
            return false;
        }
        try {
            $cacheKey = md5('process_pay_profit_order_' . $order->id);
            if (cache()->has($cacheKey)) {
                return false;
            }
            cache()->put($cacheKey, $order->id, 10);
            // Tính toán lại giá vận chuyển và lợi nhuận của sup cho đơn hàng
            OrderCostStatisticsJob::dispatch($order->id);
            $profits = $order->getSellerArtistProfits();
            if (count($profits) > 0) {
                foreach ($profits as $sellerId => $profit) {
                    $seller = User::query()->firstWhere('id', $sellerId);
                    if ($seller === null) {
                        continue;
                    }
                    if ($sellerId === $order->seller_id) {
                        $detail = 'Profit of order #' . $order->order_number;
                    } else {
                        $detail = 'Artist commission of order #' . $order->order_number;
                    }
                    $isPaid = $seller->isExistsActionUpdateBalance(array(
                        'seller_id' => $seller->id,
                        'order_id' => $order->id,
                        'type' => SellerBillingType::COMMISSION,
                        'status' => SellerBillingStatus::COMPLETED,
                        'amount' => $profit,
                        'detail' => $detail,
                    ));
                    if (!$isPaid) {
                        $seller->updateBalance(
                            $profit,
                            SellerBillingType::COMMISSION,
                            $detail,
                            $order->id,
                            SellerBillingStatus::COMPLETED,
                            null,
                            null,
                            $order->paid_at
                        );
                        if ($seller->hold_amount > 0) {
                            $seller->updateHoldAmount($profit);
                        }
                    }
                }
                return true;
            }
            return false;
        } catch (\Throwable $e) {
            logException($e, 'Pay profits for seller and artist failed, Order ID #' . $order->id);
            return false;
        }
    }

    /**
     * @param Order $order
     * @return float
     */
    public static function getAmountLimitChargeFulfillOrder(Order $order): float
    {
        $balance_2_fulfill_limit = 0.5; // per item
        try {
            $seller = $order->seller;
            $seller->loadMissing(['ads_logs' => function ($query) {
                $query->select('seller_id', 'ads_campaign_id')->with(['ads_campaign' => function ($query) {
                    $query->select('id', 'options', 'expired_at')->isRunning();
                }]);
            }]);
            if ($seller->ads_logs->isNotEmpty()) {
                $register_ads = $seller->ads_logs->loadMissing('ads_campaign');
                foreach ($register_ads as $ads_campaign) {
                    $ads_campaign = $ads_campaign->ads_campaign;
                    if ($ads_campaign && $ads_campaign->options && Str::isJson($ads_campaign->options) && $ads_campaign->expired_at && $ads_campaign->expired_at->gte(now())) {
                        $ads_campaign_options = json_decode($ads_campaign->options, true, 512, JSON_THROW_ON_ERROR);
                        if (isset($ads_campaign_options['charge_per_item']) && (float)$ads_campaign_options['charge_per_item'] > 0) {
                            $balance_2_fulfill_limit = (float)$ads_campaign_options['charge_per_item'];
                            break;
                        }
                    }
                }
            }
            return $balance_2_fulfill_limit;
        } catch (\Throwable $e) {
            logException($e, 'Get amount limit charge fulfill order failed, Order ID #' . $order->id);
            return $balance_2_fulfill_limit;
        }
    }

    /**
     * @param Order|RegionOrders $order
     * @return bool
     */
    public static function processCustomOrder(Order|RegionOrders $order): bool
    {
        try {
            dispatch(new ProcessChargeCustomOrderJob($order));
            return true;
        } catch (Throwable $exception) {
            logException($exception, 'Process custom order failed, Order ID #' . $order->id);
            return false;
        }
    }

    /**
     * @param Order|RegionOrders $order
     * @return bool
     */
    public static function processFulfillOrder(Order|RegionOrders $order): bool
    {
        try {
            dispatch(new ProcessChargeFulfillOrderJob($order));
            return true;
        } catch (Throwable $exception) {
            logException($exception, 'Process fulfill order failed, Order ID #' . $order->id);
            return false;
        }
    }

    /**
     * @param Order|RegionOrders $order
     * @return bool
     */
    public static function chargeOrderCustomServiceFee(Order|RegionOrders $order): bool
    {
        try {
            if ($order->type !== OrderTypeEnum::CUSTOM || $order->sen_fulfill_status !== OrderSenFulfillStatus::PENDING || !$order->isCustomServiceOrder()) {
                return false;
            }
            $seller = $order->seller;
            $original_order = Order::query()->whereKey($order->order_number_2)->where('seller_id', $order->seller_id)->first();
            if (!$original_order) {
                return false;
            }
            $chargeSellerAmount = $order->total_fulfill_fee;
            $charge_note = optional($order->products->first())->product_name ?? 'Extra service fee';
            $fulfill_detail = $charge_note . ' #' . $order->order_number . ' (' . $original_order->order_number . ')';
            $isChargedFulfillFee = $seller->isExistsActionUpdateBalance(array(
                'seller_id' => $seller->id,
                'order_id' => $order->id,
                'type' => SellerBillingType::FULFILL,
                'status' => SellerBillingStatus::COMPLETED,
                'detail' => $fulfill_detail,
            ));
            if ($isChargedFulfillFee) {
                Order::query()->where([
                    'id' => $order->id,
                    'sen_fulfill_status' => OrderSenFulfillStatus::PENDING,
                ])->update([
                    'status' => OrderStatus::COMPLETED,
                    'fulfill_status' => OrderFulfillStatus::FULFILLED,
                    'sen_fulfill_status' => OrderSenFulfillStatus::YES,
                    'processing_fee_paid' => $order->processing_fee,
                    'fulfill_fee_paid' => $order->total_fulfill_fee
                ]);
                OrderProduct::query()->where([
                    'order_id' => $order->id,
                    'sen_fulfill_status' => OrderSenFulfillStatus::PENDING,
                ])->update([
                    'sen_fulfill_status' => OrderSenFulfillStatus::YES,
                    'fulfill_status' => OrderProductFulfillStatus::FULFILLED
                ]);
                return true;
            }
            $transaction = $seller->updateBalance(-($chargeSellerAmount), SellerBillingType::FULFILL, $fulfill_detail, $order->id);
            if (!$transaction) {
                if (app()->isProduction()) {
                    logToDiscord(mentionDiscord(DiscordUserIdEnum::THANGNM) . " Đơn này chưa charge được tiền seller. Order ID: #" . $order->id, channel: DiscordChannel::ORDER_CUSTOM_SERVICE);
                }
                return false;
            }
            Order::query()->where('id', $order->id)->update([
                'status' => OrderStatus::COMPLETED,
                'fulfill_status' => OrderFulfillStatus::FULFILLED,
                'sen_fulfill_status' => OrderSenFulfillStatus::YES,
                'processing_fee_paid' => $order->processing_fee,
                'fulfill_fee_paid' => $order->total_fulfill_fee
            ]);
            OrderProduct::query()->where([
                'order_id' => $order->id
            ])->update([
                'sen_fulfill_status' => OrderSenFulfillStatus::YES,
                'fulfill_status' => OrderProductFulfillStatus::FULFILLED
            ]);
            OrderHistory::insertLog(
                $original_order,
                OrderHistoryActionEnum::CREATED_CHARGE_EXTRA_FEE,
                $charge_note . ' #' . $order->order_number . ', Charge fee: ' . UserService::formatCurrency($order->total_amount),
                OrderHistoryDisplayLevelEnum::CUSTOMER
            );
            return true;
        } catch (Throwable $e) {
            logException($e, 'Charge order custom service fee failed, Order ID #' . $order->id);
            return false;
        }
    }

    /**
     * @param Order|RegionOrders $order
     * @return bool
     */
    public static function chargeOrderServiceAndFulfillFee(Order|RegionOrders $order): bool
    {
        try {
            if ($order->type !== OrderTypeEnum::CUSTOM || $order->sen_fulfill_status !== OrderSenFulfillStatus::PENDING) {
                return false;
            }
            $balance_2_fulfill_limit = self::getAmountLimitChargeFulfillOrder($order); // per item
            $seller = $order->seller;
            $paid_at = !empty($order->paid_at) ? $order->paid_at : now();
            $profits = [];
            $order->products->map(function ($orderProduct) use ($order, &$profits) {
                if ($orderProduct->sen_fulfill_status !== OrderSenFulfillStatus::PENDING) {
                    return;
                }
                if (!empty($orderProduct->seller_id) && $orderProduct->seller_id !== $order->seller_id && $orderProduct->artist_profit) {
                    if (empty($profits[$orderProduct->seller_id])) {
                        $profits[$orderProduct->seller_id] = $orderProduct->artist_profit;
                    } else {
                        $profits[$orderProduct->seller_id] += $orderProduct->artist_profit;
                    }
                }
            });
            $service_detail = 'Service fee #' . $order->order_number;
            $fulfill_detail = 'Fulfill order #' . $order->order_number;
            $processing_fee = $order->processing_fee; // service fee
            $isChargedServiceFee = $seller->isExistsActionUpdateBalance(array(
                'seller_id' => $seller->id,
                'order_id' => $order->id,
                'type' => SellerBillingType::FEE,
                'status' => SellerBillingStatus::COMPLETED,
                'detail' => $service_detail,
            ));
            if (!$isChargedServiceFee) {
                $isChargedServiceFee = $seller->updateBalance(-($processing_fee), SellerBillingType::FEE, $service_detail, $order->id);
            }
            $charged_balance_2 = 0;
            if ($seller->hasBalance2CanUse()) {
                $isChargedBalance2 = $seller->isExistsActionUpdateBalance(array(
                    'seller_id' => $seller->id,
                    'order_id' => $order->id,
                    'type' => SellerBillingType::FULFILL,
                    'status' => SellerBillingStatus::COMPLETED,
                    'detail' => $fulfill_detail,
                    'balance_type' => SellerBalanceTypeEnum::BALANCE_2
                ));
                if (!$isChargedBalance2) {
                    $charged_balance_2 = $seller->updateBalance2(-($balance_2_fulfill_limit * $order->total_quantity), SellerBillingType::FULFILL, $fulfill_detail, (int)$order->id);
                }
            }
            $isChargedFulfillFee = $seller->isExistsActionUpdateBalance(array(
                'seller_id' => $seller->id,
                'order_id' => $order->id,
                'type' => SellerBillingType::FULFILL,
                'status' => SellerBillingStatus::COMPLETED,
                'detail' => $fulfill_detail,
            ));
            if (!$isChargedFulfillFee) {
                $fulfill_fee = $order->total_fulfill_fee + $charged_balance_2; // fulfill fee
                $isChargedFulfillFee = $seller->updateBalance(-($fulfill_fee), SellerBillingType::FULFILL, $fulfill_detail, $order->id, last_order_at: $paid_at);
            }
            if (!empty($profits)) {
                foreach ($profits as $sellerId => $profit) {
                    $artistSeller = User::query()->firstWhere('id', $sellerId);
                    if (!$artistSeller) {
                        continue;
                    }
                    $detail = 'Artist commission of order #' . $order->order_number;
                    $isPaidArtistProfit = $seller->isExistsActionUpdateBalance(array(
                        'seller_id' => $artistSeller->id,
                        'order_id' => $order->id,
                        'type' => SellerBillingType::COMMISSION,
                        'status' => SellerBillingStatus::COMPLETED,
                        'detail' => $detail,
                    ));
                    if (!$isPaidArtistProfit) {
                        $artistSeller->updateBalance($profit, SellerBillingType::COMMISSION, $detail, $order->id);
                    }
                }
            }
            if ($isChargedServiceFee && $isChargedFulfillFee) {
                Order::query()->where('id', $order->id)->update([
                    'sen_fulfill_status' => OrderSenFulfillStatus::YES,
                    'processing_fee_paid' => $order->processing_fee,
                    'fulfill_fee_paid' => $order->total_fulfill_fee
                ]);
                OrderProduct::query()->where([
                    'order_id' => $order->id,
                    'sen_fulfill_status' => OrderSenFulfillStatus::PENDING
                ])->update([
                    'sen_fulfill_status' => OrderSenFulfillStatus::YES
                ]);
            }
            // Tính toán lại giá vận chuyển và lợi nhuận của sup cho đơn hàng
            OrderCostStatisticsJob::dispatch($order->id);
            return true;
        } catch (Throwable $exception) {
            logException($exception, 'Charge order service and fulfill fee failed, Order ID #' . $order->id);
            return false;
        }
    }

    /**
     * @param $amount
     * @param string $detail
     * @param null $originalOrder
     * @param bool $createWithSenstore
     * @param int $sellerAmount
     * @return Order
     */
    public static function createServiceOrder($amount, $detail = 'Extra Service', $originalOrder = null, $createWithSenstore = false, $sellerAmount = 0): Order
    {
        $data = [
            'access_token' => OrderHelper::generateAccessToken($detail),
            'type' => OrderTypeEnum::SERVICE,
            'store_id' => Store::SENPRINTS_STORE_ID,
            'seller_id' => User::SENPRINTS_SELLER_ID,
        ];
        $isCustomOrder = false;
        if ($originalOrder) {
            /** @var Order $originalOrder */
            $isCustomOrder = !$createWithSenstore && $originalOrder->isCustomOrder();
            if ($isCustomOrder) {
                $data['type'] = OrderTypeEnum::CUSTOM;
                $data['seller_id'] = $originalOrder->seller_id;
            }
            $store = Store::query()->select(['id', 'domain'])
                ->where('status', StoreStatusEnum::ACTIVE)
                ->where('domain', $originalOrder->store_domain)
                ->where('seller_id', $originalOrder->seller_id)
                ->first();
            $data['order_number_2'] = $originalOrder->id; // order number of original order
            $data['store_id'] = $originalOrder->store_id;
            $data['store_domain'] = $originalOrder->store_domain;
            if ($store && (int)$store->id !== (int)$originalOrder->store_id) {
                $data['store_id'] = $store->id;
            }
            $data['store_name'] = $originalOrder->store_name;
            $data['customer_name'] = $originalOrder->customer_name;
            $data['customer_email'] = $originalOrder->customer_email;
            $data['customer_phone'] = $originalOrder->customer_phone;
            $data['address'] = $originalOrder->address;
            $data['address_2'] = $originalOrder->address_2;
            $data['city'] = $originalOrder->city;
            $data['state'] = $originalOrder->state;
            $data['postcode'] = $originalOrder->postcode;
            $data['country'] = $originalOrder->country;
        }

        if ($createWithSenstore) {
            $systemConfigDomain = SystemConfig::getConfig('preview_domain', 'senstores.com');
            $defaultStore = Store::query()->select(['id', 'domain', 'name'])->where('domain', $systemConfigDomain)->first() ?? Store::query()->select(['id', 'domain', 'name'])->find(Store::SENPRINTS_STORE_ID);
            $data['store_id'] = $defaultStore->id;
            $data['store_domain'] = $defaultStore->domain;
            $data['store_name'] = $defaultStore->name;
        }

        $newOrder = Order::query()->create($data);
        $serviceData = [
            'order_id' => $newOrder->id,
            'product_name' => $detail,
            'price' => $amount,
            'quantity' => 1
        ];

        $orderProduct = OrderProduct::query()->create($serviceData);
        $newOrder->products = collect([$orderProduct]);
        $newOrder->calculateOrder();
        if ($isCustomOrder) {
            if ($sellerAmount) {
                $newOrder->total_fulfill_fee = $sellerAmount;
            } else {
                $newOrder->total_fulfill_fee = $amount;
            }
        }
        unset($newOrder->products);
        $newOrder->save();
        if (!empty($originalOrder->region)) {
            SyncOrderToRegion::dispatch($newOrder, $originalOrder->region)->onQueue('sync_order_region');
        }
        return $newOrder;
    }

    /**
     * @param $start
     * @param $end
     * @param array $orderId
     * @return bool
     */
    public static function syncDataOrdersFromUsToSg($start = null, $end = null, array $orderId = [])
    {
        try {
            $onConnection = 'mysql_main_us';
            $chunk = 100;
            if (empty($orderId) && empty($start) && empty($end)) {
                return false;
            }
            // Lấy danh sách order trong khoảng thời gian bị mất từ DB US
            $orders = Order::on($onConnection)->when(!empty($orderId), function ($query) use ($orderId) {
                $query->whereIn('id', $orderId);
            }, function ($query) use ($start, $end) {
                $query->where(function ($query) use ($start) {
                    $query->where('paid_at', '>=', $start)->orWhere('created_at', '>=', $start);
                })->where(function ($query) use ($end) {
                    $query->where('paid_at', '<=', $end)->orWhere('created_at', '<=', $end);
                });
            })->get();
            if ($orders->isEmpty()) {
                return true;
            }
            $orderIds = $orders->pluck('id')->toArray();
            graylogInfo('Start sync orders from US to SG', [
                'category' => 'sync_order_us_to_sg',
                'start' => $start,
                'end' => $end,
                'orderIds' => $orderIds,
            ]);
            // Lấy dữ liệu các bảng order, order_product, design, order_history
            $orderProducts = OrderProduct::on($onConnection)->whereIn('order_id', $orderIds)->get();
            $designs = Design::on($onConnection)->whereIn('order_id', $orderIds)->get();
            $orderHistories = OrderHistory::on($onConnection)->whereIn('order_id', $orderIds)->get();

            // Xóa dữ liệu trên db SG trước khi đồng bộ về tránh bị duplicate
            DB::beginTransaction();
            $deletedOrders = Order::query()->whereIn('id', $orderIds)->withTrashed()->forceDelete();
            $deletedOrderProducts = OrderProduct::query()->whereIn('order_id', $orderIds)->withTrashed()->forceDelete();
            $deletedDesigns = Design::query()->whereIn('order_id', $orderIds)->forceDelete();
            $deletedOrderHistories = OrderHistory::query()->whereIn('order_id', $orderIds)->forceDelete();
            graylogInfo('Start sync orders from US to SG', [
                'category' => 'sync_order_us_to_sg',
                'deleted_orders' => $deletedOrders ? 1 : 0,
                'deleted_order_products' => $deletedOrderProducts ? 1 : 0,
                'deleted_designs' => $deletedDesigns ? 1 : 0,
                'deleted_order_histories' => $deletedOrderHistories ? 1 : 0,
            ]);
            // Thêm dữ liệu vào db SG
            $orders->chunk($chunk)->each(function ($chunkOrders) {
                $chunkOrders = $chunkOrders->map(function ($order) {
                    return $order->makeHidden(['shipping_address', 'billing_address']);
                });
                Order::query()->insert($chunkOrders->toArray());
            });
            $orderProducts->chunk($chunk)->each(function ($chunkOrderProducts) {
                OrderProduct::query()->insert($chunkOrderProducts->toArray());
            });
            $designs->chunk($chunk)->each(function ($chunkDesigns) {
                Design::query()->insert($chunkDesigns->toArray());
            });
            $orderHistories->chunk($chunk)->each(function ($chunkOrderHistories) {
                OrderHistory::query()->insert($chunkOrderHistories->toArray());
            });
            DB::commit();
            return true;
        } catch (Throwable $e) {
            DB::rollBack();
            logException($e, 'Sync data orders from US to SG failed');
            return false;
        }
    }

    /**
     * @param $data
     * @param $orderId
     * @param $type
     * @return array
     */
    public static function calculateChargeBaseAndShippingCost($data, $orderId, $type)
    {
        $response = [];
        $order = Order::query()->with('products')->find($orderId);
        if (empty($order)) {
            throw new \RuntimeException('Not found order');
        }

        $totalBaseCost = 0;
        $isException = false;
        $oldShippingCost = 0;
        $orderProducts = $order->products;
        if (empty($orderProducts)) {
            throw new \RuntimeException('Order has no items');
        }
        $orderProductIds = [];
        $orderProductData = [];
        $orderProductDataChangeTemplate = [];

        foreach ($data as $item) {
            $templateId = Arr::get($item, 'template_id');
            $oldTemplateId = Arr::get($item, 'old_template_id');
            $newTemplateId = Arr::get($item, 'new_template_id');
            $orderProductId = Arr::get($item, 'order_product_id');
            $quantity = (int)Arr::get($item, 'quantity');
            if ($newTemplateId != $oldTemplateId) {
                $orderProductDataChangeTemplate[$orderProductId] = [
                    'new_template_id' => $newTemplateId,
                    'old_template_id' => $oldTemplateId,
                ];
            }
            $orderProductData [] = [
                'order_product_id' => $orderProductId,
                'quantity' => $quantity,
            ];
            $orderProductIds [] = $orderProductId;
            $responseData = [
                'quantity' => $quantity,
                'template_id' => $templateId,
                'order_product_id' => $orderProductId,
            ];
            $newOptions = Arr::get($item, 'new_options');
            $oldOptions = Arr::get($item, 'old_options');
            if (empty($templateId) || empty($quantity) || empty($newOptions) || empty($newTemplateId)) {
                $responseData ['exception'] = 'Invalid data';
                $response ['products'] [] = $responseData;
                $isException = true;
                continue;
            }

            $orderProduct = $order->products->where('id', $orderProductId)->first();
            if (empty($orderProduct)) {
                $responseData ['exception'] = 'Not found order product';
                $response ['products'] [] = $responseData;
                $isException = true;
                continue;
            }

            $shippingCost = $orderProduct->calculateShipping();
            $variantKey = getVariantKey($newOptions);
            $orderLocation = getLocationByCode($order->country);
            $orderLocationCode = $orderLocation ? $orderLocation->getRegionCodes() : ['*'];
            $templateVariant = StoreService::getVariantWithLocation($newTemplateId, $variantKey, $orderLocationCode);
            if (empty($templateVariant)) {
                $responseData ['exception'] = 'Not found new template';
                $response ['products'] [] = $responseData;
                $isException = true;
                continue;
            }
            $baseCost = $templateVariant->base_cost;
            $productTotalShipping = 0;
            $productTotalBase = 0;
            if ($type === ChargeOrderTypeEnum::CHANGE_OPTION) {
                if (empty($oldOptions)) {
                    $responseData ['exception'] = 'Invalid old options for Change options';
                    $response ['products'] [] = $responseData;
                    $isException = true;
                    continue;
                }
                $oldVariantKey = getVariantKey($oldOptions);
                $oldTemplateVariant = StoreService::getVariantWithLocation($oldTemplateId ?? $templateId, $oldVariantKey, $orderLocationCode);
                $oldBaseCost = $oldTemplateVariant->base_cost;
                $baseCostDiff = $baseCost - $oldBaseCost;
                if ($baseCostDiff < 0 && $oldTemplateId == $newTemplateId) {
                    $responseData ['exception'] = 'Base cost of new option have to be greater than old option';
                    $response ['products'] [] = $responseData;
                    $isException = true;
                    continue;
                }
                $productTotalBase = $quantity * $baseCostDiff;

            } else if ($type === ChargeOrderTypeEnum::REPRINT) {
                $productTotalShipping = $shippingCost;
                $productTotalBase = $quantity * $baseCost;
            }

            $oldShippingCost += $shippingCost;
            $responseData ['shipping_cost'] = $shippingCost;
            $responseData ['total_shipping_cost'] = $productTotalShipping;
            $responseData ['base_cost'] = $baseCost;
            $responseData ['total_base_cost'] = $productTotalBase;
            $response['data'] [] = $responseData;
            $totalBaseCost += $productTotalBase;
        }
        if (empty($orderProductIds)) {
            throw new \RuntimeException('Order to charge has no items for calculating shipping');
        }

        $order->products = $orderProducts->whereIn('id', $orderProductIds);
        foreach ($orderProductData as $each) {
            $order->products = $order->products->map(function ($q) use ($each) {
                $q->where('id', $each['order_product_id']);
                $q->quantity = $each['quantity'];
                return $q;
            });
        }
        $totalDiffPrice = 0;
        if ($newTemplateId && $newTemplateId !== $oldTemplateId) {
            $storeInfo = Store::query()->find($order->store_id);
            $seller = User::query()->find($order->seller_id);
            $orderProductChangeIds = array_keys($orderProductDataChangeTemplate);
            $order->products = $orderProducts->whereIn('id', $orderProductChangeIds);
            foreach ($orderProductDataChangeTemplate as $orderProductId => &$each) {
                $order->products = $order->products->map(function ($q) use ($order, $orderProductId, &$each, $storeInfo, $seller) {
                    $q->where('id', $orderProductId);
                    $q->template_id = data_get($each, 'new_template_id');
                    $each['old_price'] = $q->price;
                    $newPrice = self::calculateFinalPriceOfOrder($order, $q, $storeInfo, $seller);
                    $q->price = $newPrice;
                    return $q;
                });
            }
            CampaignService::mappingCorrectPricing($order->products, false, $storeInfo->seller_id);
            foreach ($orderProductDataChangeTemplate as $orderProductId => &$each) {
                $order->products->map(function ($q) use ( $orderProductId, &$each) {
                    $each['new_price'] = $q->price;
                    return $q;
                });

                $response = array_map(function ($responseData) use ($orderProductId, $each) {
                    if ($responseData['order_product_id'] == $orderProductId) {
                        $responseData['new_price'] = $each['new_price'];
                        $responseData['old_price'] = $each['old_price'];
                        $responseData['diff_price'] = $each['new_price'] - $each['old_price'];
                    }
                    return $responseData;
                }, data_get($response, 'data'));

            }
            $totalDiffPrice += $each['new_price'] - $each['old_price'];
        }
        $newShippingCost = $order->calculateShippingCost();
        $totalShippingCost = $newShippingCost;
        $fulfillProcessingFeeIndex = 0;
        if ($type === ChargeOrderTypeEnum::CHANGE_OPTION) {
            $totalShippingCost = $newShippingCost - $oldShippingCost;
        } else if ($type === ChargeOrderTypeEnum::REPRINT &&
            !empty($order->type) &&
            $order->type === 'custom') {
            $fulfillProcessingFeeIndex = getFulfillProcessingFee();
        }
        $response['new_shipping_cost'] = $newShippingCost;
        $response['old_shipping_cost'] = $oldShippingCost;
        $response['total_shipping_cost'] = $totalShippingCost;
        $response['total_base_cost'] = $totalBaseCost;
        $response['total_amount'] = ($totalShippingCost + $totalBaseCost) * (1 + $fulfillProcessingFeeIndex);
        $response['total_price'] = $totalDiffPrice;
        $response['is_exception'] = $isException;
        return $response;
    }

    /**
     * Check if order is fulfilled partially
     * @param Order|RegionOrders $order
     * @return bool
     */
    public static function isFulfilledPartially($order)
    {
        /**
         * @var Order|RegionOrders $order
         */
        $orderProducts = $order->products;
        if ($orderProducts->isEmpty()) {
            return false;
        }
        foreach ($orderProducts as $orderProduct) {
            if ($orderProduct->fulfill_status === OrderProductFulfillStatus::PARTIALLY_FULFILLED) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if order is fully fulfilled
     * @param Order|RegionOrders $order
     * @return bool
     */
    public static function isFulfilled($order)
    {
        /**
         * @var Order|RegionOrders $order
         */
        $orderProducts = $order->products;
        if ($orderProducts->isEmpty()) {
            return false;
        }
        foreach ($orderProducts as $orderProduct) {
            if ($orderProduct->fulfill_status !== OrderProductFulfillStatus::FULFILLED) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param Order|RegionOrders $order
     * @param $transactionReference
     * @param bool $isPayPalECheck
     * @param null $currencyCode
     * @param null $currencyRate
     * @param null $paymentGatewayId
     * @return void
     */
    public static function paymentCompleted(Order|RegionOrders $order, $transactionReference, bool $isPayPalECheck = false, $currencyCode = null, $currencyRate = null, $paymentGatewayId = null)
    {
        try {
            if (empty($paymentGatewayId)) {
                $paymentGatewayId = $order->payment_gateway_id;
            }
            $order->paymentCompleted($order->total_amount, $transactionReference, $isPayPalECheck, $currencyCode, $currencyRate, $paymentGatewayId);
        } catch (\Throwable $e) {
            throw new RuntimeException('Error in mark paypal payment complete : ' . $e->getMessage());
        }
    }

    /**
     * @param Order|RegionOrders $order
     * @param $transactionReference
     * @param string $log
     * @param null $gatewayId
     * @return void
     */
    public static function paymentFailed(Order|RegionOrders $order, $transactionReference, $log = '', $gatewayId = null)
    {
        try {
            $order->paymentFailed($log, $transactionReference, $gatewayId);
        } catch (\Throwable $e) {
            throw new \RuntimeException('Error in mark paypal payment failed : ' . $e->getMessage());
        }
    }

    /**
     * @param Order|RegionOrders $order
     * @param $transactionReference
     * @return void
     * @throws Exception
     */
    public static function paymentPendingReview(Order|RegionOrders $order, $transactionReference)
    {
        try {
            $order->paymentPendingReview($transactionReference);
        } catch (\Throwable $e) {
            throw new \RuntimeException('Error in mark paypal payment pending review : ' . $e->getMessage());
        }
    }

    /**
     * @param Order|RegionOrders $order
     * @return array
     */
    public static function getShipping(Order|RegionOrders $order): array
    {
        return [
            'address' => [
                'line1' => $order->address,
                'city' => $order->city,
                'country' => $order->country,
                'line2' => $order->address_2,
                'postal_code' => $order->postcode,
                'state' => $order->state,
            ],
            'name' => $order->customer_name,
            'phone' => $order->customer_phone,
        ];
    }

    public static function calculateFinalPriceOfOrder (&$order, &$orderProduct, $storeInfo, $seller) {
        $location = getLocationByCode($order->country);
        $currency = SystemConfigController::findOrDefaultCurrency('USD');
        $currencyRate = $currency->rate;
        $templateProduct = Template::findAndCacheByKey((int)$orderProduct->template_id, false);

        // get products from DB to get price
        $productDB = Product::query()
            ->filterActiveProduct()
            ->onSellerConnection($seller)
            ->where('template_id', $orderProduct->template_id)
            ->where('campaign_id', $orderProduct->campaign_id)
            ->with([
                'template:id,options',
                'campaign:id,options,market_location',
            ])
            ->first();
        $orderProduct->setRelation('product', $productDB);
        $orderProduct->setRelation('campaign', $productDB->campaign);
        $dynamicBaseCostIndex = 0;
        $extraCustomFee = 0;
        $product = $orderProduct->product;
        $productPrice = $product->price;
        $product->options = json_decode($product->options, true, 512, JSON_THROW_ON_ERROR);
        if (!empty($product->options)) {
            // create variant key from options
            $variantKey = getVariantKey($orderProduct->options);
            $variantKeyBaseSize = getBaseVariantFromOptions($product->options,  data_get($product->options, 'color') ?? $product->default_option ?? 'white');
            $campLocation = getLocationByCode($orderProduct->product->market_location);
            $campRegionCodes = $campLocation ? $campLocation->getRegionCodes() : ['*'];
            $orderRegionCodes = $location ? $location->getRegionCodes() : ['*'];
            $templateVariantOnOrderLocation = ProductVariant::findAndCacheByTemplate($orderProduct->template_id)
                ->filter(function ($each) use ($variantKey, $orderRegionCodes) {
                    return $each->variant_key === $variantKey && in_array($each->location_code, $orderRegionCodes);
                })
                ->sortBy(function ($each) use ($orderRegionCodes) {
                    return array_search($each['location_code'], $orderRegionCodes);
                })
                ->first();
            if ($orderProduct->product->pricing_mode === PricingModeEnum::ADJUST_PRICE && $storeInfo->enable_dynamic_base_cost) {
                $templateInfoOrderAndCampLocation = StoreService::getBaseVariantOnOrderAndCampLocation($templateProduct->id, $variantKeyBaseSize, $orderRegionCodes, $campRegionCodes);
                OrderService::getDynamicBaseCostIndex($templateInfoOrderAndCampLocation['campaign_location'], $templateInfoOrderAndCampLocation['order_location'], $dynamicBaseCostIndex);
            }
            if ($templateVariantOnOrderLocation) {
                // calculate product price
                if ($orderProduct->product->pricing_mode === PricingModeEnum::ADJUST_PRICE) {
                    $productPrice += ceil($templateVariantOnOrderLocation->adjust_price * $currencyRate);
                } else if ($orderProduct->product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                    $productVariant = ProductVariant::query()
                        ->onSellerConnection($seller)
                        ->select('price')
                        ->firstWhere([
                            'variant_key' => $variantKey,
                            'product_id' => $orderProduct->product_id,
                        ]);
                    if (!is_null($productVariant)) {
                        // set product price = variant price
                        $productPrice = $productVariant->price;
                    } else if (($templateVariantOnOrderLocation->price * $currencyRate) > $productPrice) {
                        $productPrice = $templateVariantOnOrderLocation->price * $currencyRate;
                    }
                }
            }
        }

        $customerCustomOptions = !empty($product->custom_options) ? collect(json_decode($product->custom_options)) : collect();
        $customerCustomOptions = $customerCustomOptions->filter(fn($item) => !empty($item))->values();
        if ($customerCustomOptions->isNotEmpty() && (in_array($orderProduct->product->personalized, [PersonalizedType::CUSTOM_OPTION, PersonalizedType::NONE], true) || $orderProduct->product->full_printed === ProductPrintType::HANDMADE)) {
            $campaignExtraCustomFee = 0;
            $isCampaignMockupCustom = in_array($orderProduct->product->system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true);
            if (($orderProduct->product->personalized === PersonalizedType::CUSTOM_OPTION || $isCampaignMockupCustom) && !empty($product->campaign->options)) {
                $decodedOptions = json_decode($product->campaign->options, true, 512, JSON_THROW_ON_ERROR) ?? [];
                $campaignExtraCustomFee = !empty($decodedOptions['group']['extra_custom_fee']) ? (float)$decodedOptions['group']['extra_custom_fee'] : 0;
                $commonExtraCustomFee = !empty($decodedOptions['common_options']['extra_custom_fee']) ? (float)$decodedOptions['common_options']['extra_custom_fee'] : 0;
            } else if ($orderProduct->product->personalized === PersonalizedType::NONE && $orderProduct->product->full_printed === ProductPrintType::HANDMADE && !empty($product->template->options)) {
                $decodedOptions = json_decode($product->template->options, true, 512, JSON_THROW_ON_ERROR) ?? [];
                $campaignExtraCustomFee = !empty($decodedOptions['custom_options']['group']['extra_custom_fee']) ? (float)$decodedOptions['custom_options']['group']['extra_custom_fee'] : 0;
                $commonExtraCustomFee = !empty($decodedOptions['common_options']['extra_custom_fee']) ? (float)$decodedOptions['common_options']['extra_custom_fee'] : 0;
            }
            $optionGroups = $customerCustomOptions;
            $onlyOptionGroups = $optionGroups->filter(function ($group) {
                return collect($group)->where('g_type', 'common')->count() === 0;
            })->values();
            if ($product->full_printed === ProductPrintType::HANDMADE) {
                $onlyOptionGroups->each(function ($group, $idx) use ($campaignExtraCustomFee, &$extraCustomFee) {
                    if ($idx > 0) {
                        $group = collect($group);
                        if ($group->some(fn($option) => isset($option['value']) && $option['value'] !== '')) {
                            $extraCustomFee += (float)$campaignExtraCustomFee;
                        }
                        $group->each(function ($option) use (&$extraCustomFee) {
                            if (isset($option['value'], $option['price']) && $option['value'] !== '' && (float)$option['price'] > 0) {
                                $extraCustomFee += (float)$option['price'];
                            }
                        });
                    }
                });
            } else {
                $onlyOptionGroups->each(function ($group, $idx) use ($campaignExtraCustomFee, &$extraCustomFee) {
                    if ($idx > 0 && collect($group)->some(fn($option) => isset($option['value']) && $option['value'] !== '')) {
                        $extraCustomFee += (float)$campaignExtraCustomFee;
                    }
                });
            }
            $commonOptions = $optionGroups->filter(function ($group) {
                return collect($group)->where('g_type', 'common')->count() > 0;
            })->values();
            if ($commonOptions->isNotEmpty() && $commonOptions->some(fn($group) => collect($group)->some(fn($option) => isset($option['value']) && $option['value'] !== ''))) {
                $extraCustomFee += (float)$commonExtraCustomFee;
            }
        }
        $dynamicBaseCostIndexForPrice = $dynamicBaseCostIndex * $currencyRate;
        $dynamicBaseCostIndexForPrice = roundToHalf($dynamicBaseCostIndexForPrice);
        $dynamicBaseCostIndexRounded = $dynamicBaseCostIndexForPrice / $currencyRate;
        $productPrice += $extraCustomFee;
        $productPrice /= $currencyRate;
        $productPrice += $dynamicBaseCostIndexRounded;
        return $productPrice;
    }

    /**
     * @param string $currencyCode
     * @param string $paymentMethod
     * @return string
     */
    public static function checkAcceptableCurrency(string $currencyCode, string $paymentMethod = ''): string
    {
        $arrAcceptable = [
            CurrencyEnum::EUR,
            CurrencyEnum::USD,
        ];

        if ($paymentMethod === PaymentMethodEnum::MOMO) {
            $arrAcceptable[] = CurrencyEnum::VND;
        }

        return in_array($currencyCode, $arrAcceptable, true) ? $currencyCode : CurrencyEnum::USD;
    }

    /**
     * @param $amount
     * @param $currencyCode
     * @param $currencyRate
     * @return float
     */
    public static function getAmountByCurrency($amount, $currencyCode, $currencyRate): float
    {
        if ($currencyCode !== CurrencyEnum::USD) {
            $amount *= $currencyRate;
        }

        return $amount;
    }

    /**
     * @param $gateway
     * @param $eventType
     * @param $orderId
     * @param $gateId
     * @param $payload
     * @return void
     */
    public static function storePaymentGatewaySimpleLogHandler($gateway, $eventType, $orderId, $gateId, $payload): void {
        try {
            storePaymentGatewayWebhookLogs($gateway, $eventType, $orderId, $gateId, $payload);
        } catch (\Exception $e) {
            logException($e, 'storePaymentGatewaySimpleLogHandler');
        }
    }
}
