<?php
namespace App\Traits;

use Elasticsearch\ClientBuilder;
use Exception;
use GuzzleHttp\RequestOptions;
use Throwable;

trait ElasticTradeMarksClient
{
    protected $instance = null;
    /**
     * @throws Throwable
     */
    public function elastic(string $function = null, array $params = [])
    {
        $host = config('scout_elastic.client.hosts.trademarks');
        $timeout = config('scout_elastic.timeout');
        $connect_timeout = config('scout_elastic.connect_timeout');
        if (empty($host)) {
            return null;
        }
        if (is_null($this->instance)) {
            $this->instance = ClientBuilder::create()
                ->setConnectionParams([
                    'client' => [
                        RequestOptions::TIMEOUT => $timeout,
                        RequestOptions::CONNECT_TIMEOUT => $connect_timeout,
                    ],
                ])
                ->setHosts($host)
                ->build();
        }

        if (!is_null($function)) {
            throw_if(!method_exists($this->instance, $function), Exception::class, "[ElasticTradeMarksClient Client] Method '{$function}' not found");
            try {
                $result = $this->instance->$function($params);
                if (empty($result) || !empty($result['errors'])) {
                    if (!empty($result['errors'])) {
                        logToDiscord("ElasticTradeMarksClient: $function" . "\r\n" . "Result: " . json_encode($result) . "\r\n", 'error_elastic');
                    }
                    graylogInfo("Can not execute $function on elasticsearch, Query: " . json_encode($params) . ", Result: " . json_encode($result), [
                        'category' => 'elastic_sync_log',
                        'user_type' => 'system',
                        'user_id' => null,
                        'action'  => $function,
                    ]);
                }
                return $result;
            } catch (Throwable $e) {
                logToDiscord(
                    "```" . "\r\n" .
                    "🔰 ElasticTradeMarksClient: " . $function . "\r\n" .
                    "🔰 Exception: " . $e->getMessage() . "\r\n" .
                    "```" . "\r\n",
                    'error_elastic',
                    true
                );
                graylogError("Elasticsearch: $function : " . $e->getMessage(), [
                    'category' => 'elastic_query_log',
                    'user_id' => currentUser() ? currentUser()->getUserId() : null,
                    'function' => $function,
                    'params' => $params,
                    'trace' => $e->getTraceAsString(),
                ]);
                return null;
            }
        }

        return $this->instance;
    }

    /**
     * @return bool|null
     * @throws Throwable
     */
    public function elasticPing(): ?bool
    {
        return $this->elastic('ping');
    }
}
