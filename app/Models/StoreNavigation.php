<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\StoreNavigation
 *
 * @property int $id
 * @property int $store_id
 * @property string $link_text
 * @property string $link_url
 * @property string $place 'header','footer','side_column'
 * @property int|null $position menu order in number
 * @property int|null $parent_id
 * @property int $status
 * @property-read \Illuminate\Database\Eloquent\Collection|StoreNavigation[] $childs
 * @property-read int|null $childs_count
 * @property-read mixed $child_menu
 * @property-read StoreNavigation|null $parent
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation query()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation whereLinkText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation whereLinkUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation wherePlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreNavigation whereStoreId($value)
 * @mixin \Eloquent
 */
class StoreNavigation extends Model
{

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'store_navigation';

    protected $fillable = ['store_id', 'link_text', 'link_url', 'place', 'parent_id', 'position'];

    public $timestamps = false;

    protected $childs = [];

    protected $appends = ['child_menu'];

    public function childs(): HasMany
    {
        return $this->hasmany(__CLASS__, 'parent_id', 'id');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(__CLASS__, 'parent_id', 'id');
    }

    public function childrens()
    {
        return $this->childs()->with(['childrens' => function($query) {
            $query->where('store_id', $this->store_id);
            $query->whereColumn('id', '!=', 'parent_id');
        }]);
    }

    public function addChildMenu($menu)
    {
        $this->childs[] = $menu;
    }

    public function getChildMenuAttribute()
    {
        $childs2 = [];
        foreach ($this->childs as $child) {
            $childs2[] = $child->toArray();
        }
        return $childs2;
    }

    public function getDisplayMenu()
    {
        $menu = [
            'name' => $this->link_text,
            'title' => $this->link_text,
            'url' => $this->link_url
        ];

        if (count($this->childs) > 0) {
            $submenu = [];

            foreach ($this->childs as $child) {
                $submenu[] = $child->getDisplayMenu();
            }

            $menu['submenu'] = $submenu;
        }

        return $menu;
    }
}
