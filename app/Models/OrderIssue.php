<?php

namespace App\Models;

use App\Traits\ScopeFilterDateRangeTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\OrderIssue
 *
 * @property int $id
 * @property int $order_id
 * @property int $seller_id
 * @property string|null $title
 * @property string|null $issue_category
 * @property string $customer_request
 * @property string $charge_type
 * @property string $refund_type
 * @property double $charge_amount
 * @property double $refund_amount
 * @property string|null $detail
 * @property int|null $supplier_id
 * @property string|null $product_category
 * @property string $status
 * @property int $created_by
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property-read \App\Models\Order|null $order
 * @property-read \App\Models\User|null $seller
 * @property-read \App\Models\Staff|null $staff
 * @property-read \App\Models\Supplier|null $supplier
 * @method static \Database\Factories\OrderIssueFactory factory(...$parameters)
 * @method static Builder|OrderIssue filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static Builder|OrderIssue newModelQuery()
 * @method static Builder|OrderIssue newQuery()
 * @method static Builder|OrderIssue query()
 * @method static Builder|OrderIssue whereAmount($value)
 * @method static Builder|OrderIssue whereChargeType($value)
 * @method static Builder|OrderIssue whereCreatedAt($value)
 * @method static Builder|OrderIssue whereCreatedBy($value)
 * @method static Builder|OrderIssue whereCustomerRequest($value)
 * @method static Builder|OrderIssue whereDetail($value)
 * @method static Builder|OrderIssue whereId($value)
 * @method static Builder|OrderIssue whereIssueCategory($value)
 * @method static Builder|OrderIssue whereOrderId($value)
 * @method static Builder|OrderIssue whereProductCategory($value)
 * @method static Builder|OrderIssue whereSellerId($value)
 * @method static Builder|OrderIssue whereStatus($value)
 * @method static Builder|OrderIssue whereSupplierId($value)
 * @method static Builder|OrderIssue whereTitle($value)
 * @method static Builder|OrderIssue whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class OrderIssue extends Model
{
    use HasFactory;
    use ScopeFilterDateRangeTrait;

    protected $table = 'order_issue';

    protected $fillable = [
      'issue_category',
      'customer_request',
      'charge_type',
      'refund_type',
      'charge_amount',
      'refund_amount',
      'detail',
      'supplier_id',
      'product_category',
      'status',
      'created_by',
      'order_id',
      'seller_id',
    ];

    /**
     * @return BelongsTo
     */
    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'created_by', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }

    /**
     * @return HasOne
     */
    public function supplier(): HasOne
    {
        return $this->hasOne(Supplier::class, 'id', 'supplier_id');
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public const FILTER_COLUMN_DATE = 'order_issue.created_at';
}
