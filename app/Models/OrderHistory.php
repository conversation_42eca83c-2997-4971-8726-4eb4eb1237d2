<?php

namespace App\Models;

use App\Actions\Commons\InsertOrderHistory;
use App\Enums\OrderHistoryDisplayLevelEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Traits\HasRegionConnection;

/**
 * App\Models\OrderHistory
 *
 * @property string $id
 * @property int $order_id
 * @property string $action
 * @property string $order_status 'archived','draft','pending','processing','completed','on_hold','refunded','cancelled','deleted','pending_payment'
 * @property string|null $fulfill_status 'unfulfilled','fulfilled','partial_fulfilled','cancelled','processing','on_hold','invalid','no_ship'
 * @property string|null $assignee
 * @property string|null $support_status
 * @property string $display_level
 * @property string|null $detail changed note
 * @property string|null $admin_detail
 * @property string|null $updated_by Updated by user id
 * @property string $created_at
 * @method static \Database\Factories\OrderHistoryFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereAdminDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereAssignee($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereDisplayLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereFulfillStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereOrderStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereSupportStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderHistory whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class OrderHistory extends Model
{
    use HasFactory, HasRegionConnection;
    public const ARR_SELECT_ORDER = [
        'id',
        'status',
        'fulfill_status',
        'support_status',
        'assignee',
    ];
    public $incrementing = false;
    public $timestamps = false;
    protected $table = 'order_history';
    protected $fillable = [
        'id',
        'order_id',
        'action',
        'order_status',
        'fulfill_status',
        'assignee',
        'support_status',
        'display_level',
        'detail',
        'admin_detail',
        'updated_by',
    ];

    public static function insertLog(
        Order|RegionOrders|null $order,
        string $action,
        $details = [null, null],
        $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN,
        $email = null
    ): void
    {
        if (is_null($order)) {
            return;
        }
        $currentUser = currentUser();
        if ($currentUser->isSeller() || $currentUser->isAdmin() || $currentUser->isSupplier()) {
            $email ??= $currentUser->getEmail();
            if ($currentUser->isSeller()) {
                $email = User::query()->where('email', $email)->value('email');
            }
            if ($currentUser->isAdmin()) {
                $email = Staff::query()->where('email', $email)->value('email');
            }
            if ($currentUser->isSupplier()) {
                $email = Supplier::query()->where('email', $email)->value('email');
            }
        }
        InsertOrderHistory::dispatch(
            $order,
            $action,
            $details,
            $displayLevel,
            $email,
        );
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }
}
