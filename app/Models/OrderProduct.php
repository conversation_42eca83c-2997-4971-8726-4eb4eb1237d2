<?php

namespace App\Models;

use App\Enums\CountryEnum;
use App\Enums\CustomOptionType;
use App\Enums\DateRangeEnum;
use App\Enums\DesignByEnum;
use App\Enums\DesignStatusEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\FulfillmentStatusEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductPrintType;
use App\Enums\PromptTypeEnum;
use App\Enums\TrackingStatusEnum;
use App\Enums\NeedDesignTagsEnum;
use App\Models\Relationships\CustomBelongsTo;
use App\Services\TradeMarkService;
use App\Traits\HasRelationShipsCustom;
use App\Traits\ScopeFilterDateRangeTrait;
use App\Traits\SPModel;
use Awobaz\Compoships\Compoships;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\OrderService\Traits\HasRegionConnection;

/**
 * App\Models\OrderProduct
 *
 * @property int $id
 * @property int $order_id
 * @property int|null $product_id
 * @property int|null $campaign_id
 * @property int|null $seller_id
 * @property int|null $auth_id
 * @property int|null $ref_id
 * @property int|null $template_id
 * @property int|null $shipping_rule_id
 * @property int|null $related_campaign_id
 * @property int|null $related_product_id
 * @property int|null $promotion_rule_id
 * @property string|null $campaign_title copy from campaign
 * @property string|null $product_name copy from product type
 * @property string|null $product_url
 * @property string $thumb_url
 * @property string|null $options
 * @property string|null $custom_options
 * @property string|null $custom_print_space custom print space
 * @property string|null $color
 * @property string|null $size
 * @property float $fulfill_base_cost
 * @property float $base_cost
 * @property float $dynamic_base_cost_index
 * @property float $dynamic_base_cost_index_rounded
 * @property float $price
 * @property float|null $adjust_test_price
 * @property float $extra_custom_fee
 * @property float $extra_print_cost
 * @property float $weight pound:lbs
 * @property int $quantity
 * @property float $tax
 * @property string|null $barcode Store barcode file for fba order
 * @property float $total_amount
 * @property float $fulfill_shipping_cost
 * @property float $shipping_cost
 * @property float $design_cost
 * @property string $design_by
 * @property float $discount_amount discount of product if applied
 * @property float $fulfill_profit
 * @property float $seller_profit
 * @property float $artist_profit
 * @property float $sen_points
 * @property int $upsell_status 1:yes,0:no
 * @property int $fulfilled_quantity set if create fulfillment
 * @property string $fulfill_status 'unfulfilled','fulfilled','partial_fulfilled','cancelled','processing','on_hold','rejected','exception','invalid','out_of_stock','no_ship','pending','reviewing','designing','on_delivery'
 * @property string $sen_fulfill_status YES,NO,REVIEW,PENDING
 * @property int $refund_quantity
 * @property string|null $sku
 * @property int|null $supplier_id
 * @property string|null $supplier_name
 * @property string|null $assigned_supplier_at
 * @property string|null $fulfill_sku
 * @property int|null $fulfill_product_id
 * @property float $fulfill_cost
 * @property string $fulfill_order_id
 * @property string|null $fulfill_exception_log
 * @property string|null $shipping_carrier
 * @property string|null $tracking_code
 * @property string|null $next_scan_tracking_code_at
 * @property string|null $tracking_url
 * @property string|null $tracking_status
 * @property string $updated_at
 * @property string|null $fulfilled_at
 * @property string|null $delivered_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $received_at
 * @property float $processing_day
 * @property float|null $shipping_day
 * @property string|null $billed_at
 * @property int $personalized 1:yes,0:no (yes if customer customize text/image)
 * @property int $full_printed 1:yes (if product is full printed
 * @property string|null $exported_at
 * @property int $shard_id
 * @property int|null $collection_id
 * @property string $tm_status
 * @property string|null $external_product_id
 * @property string|null $external_fulfillment_id
 * @property string|null $external_id
 * @property int|null $sync_status Sync order product info to singlestore
 * @property string|null $sync_at
 * @property string|null $fulfill_fba_by
 * @property int $cross_shipping 0: not cross shipping, 1: cross shipping
 * @property string|null $supplier_exported_at
 * @property int|null $total_reprint
 * @property int|null $is_corner_placement
 * @property float|null $base_shipping_cost
 * @property string|null $custom_designs
 * @property string|null $additional_attributes
 * @property int $at_risk
 * @property string|null $campaign_type
 * @property-read \App\Models\User|null $author
 * @property-read \App\Models\Campaign|null $campaign
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Design> $customOptionDesigns
 * @property-read int|null $custom_option_designs_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $designs
 * @property-read int|null $designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $fulfillOrderDesigns
 * @property-read int|null $fulfill_order_designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $fulfillOrderMockups
 * @property-read int|null $fulfill_order_mockups_count
 * @property-read mixed $custom_options_pb
 * @property-read mixed $custom_options_regular
 * @property-read array|null $design_pb
 * @property-read mixed $print_url
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderProductLog> $logs
 * @property-read int|null $logs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $mockups
 * @property-read int|null $mockups_count
 * @property-read \App\Models\Order|null $order
 * @property-read \App\Models\Pricing|null $pricing
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $printDesigns
 * @property-read int|null $print_designs_count
 * @property-read \App\Models\Product|null $product
 * @property-read \App\Models\ProductReview|null $productReview
 * @property-read \App\Models\ProductPoint|null $product_point
 * @property-read \App\Models\User|null $seller
 * @property-read \App\Models\ShippingRule|null $shipping_rule
 * @property-read \App\Models\Supplier|null $supplier
 * @property-read \App\Models\Product|null $template
 * @property-read \App\Models\TrackingStatus|null $trackingStatus
 * @method static \Database\Factories\OrderProductFactory factory($count = null, $state = [])
 * @method static Builder|OrderProduct filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false, $submonths = null, $isTimeZone = false)
 * @method static Builder|OrderProduct filterDateRangePaidAt($dateRange, $startDate = null, $endDate = null, $isOrderBy = true, $column = 'order.paid_at')
 * @method static Builder|OrderProduct filterFulfill($arrFulfillStatus = null, $supplierId = null, $isListing = false)
 * @method static Builder|OrderProduct filterFulfillProduct($arrFulfillStatus, $supplierId = 0, $personalized = false, $isListing = true)
 * @method static Builder|OrderProduct fulfillLate()
 * @method static Builder|OrderProduct makeHiddenAll()
 * @method static Builder|OrderProduct missingFulfillCost()
 * @method static Builder|OrderProduct newModelQuery()
 * @method static Builder|OrderProduct newQuery()
 * @method static Builder|OrderProduct onDeliveryStats()
 * @method static Builder|OrderProduct onlyTrashed()
 * @method static Builder|OrderProduct query()
 * @method static Builder|OrderProduct shippingLate()
 * @method static Builder|OrderProduct trackingLate()
 * @method static Builder|OrderProduct trackingNotFound()
 * @method static Builder|OrderProduct trackingPreShipment()
 * @method static Builder|OrderProduct whereAdditionalAttributes($value)
 * @method static Builder|OrderProduct whereAdjustTestPrice($value)
 * @method static Builder|OrderProduct whereArtistProfit($value)
 * @method static Builder|OrderProduct whereAssignedSupplierAt($value)
 * @method static Builder|OrderProduct whereAtRisk($value)
 * @method static Builder|OrderProduct whereAuthId($value)
 * @method static Builder|OrderProduct whereBarcode($value)
 * @method static Builder|OrderProduct whereBaseCost($value)
 * @method static Builder|OrderProduct whereBaseShippingCost($value)
 * @method static Builder|OrderProduct whereBilledAt($value)
 * @method static Builder|OrderProduct whereCampaignId($value)
 * @method static Builder|OrderProduct whereCampaignTitle($value)
 * @method static Builder|OrderProduct whereCollectionId($value)
 * @method static Builder|OrderProduct whereColor($value)
 * @method static Builder|OrderProduct whereCrossShipping($value)
 * @method static Builder|OrderProduct whereCustomDesigns($value)
 * @method static Builder|OrderProduct whereCustomOptions($value)
 * @method static Builder|OrderProduct whereCustomPrintSpace($value)
 * @method static Builder|OrderProduct whereDeletedAt($value)
 * @method static Builder|OrderProduct whereDeliveredAt($value)
 * @method static Builder|OrderProduct whereDiscountAmount($value)
 * @method static Builder|OrderProduct whereDynamicBaseCostIndex($value)
 * @method static Builder|OrderProduct whereDynamicBaseCostIndexRounded($value)
 * @method static Builder|OrderProduct whereExportedAt($value)
 * @method static Builder|OrderProduct whereExternalFulfillmentId($value)
 * @method static Builder|OrderProduct whereExternalId($value)
 * @method static Builder|OrderProduct whereExternalProductId($value)
 * @method static Builder|OrderProduct whereExtraCustomFee($value)
 * @method static Builder|OrderProduct whereExtraPrintCost($value)
 * @method static Builder|OrderProduct whereFulfillBaseCost($value)
 * @method static Builder|OrderProduct whereFulfillCost($value)
 * @method static Builder|OrderProduct whereFulfillExceptionLog($value)
 * @method static Builder|OrderProduct whereFulfillFbaBy($value)
 * @method static Builder|OrderProduct whereFulfillOrderId($value)
 * @method static Builder|OrderProduct whereFulfillProductId($value)
 * @method static Builder|OrderProduct whereFulfillProfit($value)
 * @method static Builder|OrderProduct whereFulfillShippingCost($value)
 * @method static Builder|OrderProduct whereFulfillSku($value)
 * @method static Builder|OrderProduct whereFulfillStatus($value)
 * @method static Builder|OrderProduct whereFulfilledAt($value)
 * @method static Builder|OrderProduct whereFulfilledQuantity($value)
 * @method static Builder|OrderProduct whereFullPrinted($value)
 * @method static Builder|OrderProduct whereId($value)
 * @method static Builder|OrderProduct whereNextScanTrackingCodeAt($value)
 * @method static Builder|OrderProduct whereOptions($value)
 * @method static Builder|OrderProduct whereOrderId($value)
 * @method static Builder|OrderProduct wherePersonalized($value)
 * @method static Builder|OrderProduct wherePrice($value)
 * @method static Builder|OrderProduct whereProcessingDay($value)
 * @method static Builder|OrderProduct whereProductId($value)
 * @method static Builder|OrderProduct whereProductName($value)
 * @method static Builder|OrderProduct whereProductUrl($value)
 * @method static Builder|OrderProduct wherePromotionRuleId($value)
 * @method static Builder|OrderProduct whereQuantity($value)
 * @method static Builder|OrderProduct whereReceivedAt($value)
 * @method static Builder|OrderProduct whereRefId($value)
 * @method static Builder|OrderProduct whereRefundQuantity($value)
 * @method static Builder|OrderProduct whereRelatedCampaignId($value)
 * @method static Builder|OrderProduct whereRelatedProductId($value)
 * @method static Builder|OrderProduct whereSellerId($value)
 * @method static Builder|OrderProduct whereSellerProfit($value)
 * @method static Builder|OrderProduct whereSenFulfillStatus($value)
 * @method static Builder|OrderProduct whereSenPoints($value)
 * @method static Builder|OrderProduct whereShardId($value)
 * @method static Builder|OrderProduct whereShippingCarrier($value)
 * @method static Builder|OrderProduct whereShippingCost($value)
 * @method static Builder|OrderProduct whereShippingDay($value)
 * @method static Builder|OrderProduct whereShippingRuleId($value)
 * @method static Builder|OrderProduct whereSize($value)
 * @method static Builder|OrderProduct whereSku($value)
 * @method static Builder|OrderProduct whereSupplierExportedAt($value)
 * @method static Builder|OrderProduct whereSupplierId($value)
 * @method static Builder|OrderProduct whereSupplierName($value)
 * @method static Builder|OrderProduct whereSyncAt($value)
 * @method static Builder|OrderProduct whereSyncStatus($value)
 * @method static Builder|OrderProduct whereTax($value)
 * @method static Builder|OrderProduct whereTemplateId($value)
 * @method static Builder|OrderProduct whereThumbUrl($value)
 * @method static Builder|OrderProduct whereTmStatus($value)
 * @method static Builder|OrderProduct whereTotalAmount($value)
 * @method static Builder|OrderProduct whereTotalReprint($value)
 * @method static Builder|OrderProduct whereTrackingCode($value)
 * @method static Builder|OrderProduct whereTrackingStatus($value)
 * @method static Builder|OrderProduct whereTrackingUrl($value)
 * @method static Builder|OrderProduct whereUpdatedAt($value)
 * @method static Builder|OrderProduct whereUpsellStatus($value)
 * @method static Builder|OrderProduct whereWeight($value)
 * @method static Builder|OrderProduct withTrashed()
 * @method static Builder|Model withWhereHas(string $relation, \Closure $condition)
 * @method static Builder|OrderProduct withoutTrashed()
 * @mixin \Eloquent
 */
class OrderProduct extends BaseProduct
{
    use HasFactory;
    use SPModel;
    use SoftDeletes;
    use ScopeFilterDateRangeTrait;
    use Compoships;
    use HasRelationShipsCustom {
        HasRelationShipsCustom::hasOne insteadof Compoships;
        HasRelationShipsCustom::hasMany insteadof Compoships;
        HasRelationShipsCustom::belongsTo insteadof Compoships;
        HasRelationShipsCustom::newHasOne insteadof Compoships;
        HasRelationShipsCustom::newHasMany insteadof Compoships;
        HasRelationShipsCustom::newBelongsTo insteadof Compoships;
    }
    use HasRegionConnection;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'order_product';
    protected $fillable = [
        'order_id',
        'product_id',
        'campaign_id',
        'campaign_title',
        'template_id',
        'product_name',
        'product_url',
        'thumb_url',
        'options',
        'custom_options',
        'fulfill_base_cost',
        'base_cost',
        'price',
        'quantity',
        'tax',
        'total_amount',
        'fulfill_shipping_cost',
        'base_shipping_cost',
        'shipping_cost',
        'discount_amount',
        'fulfill_profit',
        'seller_profit',
        'upsell_status',
        'seller_id',
        'auth_id',
        'ref_id',
        'supplier_id',
        'supplier_name',
        'fulfill_sku',
        'fulfill_cost',
        'fulfill_order_id',
        'tracking_code',
        'shipping_carrier',
        'tracking_url',
        'fulfill_status',
        'fulfill_exception_log',
        'fulfilled_at',
        'billed_at',
        'personalized',
        'full_printed',
        'sku',
        'exported_at',
        'weight',
        'delivered_at',
        'processing_day',
        'received_at',
        'deleted_at',
        'adjust_test_price',
        'fulfill_product_id',
        'extra_custom_fee',
        'shipping_rule_id',
        'artist_profit',
        'tm_status',
        'sen_points',
        'shipping_day',
        'external_product_id',
        'external_fulfillment_id',
        'external_id',
        'color',
        'custom_print_space',
        'extra_print_cost',
        'barcode',
        'fulfill_fba_by',
        'cross_shipping',
        'supplier_exported_at',
        'dynamic_base_cost_index',
        'dynamic_base_cost_index_rounded',
        'related_campaign_id',
        'related_product_id',
        'promotion_rule_id',
        'at_risk',
        'combo_id',
        'campaign_type',
        'design_by',
    ];

    public $timestamps = false;
    protected $batch = false;

    public const FIELD_EXPORT = [
        'order_product.product_name',
        'order_product.quantity',
        'order_product.sku',
        'order_product.supplier_name',
        'order_product.fulfill_sku',
    ];
    public const FIELD_FULFILL = [
        'id',
        'seller_id',
        'order_id',
        'product_name',
        'options',
        'quantity',
        'supplier_id',
        'supplier_name',
        'fulfill_sku',
        'fulfill_product_id',
        'base_cost',
        'fulfill_cost',
        'product_id',
        'fulfill_status',
        'fulfill_order_id',
        'thumb_url',
        'fulfilled_at',
        'full_printed',
        'weight',
        'sku',
        'fulfill_status',
        'tracking_code',
        'tracking_status',
        'tracking_url',
        'shipping_carrier',
        'personalized',
        'template_id',
        'campaign_id',
        'total_amount',
        'shipping_cost',
        'discount_amount',
        'seller_profit',
        'fulfill_profit',
        'artist_profit',
        'sen_points',
        'custom_print_space',
        'barcode',
        'fulfill_fba_by',
    ];
    public $incrementing = false;

    /**
     * @param array $attributes
     * @param $batch
     */
    public function __construct(array $attributes = [], $batch = false)
    {
        $this->batch = $batch;
        parent::__construct($attributes);
    }

    /**
     * @return string
     */
    public function getTable(): string
    {
        if ($this->batch) {
            return $this->table;
        }
        return parent::getTable();
    }

    protected static function booted(): void
    {
        static::creating(static function ($model) {
            $model->id = generateShardId($model);
            $model->shard_id = config('senprints.shard_id');
        });
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'template_id', 'id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'auth_id');
    }

    public function mockups(): HasMany
    {
        return $this->hasMany(File::class, 'order_product_id')->where('file.type', FileTypeEnum::IMAGE);
    }

    public function designs(): HasMany
    {
        return $this->hasMany(File::class, 'order_product_id')->where('file.type', FileTypeEnum::DESIGN);
    }

    public function product_files(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'product_id');
    }

    public function product_point(): HasOne
    {
        return $this->hasOne(ProductPoint::class, 'product_id', 'template_id');
    }

    public function fulfill_product () : BelongsTo
    {
        return $this->belongsTo(Product::class, 'fulfill_product_id', 'id');
    }

    public function fulfillOrderMockups(): HasMany
    {
        return $this->hasMany(File::class, 'order_product_id')
            ->where([
                'file.type' => FileTypeEnum::IMAGE,
                'file.option' => null,
            ]);
    }

    public function fulfillOrderDesigns(): HasMany
    {
        return $this->hasMany(File::class, 'order_product_id')
            ->where([
                'file.type' => FileTypeEnum::DESIGN,
                'file.option' => FileRenderType::PRINT,
            ]);
    }

    public function customOptionDesigns(): HasMany
    {
        return $this->hasMany(Design::class, 'order_product_id');
    }

    public function shipping_rule(): BelongsTo
    {
        return $this->belongsTo(ShippingRule::class, 'shipping_rule_id');
    }

    public function printDesigns(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'product_id')
            ->where('type', FileTypeEnum::DESIGN)
            ->where('option', FileRenderType::PRINT);
    }

    public function getPrintDesigns(): array
    {
        $files = File::query()->where('product_id', $this->product_id)
            ->onSellerConnection($this->seller)
            ->where('type', FileTypeEnum::DESIGN)
            ->where('option', FileRenderType::PRINT)
            ->get();
        if ($files->isEmpty()) {
            $files = Design::query()
                ->where('order_id', $this->order_id)
                ->where('product_id', $this->product_id)
                ->where('order_product_id', $this->id)
                ->where('type', DesignTypeEnum::PRINT)
                ->get();
        }

        return $files->toArray();
    }

    /**
     * @param null|int $quantity
     * @return float|int
     */
    public function calculateTotal($quantity = null)
    {
        if ($quantity) {
            $this->total_amount = $quantity * ($this->price);
        } else {
            if (is_string($this->quantity)) {
                logToDiscord('OrderProduct calculate total error with quantity is string. Order Id: ' . $this->order_id);
            }
            $this->total_amount = $this->quantity * ($this->price);
        }
        return $this->total_amount;
    }

    public function calculateTotalBaseCost()
    {
        return $this->quantity * $this->base_cost;
    }

    public function calculateTotalRefund()
    {
        return $this->refund_quantity * $this->price;
    }

    public function calculateTax()
    {
        return $this->tax;
    }

    public function calculateShipping()
    {
        return $this->shipping_cost;
    }

    public function getTrackingUrlAttribute()
    {
        if (empty($this->tracking_code)) {
            return null;
        }

        // this case should go first, else it will match the hotfix below
        if (preg_match('/4[0-9]{28,32}/', $this->tracking_code)) {
            $trackUrl = 'https://www.aftership.com/track/dhl-global-mail/';
            return $trackUrl . $this->tracking_code;
        }

        // hotfix for MD new DHL tracking, see thread
        // https://discord.com/channels/874562708392005672/1117413356559679508/1117413362175836160
        // TODO: can delete this after a few months
        if (!empty($this->attributes['tracking_url']) && Str::contains($this->attributes['tracking_url'], 'easypost')
            && (!empty($this->attributes['shipping_carrier']) && Str::contains(Str::lower($this->attributes['shipping_carrier']), 'dhl'))
            && preg_match('/4[0-9]{28,32}/', $this->tracking_code) > 0) {
            return 'https://www.aftership.com/track/' . $this->tracking_code;
        }

        $trackUrl = 'https://t.17track.net/en#nums=';
        if ((!empty($this->attributes['shipping_carrier']) && strtolower($this->attributes['shipping_carrier']) === 'spring') || preg_match('/H[0-9]{18,23}/', $this->tracking_code) > 0) {
            $trackUrl = 'https://www.mailingtechnology.com/tracking/?tn=';
        } else if (
            preg_match('/3SD.{10,15}/', $this->tracking_code) > 0 ||
            preg_match('/N[0-9]{10,15}/', $this->tracking_code) > 0 ||
            preg_match('/13[0-9]{10,15}/', $this->tracking_code) > 0 ||
            preg_match('/UY[0-9A-Z]{10,13}/', $this->tracking_code) > 0 ||
            preg_match('/LF[0-9A-Z]{10,13}/', $this->tracking_code) > 0
        ) {
            $trackUrl = 'https://www.aftership.com/track/';
        } else if (preg_match('/026[0-9]{10,15}/', $this->tracking_code) > 0 ||
            preg_match('/ASDE[0-9]{12,18}/', $this->tracking_code) > 0
        ) {
            $trackUrl = 'https://www.aftership.com/track/asendia-de/';
        }

        return !empty($this->attributes['tracking_url']) && filter_var($this->attributes['tracking_url'], FILTER_VALIDATE_URL) ? $this->attributes['tracking_url'] : $trackUrl . $this->tracking_code;
    }

    public static function selectExportFields($date): Builder
    {
        return self::query()
            ->join('order', 'order_product.order_id', 'order.id', 'right')
            ->addSelect(self::FIELD_EXPORT)
            ->addSelect(Order::FIELD_EXPORT)
            ->whereNull([
                'order.export_id',
                'order.exported_at',
            ])->whereDate('order.created_at', '<=', $date)
            ->where('order.status', OrderStatus::PROCESSING)
            ->where('order.fulfill_status', OrderFulfillStatus::UNFULFILLED);
    }

    public function scopeFilterFulfillProduct($query, $arrFulfillStatus, $supplierId = 0, $personalized = false, $isListing = true)
    {
        if ($isListing) {
            $arrSenFulfillStatus = [
                OrderSenFulfillStatus::YES,
                OrderSenFulfillStatus::PENDING,
            ];
        } else {
            $arrSenFulfillStatus = [
                OrderSenFulfillStatus::YES,
            ];
        }

        return $query
            ->when(
                $personalized,
                function ($q) {
                    $q->where('order_product.personalized', 1);
                }
            )
            ->when(
                !empty($arrFulfillStatus),
                function ($q) use ($arrFulfillStatus) {
                    $q->whereIn('order_product.fulfill_status', $arrFulfillStatus);
                }
            )
            ->where(
                static function ($q) use ($supplierId) {
                    if ($supplierId === '-1') {
                        return $q->whereNull('order_product.supplier_id')->orWhere('order_product.supplier_id', 0);
                    }
                    if ($supplierId === '0') {
                        return $q->whereNotNull('order_product.supplier_id');
                    }
                    if ($supplierId > 0) {
                        return $q->where('order_product.supplier_id', $supplierId);
                    }

                    return $q;
                }
            )->whereIn('order_product.sen_fulfill_status', $arrSenFulfillStatus);
    }

    public function scopeFilterFulfill($query, $arrFulfillStatus = null, $supplierId = null, $isListing = false)
    {
        // default
        if (empty($arrFulfillStatus)) {
            $arrFulfillStatus = [
                OrderProductFulfillStatus::UNFULFILLED,
                OrderProductFulfillStatus::REJECTED,
            ];
        }

        return $query->filterFulfillProduct($arrFulfillStatus, $supplierId, false, $isListing);
    }
    private const DEFAULT_ORDER_SHIPPING_LATE_DATES = 29;

    public function scopeShippingLate($query)
    {
        $query = $query
        // ->where('fulfilled_at', '<', now()->subDays(21))
        // ->whereNull('order_product.received_at')
        ->whereHas('supplier.supplierShippingLateRule', function ($query) {
            $query->whereNotNull('id');
        })
        ->whereExists(function ($subQuery) {
            $subQuery->select(DB::raw(1))
                     ->from('supplier')
                     ->join('order', 'order.id', '=', 'order_product.order_id')
                     ->leftjoin('supplier_shipping_late_rule', 'supplier.id', '=', 'supplier_shipping_late_rule.supplier_id')
                     ->join('system_location', 'order.country', '=', 'system_location.code')
                     ->select([
                        'order_product.*',
                        'order.country as order_country',
                        'order.shipping_method as order_shipping_method',
                        'supplier_shipping_late_rule.*',
                    ])

                    ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->whereRaw("supplier_shipping_late_rule.region REGEXP CONCAT('(^|,)', system_location.region_code, '(,|$)')")
                                ->orWhereRaw("supplier_shipping_late_rule.include_location REGEXP CONCAT('(^|,)', order.country, '(,|$)')")
                                ->orWhereRaw("supplier_shipping_late_rule.region REGEXP '[*]'");
                        })
                        ->whereRaw("NOT (supplier_shipping_late_rule.no_location REGEXP CONCAT('(^|,)', order.country, '(,|$)'))");
                    })
                    ->whereRaw("DATEDIFF(CURRENT_DATE, order_product.fulfilled_at) > supplier_shipping_late_rule.date_late")
                    ->orWhere(function ($query) {
                        $query->whereNotNull('order_product.received_at')
                              ->whereRaw("DATEDIFF(order_product.received_at, order_product.fulfilled_at) > supplier_shipping_late_rule.date_late");
                    });
        })
        ->where(function ($query) {
            $query->whereNotNull('order_product.received_at')
            ->whereRaw("DATEDIFF(order_product.received_at, order_product.fulfilled_at) >= " . self::DEFAULT_ORDER_SHIPPING_LATE_DATES);
        })
        ->orWhere(function  ($query) {
            $query->whereNull('order_product.received_at')
            ->where('order_product.fulfill_status', '!=' ,'fulfilled')
            ->whereRaw("DATEDIFF(CURRENT_DATE, order_product.fulfilled_at) >= " . self::DEFAULT_ORDER_SHIPPING_LATE_DATES);
        })
        ;
        return $query;
    }

    public function scopeFulfillLate($query)
    {
        return $query
            ->where('fulfilled_at', '<', now()->subDays(5))
            ->whereIn('fulfill_status', [OrderProductFulfillStatus::PROCESSING, OrderProductFulfillStatus::EXCEPTION]);
    }

    public function scopeTrackingNotFound($query)
    {
        return $query
            ->where('fulfilled_at', '<', now()->subDays(8))
            ->whereIn('fulfill_status', [
                OrderProductFulfillStatus::PROCESSING,
                OrderProductFulfillStatus::ON_DELIVERY,
                OrderProductFulfillStatus::EXCEPTION,
                OrderProductFulfillStatus::FULFILLED
            ])
            ->whereNotNull('tracking_code')
            ->where('tracking_code', '<>', '')
            ->where('tracking_status', TrackingStatusEnum::NOTFOUND);
    }

    public function scopeTrackingPreShipment($query)
    {
        return $query
            ->where('fulfilled_at', '<', now()->subDays(8))
            ->whereIn('fulfill_status', [
                OrderProductFulfillStatus::PROCESSING,
                OrderProductFulfillStatus::ON_DELIVERY,
                OrderProductFulfillStatus::EXCEPTION,
                OrderProductFulfillStatus::FULFILLED
            ])
            ->whereNotNull('tracking_code')
            ->where('tracking_code', '<>', '')
            ->where('tracking_status', TrackingStatusEnum::INFO_RECEIVED);
    }

    public function scopeOnDeliveryStats($query)
    {
        return $query
            ->where('fulfilled_at', '<', now()->subDays(18))
            ->where('fulfill_status', OrderProductFulfillStatus::ON_DELIVERY);
    }

    public function scopeTrackingLate($query)
    {
        return $query
            ->where(function ($q) {
                $q->whereNull('delivered_at')
                    ->where('fulfilled_at', '<=', now()->subDays(5));
                $q->orWhere(function ($subQuery) {
                    $subQuery->whereNotNull('delivered_at')
                        ->whereRaw('DATEDIFF(delivered_at, fulfilled_at) >= 5');
                });
            })
            ->where(function ($q) {
                $q->whereNull('tracking_code')
                    ->WhereNull('tracking_url');
                $q->orWhereIn('tracking_status', TrackingStatusEnum::trackingLateStatuses());
            });
    }

    public const FILTER_COLUMN_DATE = 'order_product.fulfilled_at';


    public function scopeFilterDateRangePaidAt(
        $query,
        $dateRange,
        $startDate = null,
        $endDate = null,
        $isOrderBy = true,
        $column = 'order.paid_at'
    ) {
        $dateRanges = [];
        $dateRanges['column'] = $column;
        $dateRanges['type'] = $dateRange;
        if ($dateRange === DateRangeEnum::CUSTOM) {
            $dateRanges['range'] = [
                $startDate,
                $endDate,
            ];
        }

        $query = filterQueryByDateRange($dateRanges, $query);
        if ($isOrderBy) {
            return $query->orderByDesc(self::FILTER_COLUMN_DATE);
        }

        return $query;
    }

    /** @return mixed */
    public function getCustomOptionsPbAttribute()
    {
        $options = json_decode($this->custom_options, true);
        if (empty($options)) {
            return null;
        }
        return $options[CustomOptionType::PB] ?? null;
    }

    /** @return mixed */
    public function getCustomOptionsRegularAttribute()
    {
        $options = json_decode($this->custom_options, true);
        if (empty($options)) {
            return null;
        }
        return $options[CustomOptionType::REGULAR] ?? null;
    }

    public function putCustomOptionsPB(array $pbCustomInfo): void
    {
        $pbOptions = json_encode($pbCustomInfo['options'], JSON_UNESCAPED_SLASHES);
        $hash = md5($pbOptions);
        $pbCustomInfo['print_url'] = replacePBArtworkURL($pbCustomInfo['print_url'], $hash);
        $this->custom_options = json_encode([
            CustomOptionType::REGULAR => $this->custom_options,
            CustomOptionType::PB => json_encode($pbCustomInfo, JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES),
        ], JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES);
    }

    /**
     * @return array|null
     * @throws Exception
     */
    public function getDesignPbAttribute(): ?array
    {
        $customOptions = isset($this->custom_options_pb) ? json_decode($this->custom_options_pb, true) : [];
        $options = $customOptions['options'] ?? [];
        $printUrl = $customOptions['print_url'] ?? [];
        $artworkId = array_key_first($options);
        if ($artworkId === null) {
            return null;
        }
        return [
            'id' => $this->id,
            'artwork_id' => $artworkId,
            'print_area' => 'front',
            'custom_attributes' => $options,
            'print_url' => $printUrl,
        ];
    }

    public function productReview(): HasOne
    {
        return $this->hasOne(ProductReview::class);
    }

    public function trackingStatus(): CustomBelongsTo
    {
        return $this->belongsTo(TrackingStatus::class, 'tracking_code', 'tracking_code', callback: function ($trackingCode)  {
            $arrTrackingCode = Str::of($trackingCode)->explode(',')->filter()->toArray();
            return data_get($arrTrackingCode, 0);
        });
    }

    public function getPrintUrlAttribute()
    {
        $options = $this->custom_options_pb;
        if (empty($options)) {
            return null;
        }
        $options = json_decode($options, true);
        return $options['print_url'] ?? null;
    }

    public function pricing(): BelongsTo
    {
        return $this->belongsTo(Pricing::class, 'template_id', 'product_id');
    }

    // don't trust ide, this function getSenPoints still in used ex: ProcessOrderCompleted::174
    public function getSenPoints($order, &$senPoints): float
    {
        // get sen points
        $productPoint = $this->product_point;
        // default
        $point = 1;
        if (!is_null($productPoint)) {
            $points = json_decode($productPoint->by_locations, true);
            $point = getSenPointsByLocation($points, $order->country, $productPoint->points);

            //checking if the product is on promotion
            $promotion = ProductPromotion::query()
                ->where('product_id', $productPoint->product_id)
                ->where('start_time', '<=', now())
                ->where('end_time', '>=', now())
                ->first();

            if (!is_null($promotion)) {
                $point = $promotion->point;
            }
        }
        $totalPoint = $point * $this->quantity;
        $senPoints += $totalPoint;

        return $totalPoint;
    }

    /**
     * @param    Builder    $q
     *
     * @return Builder
     */
    public function scopeMissingFulfillCost(Builder $q): Builder
    {
        return $q->where(function($q) {
            $q->orWhereNull('fulfill_base_cost')
                ->orWhereNull('fulfill_shipping_cost')
                ->orWhere('fulfill_base_cost', 0)
                ->orWhere('fulfill_shipping_cost', 0);
        });
    }

    public function rememberOldData(): void
    {
        $this->old_seller_profit = $this->seller_profit;
        $this->old_quantity = $this->quantity;
        $this->old_artist_profit = $this->artist_profit;
        $this->old_shipping_cost = $this->shipping_cost;
        $this->old_discount_amount = $this->discount_amount;
        $this->old_total_amount = $this->total_amount;
    }

    public function removeOldData(): void
    {
        unset(
            $this->old_seller_profit,
            $this->old_artist_profit,
            $this->old_shipping_cost,
            $this->old_discount_amount,
            $this->old_quantity,
            $this->old_total_amount
        );
    }

    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'campaign_id');
    }

    /**
     * @param SystemLocation|null $location
     *
     * @return $this
     */
    public function setFulfillLogUnMatched(?SystemLocation $location): OrderProduct
    {
        $this->fulfill_exception_log = 'Assign Supplier failed by country. Order Id: '
            . $this->order_id
            . '. Order Location: '
            . $location;

        return $this;
    }

    /**
     * @return $this
     */
    public function setFulfillLogNotFoundVariant(): OrderProduct
    {
        $this->fulfill_exception_log =
            'Assign Supplier failed by OrderOption . Order Id: '
            . $this->order_id
            . '. Order Product Id: ' . $this->id
            . '. Template Id: ' . $this->template_id
            . '. VariantKey: ' . $this->options;

        return $this;
    }

    /**
     * Khi đơn hàng ship đến US hoặc EU thì đánh dấu là cross shipping
     * nếu khớp sản phẩm fulfill là * (world wide). Hoặc US thì khớp
     * với EU và ngược lại v iệc này hỗ trợ đội vận hành lọc những đơn
     * này để xử lí thủ công, tối ưu chi phí vận chuyển
     *
     * @param     string          $rawLocSup
     * @param     string|null     $oLocCode
     *
     * @return $this
     */
    public function determineCrossShipping(string $rawLocSup, ?string $oLocCode): OrderProduct
    {
        // Chuyển order location về EU nếu là EU, không thì giữ nguyên
        $oLocCode = CountryEnum::isEu($oLocCode) ? 'EU' : $oLocCode;

        // Chuyển danh sách supplier location về EU nếu có EU, không thì giữ nguyên
        $supLocCodes = Str::of($rawLocSup)
            ->explode(',')
            ->map(fn($code) => CountryEnum::isEu($code) ? 'EU' : $code);

        $this->markCrossShipping(
            $supLocCodes->contains($oLocCode) ? 0 : 1
        );

        return $this;
    }

    /**
     * @return $this
     */
    public function markCrossShipping(?int $value = null): OrderProduct
    {
        $this->cross_shipping = $value ?? 0;

        return $this;
    }

    /**
     * @return bool
     */
    public function isCrossShipping(): bool
    {
        return $this->cross_shipping === 1;
    }

    /**
     * @return bool
     */
    public function isAiMockupOrder(): bool
    {
        return $this->campaign_type === ProductSystemTypeEnum::AI_MOCKUP;
    }

    /**
     * @return bool
     */
    public function isDesignBySenPrints(): bool
    {
        return $this->isAiMockupOrder() || $this->design_by === DesignByEnum::SENPRINTS;
    }

    /**
     * Bỏ qua fulfill khi:
     * - Chưa gắn sup
     * - Trạng thái fulfill khác unfilled, rejected
     *
     * @return bool
     */
    public function skipFulfill(): bool
    {
        // Chưa gắn sup
        if (!$this->supplier_id) {
            return true;
        }
        // Trạng thái fulfill khác unfulfilled, rejected
        return !in_array(
            $this->fulfill_status,
            [OrderProductFulfillStatus::UNFULFILLED, OrderProductFulfillStatus::REJECTED],
            true
        );
    }

    public function getAssignedSupplierAt()
    {
        $order = $this->order;

        if (empty($order->paid_at)) {
            return null;
        }

        $autoAssignTime = $order->paid_at->addHours(12);
        if (in_array($order->type, [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])) {
            $autoAssignTime = $order->paid_at->addHour();
        }

        if (empty($this->assigned_supplier_at) || $autoAssignTime->isAfter($this->assigned_supplier_at)) {
            return $autoAssignTime;
        }

        return $this->assigned_supplier_at;
    }

    /**
     * Giá bán của sản phẩm do sen đặt
     *
     * @param float|null|string $value
     *
     * @return $this
     */
    public function setBaseCostAttribute($value): OrderProduct
    {
        $this->attributes['base_cost'] = $value;

        $this->calcFulfillProfit();

        return $this;
    }

    /**
     * Phí ship của sản phẩm do sen đặt
     *
     * @param float|null|string $value
     *
     * @return $this
     */
    public function setShippingCostAttribute($value): OrderProduct
    {
        $this->attributes['shipping_cost'] = $value;

        $this->calcFulfillProfit();

        return $this;
    }

    /**
     * Giá bán của sản phẩm do supplier đặt
     *
     * @param float|null|string $value
     *
     * @return $this
     */
    public function setFulfillBaseCostAttribute($value): OrderProduct
    {
        $this->attributes['fulfill_base_cost'] = $value;

        $this->calcFulfillProfit();

        return $this;
    }

    /**
     * Phí ship của supplier
     *
     * @param float|null|string $value
     *
     * @return $this
     */
    public function setFulfillShippingCostAttribute($value): OrderProduct
    {
        $this->attributes['fulfill_shipping_cost'] = $value;

        $this->calcFulfillProfit();

        return $this;
    }

    /**
     * Tính fulfill profit
     *
     * @return OrderProduct
     */
    public function calcFulfillProfit(): OrderProduct
    {
        // fulfill_profit     = tổng phí của sen      - tổng phí của supplier
        $this->fulfill_profit = $this->platformCost() - $this->supplierCost();

        return $this;
    }

    /**
     * @return float
     */
    public function platformCost()
    {
        return $this->base_cost + $this->shipping_cost;
    }

    /**
     * @return float
     */
    public function supplierCost()
    {
        return $this->fulfill_base_cost + $this->fulfill_shipping_cost;
    }

    /**
     * @return bool
     */
    public function isPrintType2dAndEmbroidery(): bool
    {
        return !in_array($this->full_printed, [ProductPrintType::PRINT_2D, ProductPrintType::EMBROIDERY, ProductPrintType::HANDMADE], true);
    }

    /**
     * @return bool
     */
    public function isPrintTypeEmbroidery(): bool
    {
        return $this->full_printed === ProductPrintType::EMBROIDERY;
    }

    /**
     * @return bool
     */
    public function isNoNeedDesignProductType(): bool
    {
        return $this->full_printed === ProductPrintType::HANDMADE;
    }

    public function skipAssignSupplier(): bool
    {
        return in_array($this->fulfill_status, [
            OrderProductFulfillStatus::FULFILLED,
            OrderProductFulfillStatus::PROCESSING,
            OrderProductFulfillStatus::EXCEPTION,
        ], true);
    }

    /**
     * @return bool
     */
    public function wasReAssignSupplier(): bool
    {
        return ((int) $this->getOriginal('supplier_id') !== (int) $this->getAttribute('supplier_id'))
            || $this->getOriginal('fulfill_sku') !== $this->getAttribute('fulfill_sku')
            || (int) $this->getOriginal('fulfill_product_id') !== (int) $this->getAttribute('fulfill_product_id');
    }

    /**
     * @return bool
     */
    public function justCreated(): bool
    {
        return $this->fulfill_status === OrderProductFulfillStatus::PENDING;
    }

    public const QUERY_LATE_TIME_CONDITION = "IF(
        order_product.received_at IS NOT NULL,
        DATEDIFF(order_product.received_at, order_product.fulfilled_at),
        DATEDIFF(CURRENT_DATE, order_product.fulfilled_at)
    )";

    public const QUERY_PRODUCT_DESIGNING_CONDITION = "IF( order_product.fulfill_status = 'designing', 1, 0 )";

    /**
     * @param array $mugsProductId
     * @return bool
     * @throws \Throwable
     */
    public function isNotEnoughCustomOptionDesigns(array $mugsProductId = []): bool
    {
        $qtyDesigns = $this->customOptionDesigns->count();
        $customPrintSpaces = $this->customOptionDesigns->pluck('print_space')->unique();
        if ($this->personalized > 0 && !$this->isPrintTypeEmbroidery()) {
            if ($this->isFullPrintedType()) {
                $printDesigns = $this->printDesigns->filter(function ($designs) {
                    $options = json_decode($this->options, true, 512, JSON_THROW_ON_ERROR) ?? [];
                    return (!empty($options['size']) && str_contains($options['size'], $designs->print_space)) || $designs->print_space === PrintSpaceEnum::DEFAULT;
                });
                $this->setRelation('printDesigns', $printDesigns);
            }
            if (!empty($mugsProductId) && in_array($this->template_id, $mugsProductId)) {
                return $qtyDesigns === 0 || $customPrintSpaces->filter(function ($printSpace) {
                    return !in_array($printSpace, $this->printDesigns->pluck('print_space')->unique()->toArray(), true);
                })->count() > 0;
            }
            return $qtyDesigns < $this->printDesigns->count();
        }
        return $this->isNotEnoughProductDesigns();
    }

    /**
     * @param bool $allDesign
     * @return bool
     * @throws \Throwable
     */
    public function isNotEnoughProductDesigns(bool $allDesign = true): bool
    {
        $printSpaces = collect(json_decode($this->template->print_spaces, null, 512, JSON_THROW_ON_ERROR))->pluck('name')->toArray();
        $printDesigns = collect();
        $designs = $allDesign ? $this->getPrintDesigns() : $this->printDesigns;
        foreach ($designs as $design) {
            if (Str::contains(data_get($design, 'print_space'), $printSpaces)) {
                $printDesigns->push($design);
            }
        }
        $this->setRelation('printDesigns', $printDesigns);
        return $printDesigns->count() === 0;
    }

    /**
     * @return bool
     * @throws \Throwable
     */
    public function isNotEnoughAiDesigns(): bool
    {
        $printSpaces = AiPrompt::query()->where([
            'seller_id' => $this->seller_id,
            'campaign_id' => $this->campaign_id,
            'type' => PromptTypeEnum::DESIGN,
        ])->get()->pluck('print_space')->unique()->toArray();
        if (count($printSpaces) === 0) {
            return true;
        }
        if ($this->personalized > 0 || !empty($this->custom_options)) {
            $qtyDesigns = $this->customOptionDesigns->count();
            $printDesigns = $this->customOptionDesigns->filter(function ($design) use ($printSpaces) {
                return Str::contains($design->print_space, $printSpaces);
            });
            $this->setRelation('printDesigns', $printDesigns);
            return $qtyDesigns === 0 || $printDesigns->count() === 0;
        }
        return $this->isNotEnoughProductDesigns(false);
    }

    /**
     * @return bool
     * @throws \Throwable
     */
    public function isNotEnoughFulfillmentDesigns(): bool
    {
        $printSpaces = collect(json_decode($this->template->print_spaces, null, 512, JSON_THROW_ON_ERROR))->pluck('name')->toArray();
        $designs = $this->fulfillOrderDesigns;
        if ($designs->count() === 0) {
            return true;
        }
        $printDesigns = collect();
        $designPrintSpaces = [];
        foreach ($designs as $design) {
            $printSpace = data_get($design, 'print_space');
            $designPrintSpaces[] = $printSpace;
            if (Str::contains($printSpace, $printSpaces)) {
                $printDesigns->push($design);
            }
        }
        if ($this->isDesignBySenPrints()) {
            if ($this->fulfillOrderMockups->isEmpty()) {
                return true;
            }
            $mockupPrintSpaces = $this->fulfillOrderMockups->pluck('print_space')->unique()->toArray();
            if (count($mockupPrintSpaces) !== count($designPrintSpaces)) {
                return true;
            }
            foreach ($designPrintSpaces as $printSpace) {
                if (!Str::contains($printSpace, $mockupPrintSpaces)) {
                    return true;
                }
            }
        }
        $this->setRelation('printDesigns', $printDesigns);
        return $printDesigns->count() === 0;
    }

    /**
     * @return HasMany
     */
    public function logs(): HasMany
    {
        return $this->hasMany(OrderProductLog::class, 'order_product_id');
    }

    /**
     * @param $promotionRule
     * @param $totalQuantity
     * @param $totalAmount
     * @return bool
     */
    public function findX($promotionRule, &$totalQuantity, &$totalAmount): bool
    {
        try {
            $minimumAmount = 0;
            $minimumQuantity = 0;

            $jsonData = json_decode($promotionRule->rules);

            if (isset($jsonData->buy)) {
                $buyRules = $jsonData->buy;
                $minimumAmount = $buyRules->minimum_amount ?? 0;
                $minimumQuantity = $buyRules->minimum_quantity ?? 0;
            }
            $totalQuantity += $this->quantity;
            $totalAmount += $this->price;
            if ($this->matchRule($promotionRule) && $this->quantity > 0) {
                if ($this->quantity === 0) {
                    return false;
                }

                if ($totalQuantity >= $minimumQuantity && $totalAmount >= $minimumAmount) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Is product in store, campaign or collection
     * @param $promotionRule
     * @return bool
     */
    private function matchRule($promotionRule): bool
    {
        $promotionRules = json_decode($promotionRule?->rules);

        if (
            $this->quantity === 0 || $this->fulfill_status === OrderProductFulfillStatus::OUT_OF_STOCK
            || $this->fulfill_status === OrderProductFulfillStatus::CANCELLED
        ) {
            return false;
        }
        if (isset($promotionRules?->get?->same_campaign) && $promotionRules?->get?->same_campaign) {
            if ($this->campaign_id == $this->related_campaign_id && $this->product_id == $this->related_product_id) {
                return false;
            }
        } else {
            if ($this->campaign_id == $this->related_campaign_id) {
                return false;
            }
        }

        if (!empty($promotionRule->campaign_id) && $this->related_campaign_id !== $promotionRule->campaign_id) {
            return false;
        }

        if (
            $promotionRule->collections_id
            && $this->relationLoaded('collections')
            && !$this->collections->contains('collections_id', $promotionRule->collections_id)
        ) {
            return false;
        }

        if (!empty($promotionRule->template_id)) {
            return $this->template_id === $promotionRule->template_id;
        }

        return true;
    }

    /**
     * @param $promotionRule
     * @param $totalQuantity
     * @return bool
     */
    public function findY($promotionRule, $totalQuantity): bool
    {
        $jsonData = json_decode($promotionRule->rules);
        $getYRules = $jsonData->get;
        $getQuantity = $getYRules->quantity ?? 0;
        $discountPercentage = $getYRules->discount_percentage ?? 0;
        $discountPercentage /= 100;

        if ($this->matchGetYRule($promotionRule) && $this->quantity > 0) {
            if ($this->quantity === 0) {
                return false;
            }

            $discountAmount = $this->price * $discountPercentage;
            $this->discount_amount += $discountAmount * ($this->quantity ?? 0);

            if ($totalQuantity >= $getQuantity) {
                return true;
            }
        }

        return $totalQuantity > 0;
    }

    /**
     * @param $promotionRule
     * @return bool
     */
    public function matchGetYRule( $promotionRule): bool
    {
        $jsonData = json_decode($promotionRule->rules);
        $getYRule = $jsonData->get;

        if (!empty($getYRule->campaigns_id)) {
            return $this->campaign_id !== $getYRule->campaigns_id;
        }

        if (
            !empty($getYRule->collections_id)
            && $this->relationLoaded('collections')
            && !$this->collections->contains('collections_id', $getYRule->collections_id)
        ) {
            return false;
        }

        return true;
    }

    /**
     * @return bool
     */
    public function skipFulfillByCampaignTradeMark(): bool
    {
        if (!$this->campaign_id) {
            return false;
        }
        $supplier = $this->supplier;
        if (!$supplier || empty($supplier->trademark_keywords)) {
            return false;
        }
        $keywords = TradeMarkService::getTradeMarkKeywordByCampaignId($this->seller_id, $this->campaign_id);
        if (empty($keywords)) {
           return false;
        }
        $trademarkKeywords = collect($supplier->trademark_keywords)->filter()->unique()->values()->map(fn ($keyword) => trim($keyword))->map(fn ($keyword) => strtolower($keyword))->toArray();
        $isViolated = false;
        foreach ($keywords as $keyword) {
            if (TradeMarkService::isTradeMarkKeyword($trademarkKeywords, $keyword)) {
                $isViolated = true;
                break;
            }
        }
        return $isViolated;
    }

    public function isTikTokOrderProduct(): bool
    {
        return (bool) $this->order->tiktok_shop;
    }

    public function isWoocommerceOrderProduct(): bool
    {
        return str_starts_with($this->order->order_number, 'WP-');
    }

    public function queryDesignButNotUploadedDesign (&$orderProduct, $type, $files, $designs) {
        $fulfillStatus = $orderProduct->fulfill_status;
        if ($fulfillStatus == FulfillmentStatusEnum::DESIGNING) {
            $fileExisted = $files
                ->where('product_id', $orderProduct->product_id)
                ->where('type', FileTypeEnum::DESIGN)
                ->where('status', FileStatusEnum::ACTIVE)
                ->first();
            $designExisted = $designs
                ->where('order_product_id', $orderProduct->id)
                ->where('status', DesignStatusEnum::ACTIVE)
                ->first();
            if ($type == NeedDesignTagsEnum::UPLOADED_DESIGN_BUT_NOT_APPROVED) {
                if ($orderProduct->personalized == PersonalizedType::NONE && !empty($fileExisted)) {
                    $orderProduct->uploaded_but_not_approved = 1;
                } else if ($orderProduct->personalized != PersonalizedType::NONE && !empty($designExisted)) {
                    $orderProduct->uploaded_but_not_approved = 1;
                } else {
                    $orderProduct->uploaded_but_not_approved = 0;
                }
            } else if ($type == NeedDesignTagsEnum::DESIGNING_BUT_NOT_UPLOADED_DESIGN) {
                if ($orderProduct->personalized == PersonalizedType::NONE && empty($fileExisted)) {
                    $orderProduct->designing_but_not_uploaded_design = 1;
                } else if ($orderProduct->personalized != PersonalizedType::NONE && empty($designExisted)) {
                    $orderProduct->designing_but_not_uploaded_design = 1;
                } else {
                    $orderProduct->designing_but_not_uploaded_design = 0;
                }
            }
        } else {
            if ($type == NeedDesignTagsEnum::UPLOADED_DESIGN_BUT_NOT_APPROVED) {
                $orderProduct->uploaded_but_not_approved = 0;
            } else if ($type == NeedDesignTagsEnum::DESIGNING_BUT_NOT_UPLOADED_DESIGN) {
                $orderProduct->designing_but_not_uploaded_design = 0;
            }
        }
    }

    public function getOptionSize(): ?string
    {
        if (empty($this->options)) {
            return null;
        }

        $options = json_decode($this->options, true);

        return $options['size'] ?? null;
    }

    public function getPrintSpacesName(): array
    {
        $productTemplate = $this->template;
        if(!$productTemplate) {
            return [];
        }

        $printSpaces = json_decode($productTemplate->print_spaces, true);
        $printSpaceNames = array_column($printSpaces, 'name');
        $printSpaceNames[] = PrintSpaceEnum::DEFAULT;

        return $printSpaceNames;
    }

    public function getDefaultPrintSpace(): string
    {
        $productTemplate = $this->template;
        $printSpaceNames = $this->getPrintSpacesName();
        $optionSize = $this->getOptionSize();

        if ($productTemplate->full_printed) {
            foreach ($printSpaceNames as $printSpaceName) {
                if (str_contains($optionSize, $printSpaceName)) {
                    return $printSpaceName;
                }
            }
        } else if (!empty($printSpaceNames)) {
            return $printSpaceNames[0];
        }

        return PrintSpaceEnum::DEFAULT;
    }

    /**
     * Find variant by its key
     * @param string $variantKey
     * @return ProductVariant|null
     */
    public function findVariantByKey(string $variantKey, string $country): ?ProductVariant
    {
        $variants = ProductVariant::query()
            ->where('product_id', $this->template_id)
            ->where('variant_key', $variantKey)
            ->get();
        $location = getLocationByCode(strtoupper($country));
        $regionCodes = $location?->getRegionCodes() ?? ['*'];

        // Lọc các variant có location_code nằm trong regionCodes
        // Sau đó sắp xếp theo thứ tự xuất hiện trong regionCodes và lấy variant đầu tiên
        return $variants->whereIn('location_code', $regionCodes)
            ->sortBy(function ($variant) use ($regionCodes) {
                return array_search($variant->location_code, $regionCodes);
            })
            ->first();
    }

    public function getSellerPrice()
    {
        return $this->product->price;
    }

    /**
     * @param $seller
     * @return int
     */
    public function getTotalDesign($seller = null): int
    {
        try {
            $seller ??= $this->seller;
            $size = $this->full_printed ? $this->size : null;
            return File::query()
                ->onSellerConnection($seller)
                ->select(
                    [
                        'id',
                        'file_url',
                        'file_url_2',
                        'type',
                        'print_space',
                    ]
                )
                ->where([
                    'product_id' => $this->product_id,
                    'campaign_id' => $this->campaign_id,
                    'type' => FileTypeEnum::DESIGN,
                    'option' => FileRenderType::PRINT,
                ])
                ->when(!empty($size), function ($q) use ($size) {
                    // check if $size includes column print_space
                    return $q->where(function ($q) use ($size) {
                        return $q->whereRaw("INSTR('$size', `print_space`)")
                            ->orWhere('print_space', 'default');
                    });
                })
                ->orderBy('print_space')
                ->count();
        } catch (Exception $e) {
            logToDiscord('getTotalDesign error: ' . $e->getMessage());
            return 0;
        }
    }
}
