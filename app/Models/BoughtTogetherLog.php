<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\BoughtTogetherLog
 *
 * @property int $id
 * @property int|null $product_id1
 * @property int|null $product_id2
 * @property int $order_id
 * @property int|null $customer_id
 * @property int|null $seller_id
 * @property int|null $store_id
 * @property string $timestamp
 * @property int|null $campaign_id1
 * @property int|null $template_id1
 * @property int|null $campaign_id2
 * @property int|null $template_id2
 * @property-read \App\Models\Product|null $product
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereCampaignId1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereCampaignId2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereProductId1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereProductId2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereTemplateId1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereTemplateId2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BoughtTogetherLog whereTimestamp($value)
 * @mixin \Eloquent
 */
class BoughtTogetherLog extends Model
{

    protected $connection = 'pgsql';
    protected $table = 'bought_together_log';
    protected $guarded = [];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id2');
    }
}
