<?php

namespace App\Models;

use App\Enums\CampaignStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\ProductType;
use App\Traits\Filterable;
use App\Traits\HasRelationShipsCustom;
use App\Traits\ScopeFilterAnalyticTrait;
use App\Traits\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

/**
 * App\Models\Campaign
 *
 * @property int $id
 * @property string|null $slug
 * @property int|null $campaign_id
 * @property int|null $supplier_id
 * @property int|null $seller_id
 * @property int|null $auth_id
 * @property int|null $company_id
 * @property int|null $template_id
 * @property string $name
 * @property string|null $name_ts
 * @property string|null $thumb_url
 * @property string|null $options
 * @property string $mockup_type mockup type
 * @property string|null $default_option
 * @property string|null $print_spaces
 * @property string $currency_code
 * @property string|null $market_location
 * @property float $base_cost
 * @property string|null $base_costs
 * @property float $price
 * @property float $old_price
 * @property float $combo_price
 * @property float $shipping_cost
 * @property string $status
 * @property string $product_type
 * @property string|null $description
 * @property string|null $description_ts
 * @property float $extra_print_cost Extra print cost
 * @property string|null $start_time
 * @property \Illuminate\Support\Carbon|null $end_time
 * @property int $show_countdown
 * @property int|null $default_product_id
 * @property int|null $default_mockup_id
 * @property string|null $tracking_code
 * @property string $tm_status
 * @property int|null $sync_status Sync campaign info to elastic-search
 * @property string|null $elastic_document_id
 * @property string|null $sku
 * @property float $score
 * @property float $time_score
 * @property int $sales_score
 * @property int|null $priority
 * @property string $pricing_mode
 * @property string $public_status
 * @property string|null $google_category_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $is_deleted
 * @property string|null $attributes
 * @property float $pre_discounted_price
 * @property string $render_mode Campaign render mode
 * @property int $personalized
 * @property int $full_printed
 * @property int $allow_bulk 1:allow_bulk
 * @property int $temp_status
 * @property string|null $visited_at
 * @property int $google_crawled_status
 * @property string $system_type
 * @property int $archived
 * @property-read \App\Models\Category|null $categories
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Collection> $collections
 * @property-read int|null $collections_count
 * @property-read \App\Models\Product|null $defaultProduct
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $designs
 * @property-read int|null $designs_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $images
 * @property-read int|null $images_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductReview> $productReviews
 * @property-read int|null $product_reviews_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product> $products
 * @property-read int|null $products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PromotionRule> $promotions
 * @property-read int|null $promotions_count
 * @property-read \App\Models\User|null $seller
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Store> $stores
 * @property-read int|null $stores_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product> $template_products
 * @property-read int|null $template_products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Upsell> $upsell
 * @property-read int|null $upsell_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductVariant> $variants
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductVariant> $campaign_products
 * @property-read int|null $variants_count
 * @property-read string|null $domain
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign addExcludeAnalytic(array $arrExclude)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign addFilterAnalytic(array $arrayFilter, array $dateRanges, $sellerId = null, $arrUnsetFilter = [])
 * @method static \Database\Factories\CampaignFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign query()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereAllowBulk($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereArchived($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereAuthId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereBaseCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereBaseCosts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereCurrencyCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereDefaultMockupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereDefaultOption($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereDefaultProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereDescriptionTs($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereElasticDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereExtraPrintCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereFullPrinted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereGoogleCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereGoogleCrawledStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereIsDeleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereMarketLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereMockupType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereNameTs($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereOldPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign wherePersonalized($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign wherePreDiscountedPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign wherePricingMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign wherePrintSpaces($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereProductType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign wherePublicStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereRenderMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereSalesScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereShippingCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereShowCountdown($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereSupplierId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereSyncStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereSystemType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereTempStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereThumbUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereTimeScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereTmStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereTrackingCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereVisitedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign withoutTrashed()
 * @mixin \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
 */
class Campaign extends Model
{
    use HasFactory;
    use SoftDeletes;
    use ScopeFilterAnalyticTrait;
    use Filterable;
    use HasRelationShipsCustom;

    public const COMBO_DISCOUNT = 10; // Default combo discount percentage

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'product';
    protected $fillable = [
        'name',
        'name_ts',
        'thumb_url',
        'description',
        'description_ts',
        'seller_id',
        'auth_id',
        'slug',
        'start_time',
        'end_time',
        'show_countdown',
        'default_product_id',
        'template_id',
        'tracking_code',
        'price',
        'old_price',
        'combo_price',
        'status',
        'render_mode',
        'tm_status',
        'personalized',
        'product_type',
        'public_status',
        'currency_code',
        'market_location',
        'mockup_type',
        'default_option',
        'options',
        'system_type',
        'temp_status',
        'full_printed',
        'sync_status',
        'template_campaign_id'
    ];

    // format to restore html5 datetime-local input
    // https://laravel.com/docs/8.x/eloquent-mutators#date-casting
    protected $casts = [
        'end_time' => 'datetime:Y-m-d\TH:i',
    ];
    protected $batch = false;

    /**
     * @param array $attributes
     * @param $batch
     */
    public function __construct(array $attributes = [], $batch = false)
    {
        $this->batch = $batch;
        parent::__construct($attributes);
    }

    /**
     * @return string
     */
    public function getTable()
    {
        if ($this->batch) {
            return $this->table;
        }
        return parent::getTable();
    }

    protected static function booted()
    {
        static::addGlobalScope('campaign_type', function ($query) {
            $query->whereIn('product_type', [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
                ProductType::CAMPAIGN_TEMPLATE,
            ]);
        });
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id')
            ->select('id', 'name', 'nickname', 'email', 'status', 'custom_payment', 'sharding_status', 'db_connection')
            ->withDefault([
                'id' => '',
                'email' => '<EMAIL>',
                'name' => 'Unknown',
                'nickname' => null
            ]);
    }

    public function stores(): HasManyThrough
    {
        return $this->hasManyThrough(
            Store::class,
            StoreProduct::class,
            'product_id',
            'id',
            'id',
            'store_id'
        );
    }

    public function collections(): HasManyThrough
    {
        return $this->hasManyThrough(
            Collection::class,
            ProductCollection::class,
            'product_id',
            'id',
            'id',
            'collection_id'
        );
    }

    public function pivotCollections()
    {
        return $this->belongsToMany(Collection::class, 'product_collection', 'product_id', 'collection_id')
            ->withPivot(['seller_id', 'created_by']);
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'campaign_id', 'id');
    }

    public function expressOrMockupProducts()
    {
        return $this->hasMany(Product::class, 'campaign_id', 'template_id');
    }

    public function images(): HasMany
    {
        return $this->hasMany(File::class, 'campaign_id', 'id')
            ->select(['id', 'campaign_id', 'file_url', 'file_url_2', 'product_id', 'type', 'type_detail', 'option', 'token', 'print_space', 'mockup_id'])
            ->where('type', FileTypeEnum::IMAGE)
            ->orderByDesc('type_detail')
            ->orderByDesc('print_space')
            ->orderBy('position')
            ->orderBy('id');
    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'campaign_id', 'id');
    }

    public function defaultProduct(): HasOne
    {
        return $this->hasOne(Product::class, 'id', 'default_product_id');
    }

    public function upsell(): HasMany
    {
        return $this->hasMany(Upsell::class, 'product_id', 'id');
    }

    public function promotions(): HasMany
    {
        return $this->hasMany(PromotionRule::class, 'campaign_id', 'id');
    }

    public function designs(): HasMany
    {
        return $this->hasMany(File::class, 'campaign_id', 'id')
            ->when($this->system_type === ProductSystemTypeEnum::COMBO, function ($query) {
                $query->whereNotNull('product_id'); // For combo campaign, designs must be linked to specific products
            });
    }

    /**
     * Get designs for a specific product in combo
     * @param int $productId
     * @return HasMany
     */
    public function getProductDesigns(int $productId): HasMany
    {
        return $this->hasMany(File::class, 'campaign_id', 'id')
            ->where('product_id', $productId);
    }

    public function productReviews(): HasMany
    {
        return $this->hasMany(ProductReview::class);
    }

    /**
     * Get campaign info with campaign ids
     * $fields[id, name, slug, description]
     * @param array $campaignIds
     * @return array
     */
    public static function get_campaign_info_by_campaign_ids(array $campaignIds = []): array
    {
        $campaigns = self::query()
            ->select([
                'id',
                'name',
                'slug',
                'description',
                'default_product_id',
            ])
            ->whereIn('id', $campaignIds)
            ->get();

        $data = [];

        if ($campaigns->count() > 0) {
            foreach ($campaigns as $campaignIndex => $campaign) {
                if (!isset($data[$campaign->id])) {
                    $data[$campaign->id] = [
                        'name' => $campaign->name,
                        'slug' => $campaign->slug,
                        'description' => $campaign->description,
                        'default_product_id' => $campaign->default_product_id,
                    ];
                }

                unset($campaigns[$campaignIndex]);
            }
        }

        return $data;
    }

    public function categories(): HasOneThrough
    {
        return $this->hasOneThrough(
            Category::class,
            ProductCategory::class,
            'product_id',
            'id',
            'campaign_id',
            'product_id',
        );
    }

    public function getNameAttribute(): string
    {
        // ref: https://stackoverflow.com/a/35184977/3872002
        // https://laravel.com/docs/8.x/eloquent-mutators#defining-a-mutator
        if (is_null($this->attributes['name'])) {
            return 'Draft #' . $this->id;
        }

        return $this->attributes['name'];
    }

    public const PENDING_THRESHOLD = 10;

    public const FILTER_COLUMN_DATE = 'product.created_at';

    public function template_products(): HasMany
    {
        return $this->hasMany(Product::class, 'campaign_id', 'id')->where('product_type', ProductType::PRODUCT_TEMPLATE);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function campaign_products(): HasMany
    {
        return $this->hasMany(Product::class, 'campaign_id', 'id')->where('product_type', ProductType::PRODUCT);
    }

    /**
     * @return bool
     */
    public function isDraft(): bool
    {
        return $this->status === CampaignStatusEnum::DRAFT;
    }

    /**
     * @param string $slug
     * @param $seller
     * @return Campaign|null
     */
    public static function findBySlugOfSeller(string $slug, $seller): ?static
    {
        return self::query()
            ->onSellerConnection($seller)
            ->select(['id', 'name', 'slug', 'thumb_url', 'status'])
            ->where([
                'slug' => $slug,
                'seller_id' => $seller->id,
            ])
            ->first();
    }

    public function getDomainAttribute(): ?string
    {
        $store = $this->stores->first();
        return $store->base_url ?? null;
    }

    /**
     * Check if this campaign is a combo campaign
     * @return bool
     */
    public function isCombo(): bool
    {
        return $this->system_type === ProductSystemTypeEnum::COMBO;
    }
    public function aiPrompt(): HasMany
    {
        return $this->hasMany(AiPrompt::class);
    }
}
