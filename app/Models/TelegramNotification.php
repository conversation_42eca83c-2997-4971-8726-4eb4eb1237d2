<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * App\Models\TelegramNotification
 *
 * @property int $seller_id
 * @property string|null $chat_id
 * @property int $enable
 * @property string|null $token
 * @method static \Illuminate\Database\Eloquent\Builder|TelegramNotification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TelegramNotification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TelegramNotification query()
 * @method static \Illuminate\Database\Eloquent\Builder|TelegramNotification whereChatId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TelegramNotification whereEnable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TelegramNotification whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TelegramNotification whereToken($value)
 * @mixin \Eloquent
 */
class TelegramNotification extends Model
{
    use HasFactory;

    protected $table = 'telegram_notification';

    protected $fillable = ['seller_id', 'token'];

    public $timestamps = false;
}
