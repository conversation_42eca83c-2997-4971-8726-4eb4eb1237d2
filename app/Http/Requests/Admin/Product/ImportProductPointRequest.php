<?php

namespace App\Http\Requests\Admin\Product;

use App\Rules\Admin\Product\ImportProductPointRule;
use Illuminate\Foundation\Http\FormRequest;

class ImportProductPointRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'file' => ['required']
        ];
        if ($this->hasFile('file')) {
            $rules['file'][] = new ImportProductPointRule($this->file('file'));
        }
        return $rules;
    }
}
