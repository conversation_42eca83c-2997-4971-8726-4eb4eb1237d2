<?php

namespace App\Http\Requests;

use App\Enums\CampaignStatusEnum;
use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ChangeCampaignStatusRequest extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return currentUser()->hasPermission('update_campaign');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'campaign_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product'),
            ],
            'status' => [
                'required',
                Rule::in([CampaignStatusEnum::getValues()])
            ]
        ];
    }
}
