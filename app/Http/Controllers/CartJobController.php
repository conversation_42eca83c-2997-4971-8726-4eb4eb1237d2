<?php

namespace App\Http\Controllers;

use App\Enums\AbandonedLogStatusEnum;
use App\Enums\CartJobProcessStatusEnum;
use App\Enums\NotificationTypeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Models\AbandonedLog;
use App\Models\CartJob;
use App\Models\Currency;
use App\Models\NotificationSetting;
use App\Models\Order;
use App\Services\StoreService;
use App\Traits\GetStoreDomain;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use App\Jobs\GeneralRegionOrderJob;

class CartJobController extends Controller
{
    use GetStoreDomain;

    /**
     * Create new cart job delay 1 hour when order created event triggered for wait order complete
     * @param Order $order
     * @return bool
     */
    public static function store(Order $order): bool
    {
        $orderId = $order->id;
        $cartJob = new CartJob();
        $cartJob->order_id = $orderId;
        $cartJob->next_email = '';

        if (app()->environment('development', 'local')) {
            $startTime = Carbon::now('UTC')->addMinute(); // for test on dev
        } else {
            $startTime = Carbon::now('UTC')->addHour(); //TODO: how many time after assign this
            // order status to abandoned @james
        }

        $cartJob->start_time = $startTime;
        return $cartJob->save();
    }

    /**
     * Update cart job with next email template
     * @param CartJob $cartJob
     * @param $nextEmail
     * @return bool
     */
    public static function update(CartJob $cartJob, $nextEmail): bool
    {
        try {
            /** @var NotificationSetting $notificationSetting */
            $notificationSetting = $cartJob->notifications->firstWhere('type', $nextEmail);

            if (!is_null($notificationSetting)) {
                $cartJob->next_email = null;
                $sendAfter = 0;
            } else {
                $cartJob->next_email = $nextEmail;
                $sendAfter = $notificationSetting->send_after; // seconds
            }

            $createdAt = new Carbon($cartJob->created_at);
            $startTime = $createdAt->addSeconds($sendAfter);
            $cartJob->start_time = $startTime;
            return $cartJob->save();
        } catch (\Exception $e) {
            logException($e, 'CartJobController@update');
            return false;
        }
    }

    /**
     * Get next recover email template by current email template name.
     */
    public static function getTemplate($currentTemp = null)
    {
        if ($currentTemp === null) {
            return NotificationTypeEnum::ABANDONED_CHECKOUT_EMAIL_1;
        }

        $templates = NotificationTypeEnum::getValues();

        if ($currentTemp) {
            foreach ($templates as $id => $temp) {
                if ($temp === $currentTemp) {
                    $nextId = $id + 1;

                    if ($nextId < count($templates)) {
                        return $templates[$nextId];
                    }
                }
            }
        }

        return null;
    }

    /**
     * Process cart job and send email to customer
     * @param int $orderId
     * @param string $message
     * @param string $subject
     * @param string $promotion_title
     * @param string $coupon
     * @param string $emailType
     * @return int
     */
    public static function process(
        int    $orderId,
        string $message,
        string $subject,
        string $promotion_title,
        string $coupon,
        string $emailType
    ): int
    {
        try {
            $order = Order::query()
                ->select([
                    'id',
                    'customer_id',
                    'order_number',
                    'total_quantity',
                    'total_shipping_amount',
                    'total_discount',
                    'total_amount',
                    'status',
                    'payment_status',
                    'store_id',
                    'access_token',
                    'customer_email',
                    'customer_name'
                ])
                ->where('id', $orderId)
                ->whereIn('status', [OrderStatus::PENDING, OrderStatus::DRAFT])
                ->with([
                    'customer:id,email,name',
                    'products:id,order_id,campaign_title,product_name,product_url,thumb_url,options,price,quantity,' .
                    'shipping_cost,discount_amount,total_amount'
                ])
                ->first();

            if (is_null($order)) {
                return CartJobProcessStatusEnum::ORDER_NOT_FOUND;
            }

            if ($order->customer && $order->payment_status === OrderPaymentStatus::UNPAID) { // Some order don't have customer info???
                if ($order->products->count() === 0) {
                    return CartJobProcessStatusEnum::EMPTY_CART;
                }

                // send email
                self::send($order, $message, $subject, $promotion_title, $coupon, $emailType);

                return CartJobProcessStatusEnum::EMAIL_SENT;
            }

            return CartJobProcessStatusEnum::CUSTOMER_NOT_FOUND;
        } catch (\Exception $e) {
            logException($e, 'CartJobController::process');
            return CartJobProcessStatusEnum::UNKNOWN;
        }
    }

    /**
     * @param Order $order
     * @param string $message
     * @param string $subject
     * @param string $promotion_title
     * @param string $coupon
     * @param string $emailType
     */
    public static function send(
        Order  $order,
        string $message,
        string $subject,
        string $promotion_title,
        string $coupon,
        string $emailType
    ): void
    {
        $storeInfo = StoreService::getStoreInfo($order->store_id);

        if (is_null($storeInfo)) {
            return;
        }

        $currency = Currency::query()->firstWhere('code', $order->currency_code);

        if (is_null($currency)) {
            $currency = Currency::query()->firstWhere('code', 'USD');

            if (is_null($currency)) {
                $currency = 'USD';
            }
        }

        $config = [
            'to' => $order->customer_email,
            'template' => 'buyer.abandoned_checkout',
            'data' => [
                'subject' => $subject,
                'name' => $order->customer_name,
                'email' => $order->customer_email,
                'message' => $message,
                'products' => $order->products,
                'order_number' => $order->order_number,
                'total_amount' => $order->total_amount,
                'total_shipping_amount' => $order->total_shipping_amount,
                'total_discount' => $order->total_discount,
                'cart_url' => self::getCartUrl($order, $coupon, $emailType),
                'promotion_title' => $promotion_title,
                'coupon' => $coupon,
                'store_name' => $order->store_name,
                'base_url' => config('senprints.base_url_seller'),
                'store_info' => $storeInfo,
                'currency' => $currency
            ]
        ];
        sendEmail($config);
    }

    public static function getCartUrl(Order $order, string $coupon = '', string $emailType = ''): string
    {
        $accessToken = $order->access_token;
        $domain = self::getDomainByStoreId($order->store_id);
        $utcQuery = "utm_source={$emailType}&utm_medium=email&utm_campaign=abandoned_email";
        $couponQuery = (empty($coupon) ? '' : '&discount=' . $coupon);
        return 'https://' . $domain . '/checkout/' . $accessToken . "?" . $utcQuery . $couponQuery;
    }

    public static function getSmsCartUrl(Order $order, string $coupon = '', string $cartKey = ''): string
    {
        $accessToken = $order->access_token;
        $domain = self::getDomainByStoreId($order->store_id);
        $utcQuery = "utm_source=sms&utm_medium=sms&utm_campaign=abandoned_sms";
        $couponQuery = (empty($coupon) ? '' : '&discount=' . $coupon);
        $cartKeyQuery = (empty($coupon) ? '' : '&cart_key=' . $cartKey);
        return 'https://' . $domain . '/checkout/' . $accessToken . "?" . $utcQuery . $couponQuery . $cartKeyQuery;
    }

    public function updateAbandonedCartStatus(Request $request): void
    {
        $cartKey = $request->get('cartKey');
        $orderToken = $request->get('orderToken');
        if ($orderToken) {
            $order = Order::query()->select(['id', 'access_token', 'payment_status', 'status'])->whereAccessToken($orderToken)->first();
            if (!empty($order)) {
                $paymentStatus = $order->payment_status;
                // if order is paid, update SmsLog status to success
                if ($paymentStatus === OrderPaymentStatus::PAID) {
                    $abandonedLog = AbandonedLog::query()->firstWhere([
                        'id' => $cartKey,
                        'reference_id' => $order->id
                    ]);

                    if ($abandonedLog !== null) {
                        GeneralRegionOrderJob::dispatch(
                            CartJobController::class,
                            'updateAbandonedCartStatusLogs',
                            [$abandonedLog, AbandonedLogStatusEnum::SUCCESS]
                        );
                    }
                }
            }
        }
    }

    public function trackOpenAbandonedCart(Request $request): void
    {
        $cartKey = $request->get('cartKey');
        $cart = AbandonedLog::query()->firstWhere('id', $cartKey);

        if ($cart === null) {
            return;
        }

        $totalOpen = $cart->total_open;
        $totalOpen++;

        GeneralRegionOrderJob::dispatch(
            CartJobController::class,
            'updateTrackAbandonedLogs',
            [$cart, (int)$totalOpen]
        );
    }

    /**
     * @param AbandonedLog|null $cart
     * @param int $totalOpen
     * @return void
     */
    public function updateTrackAbandonedLogs(?AbandonedLog $cart, int $totalOpen): void
    {
        if ($cart === null) {
            return;
        }
        $cart->update(['total_open' => $totalOpen]);
    }

    /**
     * @param AbandonedLog|null $cart
     * @param int $status
     * @return void
     */
    public function updateAbandonedCartStatusLogs(?AbandonedLog $cart, int $status): void
    {
        if ($cart === null) {
            return;
        }
        $cart->update(['recovered' => 1, 'status' => $status]);
    }
}
