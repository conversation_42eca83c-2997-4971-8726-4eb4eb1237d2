<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\CampaignStatusEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\PgsAndMysqlVersionEnum;
use App\Enums\ProductType;
use App\Enums\StoreStatusEnum;
use App\Models\IndexEventLogs;
use App\Models\IndexOrder;
use App\Models\IndexProduct;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\StatsOrder;
use App\Models\Store;
use App\Models\SystemConfig;
use App\Models\TempEventLog;
use App\Services\CampaignService;
use App\Services\FulfillmentService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use Carbon\Carbon;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Throwable;

class PingController extends Controller
{
    use ApiResponse;
    use ElasticClient;

    public function ping(): JsonResponse
    {
        try {
            $time = round(microtime(true) * 1000);
            try {
                $dbTest = Order::query()->orderByDesc('paid_at')->value('id');
            } catch (Throwable $e) {
                $dbTest = 0;
            }
            $endTime = round(microtime(true) * 1000);
            $dbPingTime = $endTime - $time;
            $time = $endTime;
            $cacheTest = cache()->put('test', $time, 60) == cache()->get('test');
            $endTime = round(microtime(true) * 1000);
            $cachePingTime = $endTime - $time;
            $time = $endTime;

            $data = [
                'time' => $time,
                'db_test' => $dbTest,
                'db_ping_time' => $dbPingTime . 'ms',
                'cache_test' => $cacheTest,
                'cache_ping_time' => $cachePingTime . 'ms',
                'shard_id' => config('senprints.shard_id'),
                'server_info' => config('senprints.server_info'),
                'hostname' => gethostname(),
                'region' => config('app.region'),
                'region_master' => config('app.region_master'),
                'version' => env('BITBUCKET_BUILD_NUMBER')
            ];

            // check if data have 0 or false value
            // $statusCode = $dbTest && $esTest && $cacheTest ? 200 : 500;
            $statusCode = $dbTest && $cacheTest ? 200 : 500;

            return $this->successResponse($data, '', $statusCode);
        } catch (Throwable $e) {
            return $this->errorResponse($e, 500);
        }
    }

    public function orderStatus(): JsonResponse
    {
        $lastTimeToCheck = 30;
        $lastPaidTimeToCheck = false;

        $date = new \DateTime();
        $date->setTimezone(new \DateTimeZone('+0700')); //GMT
        $currentHour = $date->format('G');

        if ($currentHour >= 13 && $currentHour < 17) {
            $lastTimeToCheck = 150;
        }

        if ($currentHour >= 17 && $currentHour < 19) {
            $lastTimeToCheck = 60;
        }

        if ($currentHour >= 19 && $currentHour < 23) {
            $lastTimeToCheck = 20;
        }

        if ($currentHour >= 23 && $currentHour < 0) {
            $lastTimeToCheck = 15;
            $lastPaidTimeToCheck = 20;
        }

        if ($currentHour >= 0 && $currentHour < 4) {
            $lastTimeToCheck = 15;
            $lastPaidTimeToCheck = 20;
        }

        if ($currentHour >= 4 && $currentHour < 9) {
            $lastTimeToCheck = 25;
            $lastPaidTimeToCheck = 30;
        }

        if ($currentHour >= 9 && $currentHour < 13) {
            $lastTimeToCheck = 60;
        }

        if (!$lastPaidTimeToCheck) {
            $lastPaidTimeToCheck = $lastTimeToCheck + 15;
        }

        $lastDayOrders = $this->countLastDayOrders();
        $baseOrders = 500;
        $baseOrderRate = 0;
        if ($lastDayOrders > 0) {
            $baseOrderRate = number_format($baseOrders / $lastDayOrders, 2);
            $lastPaidTimeToCheck = max($baseOrderRate * $lastPaidTimeToCheck, 15);
            $lastTimeToCheck = max($baseOrderRate * $lastTimeToCheck, 10);
        }

        try {
            $statusCode = 200;
            $message = 'Normal';

            $lastCreatedTime = Order::query()
                ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                ->orderByDesc('created_at')
                ->value('created_at');

            if ($lastCreatedTime->lt(now()->subMinutes($lastTimeToCheck))) {
                $statusCode = 500;
                $message = "No order created in $lastTimeToCheck minutes";
            }

            $lastPaidTime = Order::query()
                ->where('paid_at', '>', now()->subHours(2))
                ->where([
                    'payment_status' => OrderPaymentStatus::PAID
                ])
                ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                ->orderByDesc('paid_at')
                ->value('paid_at');

            if ($lastPaidTime->lt(now()->subMinutes($lastPaidTimeToCheck))) {
                $statusCode = 500;
                $message = "No order paid in $lastPaidTimeToCheck minutes";
            }

            return $this->successResponse([
                'lastCreatedTime' => $lastCreatedTime->addHours(7)->format('Y-m-d h:i:s'),
                'lastPaidTime' => $lastPaidTime->addHours(7)->format('Y-m-d h:i:s'),
                'lastDayOrders' => $lastDayOrders,
                'baseOrderRate' => $baseOrderRate,
                'lastTimeTocheck' => $lastTimeToCheck,
                'lastPaidTimeToCheck' => $lastPaidTimeToCheck
            ], $message, $statusCode);
        } catch (Throwable $e) {
            return $this->errorResponse($e, 500);
        }
    }

    public function health()
    {
        echo "backend-api OK";
    }

    private function countLastDayOrders()
    {
        return Cache::remember('countYesterdayOrders', 20 * 60, static function () {
            return Order::query()
                ->whereIn('type', [
                    OrderTypeEnum::REGULAR,
                    OrderTypeEnum::CUSTOM,
                ])
                ->where('paid_at', ">=", now()->subDay())
                ->count();
        });
    }

    public function getStoresActive(Request $request): array
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;

        if (!$request->has('senprint_key')) {
            abort(404);
        }

        $arr = [];
        $date = 10;
        $time = now()->subDays($date);

        $storeQuery = Store::query()
            ->select([
                'domain',
                'sub_domain',
            ])
            ->where('status', StoreStatusEnum::ACTIVE);

        if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
            $storeIdsHasVisits = TempEventLog::query()
                ->where('datestamp', '>=', $time)
                ->distinct()
                ->pluck('store_id');

            $storeIdsHasSales = StatsOrder::query()
                ->where('datestamp', '>=', $time)
                ->distinct()
                ->pluck('store_id');
        } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
            $storeIdsHasVisits = IndexEventLogs::query()
                ->where('datestamp', '>=', $time)
                ->distinct()
                ->pluck('store_id');
            $storeIdsHasSales = IndexOrder::query()
                ->where('paid_at', '>=', $time)
                ->distinct()
                ->pluck('store_id');
        }


        $storeQuery->clone()
            ->whereKey($storeIdsHasVisits)
            ->get()
            ->map(function ($each) use (&$arr) {
                $arr['hasVisitsDomains'][] = $each->base_url;
            });

        $storeQuery->clone()
            ->whereKey($storeIdsHasSales)
            ->get()
            ->map(function ($each) use (&$arr) {
                $arr['hasSalesDomains'][] = $each->base_url;
            });

        return $arr;
    }

    public function getActiveDomains()
    {
        Cache::clear();
        return Cache::remember('sp-active-domains', CacheTime::CACHE_1H, static function () {
            $data = Order::where('paid_at', '>=', now()->subDay())
                ->where('store_domain', '!=', '')
                ->groupBy('store_domain')->orderByRaw('count(*) desc')
                ->limit(100)->pluck('store_domain');

            if ($data->isEmpty()) {
                return [
                    'store.dev.senprints.net',
                    'upsell.store.senprints.net',
                    'kientest-dev-1.senprints.xyz'
                ];
            }

            return $data;
        });
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getTotalProductNeedToSyncToSingleStore(Request $request)
    {
        try {
            $limit = (int) $request->query('limit', 10000);
            $product = Product::query()
                ->withTrashed()
                ->select(DB::raw('count(*) as total'))
                ->where('sync_status', '=', Product::SYNC_DATA_TO_SINGLE_STORE)
                ->first();
            $total = $product ? $product->total : 0;
            $data = [
                'version' => '20230619',
                'total' => $total,
                'limit' => $limit,
            ];
            $statusCode = $total <= $limit ? 200 : 500;
            return $this->successResponse($data, '', $statusCode);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getTotalOrdersNeedToSyncToSingleStore(Request $request)
    {
        try {
            $limit = (int) $request->query('limit', 500);
            $orders = Order::query()->select(DB::raw('count(id) as total'))->whereRaw('(sync_at IS NULL OR sync_at < updated_at)')->withTrashed()->first();
            $total_orders = $orders ? $orders->total : 0;
            $data = [
                'version' => '20231228',
                'orders' => $total_orders,
                'limit' => $limit,
            ];
            $statusCode = $total_orders <= $limit ? 200 : 500;
            if ($statusCode === 200) {
                $orderProducts = OrderProduct::query()->select(DB::raw('count(id) as total'))->whereRaw('(sync_at IS NULL OR sync_at < updated_at)')->withTrashed()->first();
                $total_order_products = $orderProducts ? $orderProducts->total : 0;
                $statusCode = $total_order_products <= $limit ? 200 : 500;
                $data['order_products'] = $total_order_products;
            }
            return $this->successResponse($data, '', $statusCode);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function checkSyncCampsEsAndSinglestore(Request $request)
    {
        try {
            $data = CampaignService::checkSynchronizingCampsEsAndSinglestore();
            if (isset($data) && $data['sync_err']) {
                return $this->errorResponse($data, 500);
            }
            return $this->successResponse($data, '', 200);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function esSyncProductPendingCounter(Request $request)
    {
        try {
            $limit = (int) $request->query('limit', 10000);
            $product = Product::query()
                ->withTrashed()
                ->select(DB::raw('count(*) as total'))
                ->where('sync_status', '=', Product::SYNC_DATA_STATS_ENABLED)
                ->first();
            $total = $product ? $product->total : 0;
            $data = [
                'version' => '2023-10-18',
                'total' => $total,
                'limit' => $limit,
            ];
            $statusCode = $total <= $limit ? 200 : 500;
            return $this->successResponse($data, '', $statusCode);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function checkDbSyncLatencyUSAndSing(Request $request): JsonResponse
    {
        try {
            $limit = (int) $request->get('limit', 300);
            $secondsBehindMaster = getDatabaseSecondBehindMaster();
            if ($secondsBehindMaster < 0) {
                return $this->errorResponse('Cannot get seconds behind master', 500);
            }
            $minutesBehindMaster = round($secondsBehindMaster / 60, 2);
            $data = [
                'minutes_behind_master' => $minutesBehindMaster,
                'seconds_behind_master' => $secondsBehindMaster,
                'limit' => $limit,
            ];

            $statusCode = $secondsBehindMaster <= $limit ? 200 : 500;
            return $this->successResponse($data, '', $statusCode);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getTotalPendingJobsOnAllQueues(Request $request)
    {
        try {
            $limit = (int) $request->get('limit', 5000);
            $region = $request->get('region');
            $port = $request->get('port');
            $rabbitmqHost = $request->get('host');
            $protocol = $request->get('protocol', 'https');
            if (!empty($region)) {
                $region = strtoupper($region);
                $region = in_array($region, ['US', 'EU'], true) ? $region . "_" : null;
            }
            if (empty($rabbitmqHost)) {
                $rabbitmqHost = env("RABBITMQ_{$region}HOST");
                if (empty($rabbitmqHost)) {
                    return $this->errorResponse('Not found RabbitMQ host.', 500);
                }
            }
            // remove http or https from host
            $rabbitmqHost = preg_replace('/^https?:\/\//', '', $rabbitmqHost);
            if ($port && (int) $port !== 80 && (int) $port > 0) {
                $rabbitmqHost .= ':' . $port;
                $protocol = 'http';
            }
            $baseUrl = "$protocol://$rabbitmqHost/api";
            $httpHandler = Http::withBasicAuth(env("RABBITMQ_{$region}USER"), env("RABBITMQ_{$region}PASSWORD"))
                ->baseUrl($baseUrl)
                ->acceptJson()
                ->asJson();
            $result = $httpHandler->get('queues');
            if ($result->failed()) {
                return $this->errorResponse('Cannot get data from RabbitMQ', 500);
            }
            $total_jobs = 0;
            $queues = [];
            if ($result->successful()) {
                $result = $result->json();
                if (!empty($result)) {
                    $result = collect($result);
                    $queues = $result->select(['name', 'messages'])->map(function ($queue) {
                        return [
                            'queue_name' => $queue['name'],
                            'total_jobs' => $queue['messages'],
                        ];
                    });
                    $total_jobs = $queues->pluck('total_jobs')->sum();
                    $queues = $queues->filter(function ($queue) {
                        return $queue['total_jobs'] > 0;
                    })->values()->toArray();
                }
            }
            $data = [
                'version' => '2025-02-27',
                'region' => $region,
                'limit' => $limit,
                'total_jobs' => $total_jobs,
                'queues_report' => $queues,
            ];
            $statusCode = $total_jobs <= $limit ? 200 : 500;
            return $this->successResponse($data, '', $statusCode);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getLatestBlockedCampaigns(Request $request)
    {
        try {
            $limit = (int) $request->get('limit', 1000);
            $campaignIds = IndexProduct::query()
                ->select('id')
                ->where('status', CampaignStatusEnum::BLOCKED)
                ->whereIn('product_type', [
                    ProductType::CAMPAIGN,
                    ProductType::CAMPAIGN_EXPRESS
                ])
                ->withTrashed()
                ->orderByDesc('updated_at')
                ->limit($limit)
                ->pluck('id')
                ->toArray();
            $data = [
                'ids' => [],
                'limit' => $limit,
            ];
            if (empty($campaignIds)) {
                return $this->successResponse($data, '');
            }
            $data['ids'] = $campaignIds;
            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function scheduleHealth(Request $request): JsonResponse
    {
        try {
            $mins = (int) $request->get('mins', 5);
            $now = now();
            $settingObject = SystemConfig::getCustomConfig(CacheKeys::LAST_DATETIME_SCHEDULE_RUN);
            $last_run_time = $settingObject->value ?? null;
            $data = [
                'mins' => $mins,
                'now' => $now->toDateTimeString(),
                'last_run' => $last_run_time,
                'diff' => $last_run_time ? Carbon::parse($last_run_time)->diffForHumans($now) : null
            ];
            if (empty($last_run_time) || Carbon::parse($last_run_time)->addMinutes($mins)->lte($now)) {
                return $this->successResponse($data, code: 500);
            }
            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function pendingOrdersFulfillment(Request $request): JsonResponse
    {
        try {
            $limit = (int) $request->get('limit', 200);
            $limitation_setting = SystemConfig::getCustomConfig('fulfillment_limitation_post_orders');
            $limitRegular = 10;
            $limitExpress = 5;
            $limitFulfillment = 10;
            if ($limitation_setting) {
                $value = $limitation_setting->json_data;
                $limitation = json_decode($value, true);
                $limitRegular = data_get($limitation, 'regular', 10);
                $limitExpress = data_get($limitation, 'express', 5);
                $limitFulfillment = data_get($limitation, 'fulfillment', 10);
            }
            $pending_orders_ids = FulfillmentService::getPendingFulfillmentOrderIds(false);
            $total_pending_orders = count($pending_orders_ids);
            $data = [
                'version' => '2024-10-14',
                'limit' => $limit,
                'pending_orders' => $total_pending_orders,
                'pending_orders_ids' => $pending_orders_ids,
                'limit_regular' => $limitRegular,
                'limit_express' => $limitExpress,
                'limit_fulfillment' => $limitFulfillment,
            ];
            if ($total_pending_orders <= $limit) {
                return $this->successResponse($data);
            }
            return $this->errorResponse(code: 500, customData: $data);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function tryToClearHtmlCache(Request $request): JsonResponse
    {
        try {
            $domain = $request->get('domain', 'senstores.com');
            $htmlCacheParams[] = [
                'type' => 'domain',
                'value' => $domain
            ];
            $result = clearHtmlCache($htmlCacheParams, false);
            $data = [
                'version' => '2024-10-17',
                'domain' => $domain,
                'result' => $result,
            ];
            if (!empty($result['success'])) {
                return $this->successResponse($data);
            }
            return $this->errorResponse(code: 500, customData: $data);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function checkStoreLoadHtml(Request $request): JsonResponse
    {
        try {
            $domain = $request->get('domain', 'senstores.com');
            if (Str::isUrl($domain)) {
                $domain = parse_url($domain, PHP_URL_HOST);
            }
            $timeout = $request->get('timeout', 30);
            $domain = 'https://' . $domain . '?t=' . time();
            $response = Http::withoutVerifying()->timeout($timeout)->get($domain);
            $end_time = microtime(true) - LARAVEL_START;
            $data = [
                'version' => '2024-11-11',
                'url' => $domain,
                'timeout_in_seconds' => $timeout,
                'response_in_seconds' => round($end_time, 2),
            ];
            if ($response->successful()) {
                return $this->successResponse($data);
            }
            return $this->errorResponse(code: 500, customData: $data);
        } catch (ConnectionException|\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function checkEventLogs(Request $request): JsonResponse
    {
        try {
            $limit = (int) $request->get('limit', 5); // minutes
            $total = IndexEventLogs::query()->where('timestamp', '>=', now()->subMinutes($limit))->count();
            $data = [
                'version' => '2025-01-15',
                'limit' => $limit,
                'total' => $total,
            ];
            if ($total > 0) {
                return $this->successResponse($data);
            }
            return $this->errorResponse(code: 500, customData: $data);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function checkRecentOrdersByPaymentMethod(Request $request): JsonResponse
    {
        try {
            $time = (int) $request->input('time', 20);
            $paymentMethod = (string) $request->input('method');
            $paymentMethod = strtolower($paymentMethod);
            if (!empty($paymentMethod) && !in_array($paymentMethod, PaymentMethodEnum::getValues(), true)) {
                return $this->errorResponse('Invalid payment method.');
            }

            if ($paymentMethod === PaymentMethodEnum::PAYPAL) {
                $time *= 1.5;
            }

            $data = [
                'version' => '2025-06-12',
                'now' => now()->toDateTimeString(),
                'time' => $time . ' minutes ago',
                'method' => $paymentMethod,
            ];
            if ($time <= 0) {
                return $this->successResponse($data);
            }
            $cutoffTime = now()->subMinutes($time);
            $data['cutoff_time'] = $cutoffTime->toDateTimeString();
            $total = Order::query()
                ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                ->where('payment_status', OrderPaymentStatus::PAID)
                ->where('paid_at', '>=', $cutoffTime)
                ->whereNotNull('paid_at')
                ->when($paymentMethod, function ($query) use ($paymentMethod) {
                    if ($paymentMethod === PaymentMethodEnum::PAYPAL) {
                        return $query->where('payment_method', PaymentMethodEnum::PAYPAL);
                    }
                    return $query->where('payment_method', 'like', $paymentMethod . '%');
                })
                ->count();
            $data['total_orders'] = $total;
            if ($total > 0) {
                return $this->successResponse($data);
            }
            return $this->errorResponse(code: 500, customData: $data);

        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
}
