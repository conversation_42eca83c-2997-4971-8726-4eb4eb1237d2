<?php

namespace App\Http\Controllers;

use App\Models\WebhookNotification;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\Rule;

class WebhookNotificationController extends Controller
{
    use ApiResponse;

    public function get(): JsonResponse
    {
        $data = WebhookNotification::query()
            ->where([
                'seller_id' => currentUser()->getUserId(),
                'enable' => 1
            ])
            ->get();

        return $this->successResponse($data);
    }

    public function store(Request $request): JsonResponse
    {
        $slack = $request->post('slack');
        $discord = $request->post('discord');
        $msTeams = $request->post('msteams');
        $custom = $request->post('custom');

        $data = [];
        $deleteTypes = [];
        $sellerId = currentUser()->getUserId();

        if ($slack) {
            $data[] = [
                'seller_id' => $sellerId,
                'url' => $slack,
                'type' => 'slack'
            ];
        } else {
            $deleteTypes[] = 'slack';
        }

        if ($discord) {
            $data[] = [
                'seller_id' => $sellerId,
                'url' => $discord,
                'type' => 'discord'
            ];
        } else {
            $deleteTypes[] = 'discord';
        }

        if ($msTeams) {
            $data[] = [
                'seller_id' => $sellerId,
                'url' => $msTeams,
                'type' => 'ms_teams'
            ];
        } else {
            $deleteTypes[] = 'ms_teams';
        }

        if ($custom) {
            $data[] = [
                'seller_id' => $sellerId,
                'url' => $custom,
                'type' => 'custom'
            ];
        } else {
            $deleteTypes[] = 'custom';
        }

        $updated = false;
        $deleted = false;

        if (count($data) > 0) {
            try {
                $updated = WebhookNotification::query()
                    ->upsert(
                        $data,
                        ['seller_id', 'type'], // columns for "where" condition
                        ['url'] // column to update
                    );
            } catch (\Throwable $e) {
                return $this->errorResponse($e->getMessage());
            }
        }

        if (count($deleteTypes) > 0) {
            $deleted = WebhookNotification::query()
                ->where('seller_id', $sellerId)
                ->whereIn('type', $deleteTypes)
                ->delete();
        }

        if ($updated || $deleted) {
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function test(Request $request): JsonResponse
    {
        $allowedTypes = ['slack', 'discord', 'ms_teams', 'custom'];

        $request->validate([
            'url' => [
                'required',
                'url'
            ],
            'type' => [
                'required',
                'string',
                Rule::in($allowedTypes)
            ]
        ]);

        $url = $request->post('url');
        $type = $request->post('type');

        $key = $type === 'discord' ? 'content' : 'text';
        $res = Http::asJson()->post($url, [$key => 'This is a test message from SenPrints!']);
        return $res->successful() ? $this->successResponse() : $this->errorResponse();
    }
}
