<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CurrencyEnum;
use App\Enums\OrderFraudStatus;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\PaymentMethodEnum;
use App\Enums\Stripe\RiskLevelEnum as StripeRiskLevelEnum;
use App\Enums\StripePaymentIntent;
use App\Enums\TestEnum;
use App\Http\Controllers\Controller;
use App\Http\Controllers\SystemConfigController;
use App\Http\Requests\Stripe\GetIntentOrderRequest;
use App\Jobs\GeneralRegionOrderJob;
use App\Jobs\UpdatePaymentIntentForOrderJob;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Models\StripeCustomer;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\OrderService;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use App\Traits\Encrypter;
use App\Traits\OrderDescription;
use App\Traits\StripePaymentDescriptors;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Services\RegionOrderService;
use RuntimeException;
use Stripe\ApplePayDomain;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\SignatureVerificationException;
use Stripe\PaymentIntent;
use Stripe\Refund;
use Stripe\Stripe;
use Stripe\Webhook;
use Throwable;

class StripeController extends Controller
{
    use ApiResponse, OrderDescription, StripePaymentDescriptors, Encrypter;

    private ?string $endpointSecret;
    private ?string $publishableKey;
    private PaymentGateway $gateway;

    public string $gatewayName = PaymentMethodEnum::STRIPE;

    private const PAYMENT_METHODS = [
        'afterpay_clearpay' => [
            'GBP',
        ],
        'alipay' => [
            'CNY',
            'GBP',
        ],
        'au_becs_debit' => [
            'AUD',
        ],
        'boleto' => [
            'BRL',
        ],
        'eps' => [
            'EUR',
        ],
        'fpx' => [
            'MYR',
        ],
        'giropay' => [
            'EUR',
        ],
        'grabpay' => [
            'MYR',
            'SGD',
        ],
        'oxxo' => [
            'MXN',
        ],
        'p24' => [
            'EUR',
            'PLN',
        ],
        'sepa_debit' => [
            'EUR',
        ],
        'wechat_pay' => [
            'CNY',
            'GBP',
        ],
    ];
    private const ADDITION_PAYMENT_METHODS = [
        'klarna' => [
            'EUR',
            'USD',
        ],
        'ideal' => [
            'EUR',
        ],
        'bancontact' => [
            'EUR',
        ],
        'sofort' => [
            'EUR',
        ],
    ];

    private function init($gatewayId, $string = null): void
    {
        $gatewayId ??= optional(request())->get('gateway_id');

        // load config
        $query = PaymentGateway::query()
            ->where('gateway', $this->gatewayName);

        if (!empty($gatewayId)) {
            $query->where('id', $gatewayId);
        }
        $gateway = $query->first();

        if (empty($gateway)) {
            $message = 'Cannot find Stripe Gateway ' . $gatewayId;
            $message .= ' - Info ' . $string;
            logToDiscord($message, 'error_checkout');
            throw new RuntimeException($message);
        }

        $config = self::safeLoadConfig($gateway);
        $configObj = json_decode($config);

        $this->endpointSecret = $configObj->endpoint_secret;
        $this->publishableKey = $configObj->publishable_key;
        $this->gateway = $gateway;
        Stripe::setApiKey($configObj->secret_key);
    }

    public function getIntentOrder(GetIntentOrderRequest $request, $paymentMethods = [], string $currencyCode = null): JsonResponse
    {
        // set for Apple Pay
        $domain = StoreService::currentDomain();
        $token = $request->get('order_token');
        $orderRegion = RegionOrderService::getAppRegion($token);
        $gatewayId = (int) $request->get('gateway_id');
        $requestPaymentMethods = $paymentMethods ?? [];

        try {
            $this->init($gatewayId, $token);
        } catch (Throwable $e) {
            logToDiscord(
                "Init Stripe failed."
                . "\nGateway Id: " . $gatewayId
                . "\nException: " . $e->getMessage(),
                'error_checkout'
            );
            return $this->errorResponse();
        }

        $instance = RegionOrderService::regionOrderModelInstance($token, $orderRegion);
        $orderQuery = $instance['order'];
        $orderProductQuery = $instance['order_products'];
        $order = $orderQuery->select([
            'id',
            'order_number',
            'transaction_id',
            'total_amount',
            'total_discount',
            'total_shipping_amount',
            'total_product_amount',
            'total_tax_amount',
            'tip_amount',
            'currency_code',
            'currency_rate',
            'access_token',
            'customer_id',
            'customer_name',
            'customer_phone',
            'customer_email',
            'address',
            'address_2',
            'city',
            'country',
            'postcode',
            'state',
            'insurance_fee',
            'seller_id',
            'store_id',
            'payment_gateway_id',
            'type',
        ])->firstWhere('access_token', $token);

        if (is_null($order)) {
            return $this->errorResponse();
        }
        /** @var Order|RegionOrders $order */
        $orderProduct = $orderProductQuery->select([
            'order_id',
            'product_name',
            'options',
            'quantity',
            'price'
        ])->where('order_id', $order->id)->get();

        $order->setRelation('order_products', $orderProduct);

        if (is_array($paymentMethods)) {
            if (empty($paymentMethods)) {
                // default have card (google + apple pay)
                $paymentMethods = [
                    'card',
                ];
                foreach (self::PAYMENT_METHODS as $paymentMethod => $currencies) {
                    if (in_array($currencyCode, $currencies, true)) {
                        $paymentMethods[] = $paymentMethod;
                    }
                }
            }
            $arrExcept = [];
            if (!app()->isProduction()) {
                $arrExcept[] = 'alipay';
                $arrExcept[] = 'wechat_pay';
            }
            $idsExceptKlarna = SystemConfig::getConfig('ids_except_klarna', []);
            if (!empty($idsExceptKlarna)) {
                $arr = Str::of($idsExceptKlarna)->explode(',')->filter()->map(fn ($item) => trim($item))->map(fn ($item) => (int)$item)->toArray();
                if (in_array($gatewayId, $arr, true)) {
                    $arrExcept[] = 'klarna';
                }
            }
            foreach ($paymentMethods as $key => $paymentMethod) {
                if (in_array($paymentMethod, $arrExcept, true)) {
                    unset($paymentMethods[$key]);
                }
            }
            $paymentMethods = array_values($paymentMethods);
        }
        // check if acceptedCurrency
        $currencyCode ??= $order->currency_code;
        $currencyCode = OrderService::checkAcceptableCurrency(
            $currencyCode,
            PaymentMethodEnum::STRIPE
        );
        // TODO: Remove after the payment method klarna is enabled again
        $isCustomOrder = $this->gateway->seller_id && $this->gateway->seller_id !== User::SENPRINTS_SELLER_ID;
        if (!$isCustomOrder && is_array($paymentMethods) && in_array('klarna', $paymentMethods, true)) {
            if ($currencyCode === CurrencyEnum::USD) {
                $gatewayId = (int) SystemConfig::getConfig('payment_default_klarna_id');
            } else {
                $gatewayId = (int) SystemConfig::getConfig('payment_europe_stripe_id');
            }
            if ($gatewayId > 0) {
                try {
                    $this->init($gatewayId, $token);
                } catch (Throwable $e) {
                    logToDiscord(
                        "Init Stripe failed."
                        . "\nGateway Id: " . $gatewayId
                        . "\nException: " . $e->getMessage(),
                        'error_checkout'
                    );
                    return $this->errorResponse();
                }
            }
        }

        if (!empty($domain)) {
            try {
                ApplePayDomain::create([
                    'domain_name' => $domain,
                ]);
            } catch (Throwable $e) {
                logToDiscord(
                    'Create Apple domain failed.'
                    . "\nOrder Id: " . $order->id
                    . "\nDomain: " . $domain
                    . "\nException: " . $e->getMessage(),
                    'error_checkout'
                );
            }
        }

        try {
            // must be Zero-decimal currency
            $amount = $order->total_amount * 100;
            if (empty($currencyCode)) {
                $currencyRate = $order->currency_rate;
            } else {
                $currencyRate = SystemConfigController::findOrDefaultCurrency($currencyCode)->rate;
            }
            $amount = roundAmountPayment(
                OrderService::getAmountByCurrency($amount, $currencyCode, $currencyRate),
                0
            );
            if ($amount < 1) {
                return $this->errorResponse('Amount must be greater than 1.');
            }
            if ($amount >= 999999) {
                return $this->errorResponse('Amount must be less than 999999.');
            }

            $description = self::getOrderDescription($order);

            $data = [
                'description' => Str::limit($description, 990),
                'amount' => $amount,
                'currency' => $currencyCode,
                'metadata' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'access_token' => $order->access_token,
                    'payment_gateway_id' => $gatewayId,
                    'region' => $orderRegion,
                    'current_region' => config('app.region')
                ],
            ];

            // https://stripe.com/docs/payments/account/statement-descriptors#dynamic
            try {
                $statementDescriptorPrefix = $this->gateway->statement_descriptor_prefix;

                // if we don't have prefix, use default string to generate suffix
                if (empty($statementDescriptorPrefix)) {
                    $statementDescriptorPrefix = 'AAAAAAAAAA'; // max length of prefix is 10
                }

                $statementDescriptorSuffix = self::getStatementDescriptorSuffix(
                    $statementDescriptorPrefix,
                    $order->order_number
                );

                if (!empty($statementDescriptorSuffix)) {
                    $data['statement_descriptor_suffix'] = $statementDescriptorSuffix;
                }
            } catch (Throwable $e) {
                logToDiscord(
                    'Create statement descriptor failed.'
                    . "\nOrder Id: " . $order->id
                    . "\nException: " . $e->getMessage(),
                    'error_checkout'
                );
            }
            if (!empty($paymentMethods)) {
                $data['payment_method_types'] = $paymentMethods;
            } else {
                $data['automatic_payment_methods'] = [
                    'enabled' => true
                ];
            }
            if (!is_null($order->customer_name) && !is_null($order->address)) {
                $data['shipping'] = OrderService::getShipping($order);
            }
            $paymentIntent = PaymentIntent::create($data);
            if ((int)$order->payment_gateway_id !== (int)$gatewayId && !$isCustomOrder && Str::startsWith($order->payment_method, PaymentMethodEnum::STRIPE)) {
                $order->payment_gateway_id = $gatewayId;
                $order->save();
            }
            graylogInfo('Created payment intend on Stripe - Order ID: ' . $order->id, [
                'category' => 'checkout_stripe',
                'order_id' => $order->id,
                'payment_intend_data' => $data,
                'actual_payment_methods' => $paymentMethods,
                'request_payment_methods' => $requestPaymentMethods,
                'request_all' => $request->all(),
                'order_region' => $orderRegion,
                'instance' => $instance,
                'current_region' => config('app.region')
            ]);
            return $this->successResponse([
                'clientSecret' => $paymentIntent->client_secret,
                'publishableKey' => $this->publishableKey,
                'paymentMethods' => $paymentMethods,
                'currencyCode' => $currencyCode,
                'paymentGatewayId' => $gatewayId,
            ]);
        } catch (ApiErrorException $e) {
            logToDiscord(
                'Create API Order Stripe failed.'
                . "\nException:" . $e->getMessage()
                . "\nCode:" . $e->getCode()
                . "\nRequest Id:" . $e->getRequestId()
                . "\nOrder Id: " . $order->id
                . "\nSeller Id: " . $order->seller_id
                . "\nStore Id: " . $order->store_id
                . "\nPayment Gateway Id: " . $gatewayId,
                'error_checkout'
            );
        } catch (Throwable $e) {
            logToDiscord(
                'Create Order Stripe failed.'
                . "\nException:" . $e->getMessage()
                . "\nCode:" . $e->getCode()
                . "\nOrder Id: " . $order->id
                . "\nSeller Id: " . $order->seller_id
                . "\nStore Id: " . $order->store_id
                . "\nPayment Gateway Id: " . $gatewayId,
                'error_checkout'
            );
            return $this->errorResponse('[1] Load payment failed. Please try other methods.');
        }

        return $this->errorResponse('[2] Load payment failed. Please try other methods.');
    }

    public function getIntentOrderAddition(GetIntentOrderRequest $request): JsonResponse
    {
        $paymentMethods = $request->get('payment_methods');
        $currencyCode = $request->get('currency_code');
        if (!empty($paymentMethods) && is_string($paymentMethods)) {
            $invalid = false;
            $paymentMethods = Str::of($paymentMethods)->explode(',')->filter()->map(fn ($item) => trim($item))->toArray();
            foreach ($paymentMethods as $paymentMethod) {
                if (!in_array($paymentMethod, self::getPaymentMethods(), true)) {
                    $invalid = true;
                    break;
                }
            }
            if ($invalid) {
                return $this->errorResponse('Invalid payment methods.');
            }
        }
        return $this->getIntentOrder($request, $paymentMethods, $currencyCode);
    }

    /**
     * @param $orderToken
     * @param $transactionId
     * @param $orderRegion
     * @return JsonResponse
     */
    public function updatePendingPayment($orderToken, $transactionId = null, $orderRegion = null): JsonResponse
    {
        try {
            $transactionId ??= request('transactionReference');
            if (!isset($orderRegion)) {
                $orderRegion = config('app.master_region');
            }
            if (isset($orderRegion)) {
                $orderQuery = RegionOrderService::regionOrderModelInstance($orderToken, $orderRegion)['order'];
            } else {
                $orderQuery = Order::query();
            }
            $order = $orderQuery->where([
                'access_token' => $orderToken,
                'status' => OrderStatus::PENDING,
                'payment_status' => OrderPaymentStatus::UNPAID
            ])->first();
            if (!$order) {
                return $this->errorResponse();
            }
            $updated = $order->update([
                'status' => OrderStatus::PENDING_PAYMENT,
                'payment_status' => OrderPaymentStatus::PENDING,
                'transaction_id' => $transactionId,
                'paid_at' => DB::raw('CURRENT_TIMESTAMP'),
            ]);
            GeneralRegionOrderJob::dispatch(
                OrderService::class,
                'storePaymentGatewaySimpleLogHandler',
                [PaymentMethodEnum::STRIPE, 'CALLBACK.UPDATE_PAYMENT', $order->id, $order->payment_gateway_id, request()?->all()]
            );
            return $updated ? $this->successResponse() : $this->errorResponse();
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    public function updateTracking($order, $tracking): void
    {
        try {
            // crawl back old order detail
            $this->init($order->payment_gateway_id, $order->id);
            $intent = PaymentIntent::retrieve($order->transaction_id);

            // get old shipping, if don't have, create one
            $shipping = $intent->shipping ?? (object) OrderService::getShipping($order);
            $shippingCarrier = !empty($tracking['shipping_carrier']) ? $tracking['shipping_carrier'] : 'unknown';
            $trackingCode = !empty($tracking['tracking_code']) ? $tracking['tracking_code'] : 'unknown';

            // add more tracking
            if (!empty($shipping->carrier)) {
                $shipping->carrier .= ',' . $shippingCarrier;
            } else {
                $shipping->carrier = $shippingCarrier;
            }
            if (!empty($shipping->tracking_number)) {
                $shipping->tracking_number .= ',' . $trackingCode;
            } else {
                $shipping->tracking_number = $trackingCode;
            }
            $shipping->carrier = Str::of($shipping->carrier)->explode(',')->filter()->map(fn ($item) => trim($item))->unique()->implode(',');
            $shipping->tracking_number = Str::of($shipping->tracking_number)->explode(',')->filter()->map(fn ($item) => trim($item))->unique()->implode(',');
            $shipping = get_class($shipping) === 'Stripe\StripeObject' ? $shipping->toArray() : (array)$shipping;

            PaymentIntent::update(
                $order->transaction_id,
                [
                    'shipping' => $shipping,
                ]
            );
        } catch (Exception $e) {
            $message = 'Update Tracking Stripe failed'
                . "\nException: " . $e->getMessage()
                . "\nOrder Id: " . $order->id;
            if (method_exists($e, 'getRequestId')) {
                $message .= "\nRequest Id: " . $e->getRequestId();
            }

            logToDiscord(
                $message,
                'error_checkout'
            );
            return;
        }
    }

    /**
     * @param Request $request
     * @param $gateId
     * @return JsonResponse
     */
    public function handleWebhook(Request $request, $gateId = null): JsonResponse
    {
        $event = $request->all();
        $eventType = Arr::get($event, 'type');
        $object = Arr::get($event, 'data.object');
        $data = Arr::get($object, 'metadata');
        $orderId = Arr::get($data, 'order_id');
        $orderNumber = Arr::get($data, 'order_number');
        $orderAccessToken = Arr::get($data, 'access_token');
        $paymentGatewayId = Arr::get($data, 'payment_gateway_id');
        $changesObject = Arr::get($object, 'charges.data');
        $paymentObject = Arr::get($object, 'object');
        $orderRegion = Arr::get($data, 'region');
        $ignoredEvents = [
            StripePaymentIntent::CREATED,
        ];

        if (in_array($eventType, $ignoredEvents, true)) {
            return $this->successResponse();
        }
        if ($orderAccessToken === TestEnum::ACCESS_TOKEN) {
            return $this->successResponse();
        }
        GeneralRegionOrderJob::dispatch(
            OrderService::class,
            'storePaymentGatewaySimpleLogHandler',
            [PaymentMethodEnum::STRIPE, $eventType, $orderId, $paymentGatewayId ?? $gateId, $event]
        );
        $context = [
            'category' => 'stripe_webhook',
            'order_id' => $orderId,
            'payment_gateway_id' => $paymentGatewayId,
            'payment_obj' => $paymentObject,
            'event_type' => $eventType,
            'gateway_id' => $gateId,
            'order_number' => $orderNumber
        ];

        graylogInfo(
            json_encode($event),
            $context
        );
        if (empty($orderAccessToken)) {
            return $this->errorResponse();
        }
        $regionOrderServiceInstance = [];
        if (isset($orderRegion)) {
            $regionOrderServiceInstance = RegionOrderService::regionOrderModelInstance($orderAccessToken, $orderRegion);
            $orderQuery = $regionOrderServiceInstance['order'];
        } else {
            $orderQuery = Order::query();
        }

        if (!isset($orderQuery)) {
            return $this->errorResponse();
        }

        $order = null;

        try {
            $order = $orderQuery
                ->where(function ($q) use ($orderId, $orderNumber) {
                    $q->where('id', $orderId)->orWhere('order_number', $orderNumber);
                })
                ->where('access_token', $orderAccessToken)
                ->first();
            if (isset($order)) {
                $stripeCustomer = StripeCustomer::query()
                    ->select('stripe_id')
                    ->where('customer_id', $order->customer_id)
                    ->when($paymentGatewayId, function ($query) use ($paymentGatewayId) {
                        $query->where('gateway_id', $paymentGatewayId);
                    }, function ($query) use ($order) {
                        $query->where('gateway_id', $order->payment_gateway_id);
                    })
                    ->first();
                $order->setRelation('stripe_id', $stripeCustomer?->stripe_id);
            }

        } catch (\Exception $e) {
            graylogError('Error in get order id and access token ', [
                'category' => 'stripe_webhook_error',
                'error' => $e,
            ]);
        }

        try {
            if ($paymentObject === "charge") {
                $authorizedStatus = Arr::get($object, "outcome.type");
                $paymentMethodCardDetail = Arr::get($object, "payment_method_details.card");
                if ($authorizedStatus === "authorized" && $order && $paymentMethodCardDetail) {
                    $last4CardNumber = $paymentMethodCardDetail["last4"];
                    $orderQuery
                        ->where('access_token', $order->access_token)
                        ->update([
                            'last4_card_number' => $last4CardNumber,
                        ]);
                }
                return $this->successResponse();
            }
        } catch (\Exception $e) {
            graylogError('Error in get last4 card digits: ', [
                'category' => 'stripe_webhook',
                'error' => $e,
            ]);
        }

        if (is_null($order)) {
            $message = 'Stripe webhook';
            $message .= "\nOrder Id: " . $orderId;
            $message .= "\nDetail" . json_encode($event);
            logToDiscord($message, 'error_checkout');
            $orderInstance = data_get($regionOrderServiceInstance, 'order');
            graylogError('Error in stripe webhook not found order ', [
                'category' => 'stripe_webhook_error',
                'order_id' => $orderId,
                'order_number' => $orderNumber,
                'access_token' => $orderAccessToken,
                'model_instance' => $regionOrderServiceInstance,
                'order_model' => $orderInstance ? $orderInstance->getModel() : null,
                'detail' => json_encode($event),
                'gate_id' => $gateId,
                'region' => $orderRegion,
                'sg_order' => Order::query()->where('access_token', $orderAccessToken)->first(),
                'us_order' => RegionOrders::onRegion('us')->where('access_token', $orderAccessToken)->first(),
                'eu_order' => RegionOrders::onRegion('eu')->where('access_token', $orderAccessToken)->first(),
            ]);
            return $this->errorResponse($message, 400);
        }

        if (empty($paymentGatewayId)) {
            $paymentGatewayId = $order->payment_gateway_id;
        }

        if (!empty($changesObject)) {
            // Default charge data
            $changesObject = $changesObject[0];
            $orderRiskLevel = Arr::get($changesObject, 'outcome.risk_level');
            $orderRiskScore = Arr::get($changesObject, 'outcome.risk_score', 0);
            $orderSellerMessage = Arr::get($changesObject, 'outcome.seller_message');
            $orderOutcomeType = Arr::get($changesObject, 'outcome.type');
            $orderRule = Arr::get($changesObject, 'outcome.rule_decision.rule');
            $liabilityShift = Arr::get($changesObject, 'liability_shift');
            // Create order flag log with risk level in [highest, elevated]
            if (in_array($orderRiskLevel, [StripeRiskLevelEnum::HIGHEST, StripeRiskLevelEnum::ELEVATED], true)) {
                $orderRiskLevel = ucfirst($orderRiskLevel);
                $flagReasons = [
                    "Stripe risk level: {$orderRiskLevel}.",
                    "Stripe risk score: {$orderRiskScore}."
                ];
                if (!empty($orderSellerMessage)) {
                    $flagReasons[] = "Stripe risk message: {$orderSellerMessage}.";
                }
                if (!empty($orderOutcomeType)) {
                    $orderOutcomeType = ucfirst($orderOutcomeType);
                    $flagReasons[] = "Stripe risk type: {$orderOutcomeType}.";
                }
                if ($liabilityShift && data_get($liabilityShift, 'successful', false) && data_get($liabilityShift, 'code') === 'three_d_secure_successful') {
                    $flagReasons[] = "Stripe 3DS: Yes.";
                }
                if ($orderRule && data_get($orderRule, 'enabled', false)) {
                    $action = data_get($orderRule, 'action');
                    $action = $action === 'allow' ? 'Allowed' : ucfirst($action);
                    $flagReasons[] = $action . " by rule " . data_get($orderRule, 'rule_text') . ".";
                }
                $orderQuery
                    ->where('id', $order->id)
                    ->update([
                        'fraud_status' => OrderFraudStatus::FLAGGED,
                        'flag_log' => implode(', ', $flagReasons)
                    ]);
                sendLogOrderFraudToDiscord($order, OrderFraudStatus::FLAGGED, null, $flagReasons);
            }
        }


        try {
            $this->init($paymentGatewayId, $order->id);

            $payload = @file_get_contents('php://input');
            $sigHeader = $_SERVER['HTTP_STRIPE_SIGNATURE'];

            Webhook::constructEvent(
                $payload,
                $sigHeader,
                $this->endpointSecret
            );

        } catch (Throwable $e) {
            if ($e instanceof SignatureVerificationException) {
                // ignore request because of multiple gateway configs
                if ($order->payment_status === OrderPaymentStatus::PAID) {
                    return $this->successResponse('Order already paid.');
                }

                return $this->errorResponse('Invalid signature.');
            }

            logToDiscord(
                'handleWebhookStripe failed.'
                . "\nDetail: "
                . $e->getMessage()
                . "\nId: "
                . $orderId
                . "\nGateway Id: "
                . $paymentGatewayId,
                'error_checkout'
            );
            // Invalid payload
            return $this->errorResponse($e->getMessage(), 400);
        }

        try {
            $typeObject = Arr::get($object, 'object');
            if ($typeObject === 'payment_intent') {
                $paymentObjectId = Arr::get($object, 'id');
            } else {
                $paymentObjectId = Arr::get($object, 'payment_intent');
            }

            switch ($eventType) {
                case StripePaymentIntent::SUCCEEDED:
                    $totalPaid = Arr::get($object, 'amount_received') / 100;

                    // if different currency payment
                    $currencyCode = strtoupper(Arr::get($object, 'currency'));

                    if ($currencyCode === CurrencyEnum::EUR) {
                        $currencyRate = $totalPaid / $order->total_amount;
                        $totalPaid = $order->total_amount; // convert total paid to USD
                    } else {
                        $currencyRate = null;
                        $currencyCode = null;
                    }

                    $order->paymentCompleted($totalPaid, $paymentObjectId, false, $currencyCode, $currencyRate, $paymentGatewayId);
                    UpdatePaymentIntentForOrderJob::dispatch($paymentObjectId, $order, $paymentGatewayId);
                    break;
                case StripePaymentIntent::FAILED:
                    $errors = Arr::get($object, 'last_payment_error', []);
                    $log = implodeWithKeys(
                        $errors,
                        ':',
                        '|',
                        [
                            'code',
                            'decline_code',
                            'message',
                            'type',
                        ]
                    );
                    $order->paymentFailed($log, $paymentObjectId, $paymentGatewayId);
                    break;
                case StripePaymentIntent::CANCELLED:
                    $code = Arr::get($changesObject, 'failure_code');
                    $message = Arr::get($changesObject, 'failure_message');
                    $log = "Code: $code | Message: $message";
                    $order->paymentFailed($log, $paymentObjectId, $paymentGatewayId);
                    break;
                case StripePaymentIntent::PENDING:
                    $this->updatePendingPayment($orderAccessToken, $paymentObjectId, $orderRegion);
                    break;
                case StripePaymentIntent::REQUIRES_ACTION:
                case StripePaymentIntent::PROCESSING:
                    // ignore log
                    break;

                case StripePaymentIntent::DISPUTE_CREATED:
                    // nothing yet
                    break;

                case StripePaymentIntent::RADAR_EARLY_FRAUD_WARNING:
                    // todo: handle this event (https://senprints.atlassian.net/browse/SDV-2183)
                    break;

                default:
                    throw new RuntimeException(
                        $paymentObjectId . ' with order ID = ' . $orderId . ' return event: : ' . $eventType
                    );
            }

            return $this->successResponse();
        } catch (Throwable $e) {
            graylogError('Stripe complete order', [
                'category' => 'stripe_complete_order_error',
                'data' => $e
            ]);
            logToDiscord(
                'Handle webhookRequest failed.'
                . "\nException:" . $e->getMessage()
                . "\nOrder Id: " . $orderId
                . "\nPayment Gateway Id: " . $paymentGatewayId,
                'error_checkout'
            );

            return $this->errorResponse($e->getMessage(), 400);
        }
    }

    /**
     * @param Order $order
     * @param float|null $amount
     * @param int $fullRefund
     * @return array|string[]|Refund
     */
    public function refundOrderToGateway(Order $order, ?float $amount, int $fullRefund = 0)
    {
        if (empty($amount) && empty($fullRefund)) {
            return [
                'status' => 'failed',
                'reason' => 'Amount is required'
            ];
        }

        $dataRefund = [];

        if (empty($fullRefund) && !empty($amount) && (float) $amount > 0) {
            if ((float) $amount > (float) $order->total_amount) {
                return [
                    'status' => 'failed',
                    'message' => 'Refund amount cannot be greater than total amount',
                ];
            }

            // must refund by cent, not $ ( see doc below )
            // https://stripe.com/docs/api/refunds/create?lang=php
            if ($order->currency_code === CurrencyEnum::EUR) {
                $amount *= $order->currency_rate;
            }
            $dataRefund['amount'] = (int) ($amount * 100);
        }

        try {
            graylogInfo('Start refund to gateway transaction id: ' . $order->transaction_id, [
                'category' => 'refund_to_gateway',
                'order_id' => $order->id,
                'transaction_id' => $order->transaction_id,
                'payment_method' => 'stripe'
            ]);
            $this->init($order->payment_gateway_id, $order->id);
            $intent = PaymentIntent::retrieve($order->transaction_id, [
                'expand' => ['latest_charge'],
            ]);

            // check if we already refunded before
            try {
                if (isset($intent->latest_charge) && !is_null($intent->latest_charge) && !empty($dataRefund['amount'])) {
                    $refunds = Refund::all([
                        'payment_intent' => $order->transaction_id,
                    ]);

                    if ($refunds->count() > 0) {
                        collect($refunds)->filter(function ($refund) use ($dataRefund) {
                            return $refund->amount === $dataRefund['amount'];
                        });
                    }
                }
            } catch (Throwable $e) {
                logException($e, __FUNCTION__ . ': Order Id: ' . $order->id);
            }
        } catch (ApiErrorException $e) {
            logException($e, __FUNCTION__ . ': Order Id: ' . $order->id);
            return [
                'status' => 'failed',
                'reason' => $e->getMessage()
            ];
        }
        $dataRefund['payment_intent'] = $intent->id;
        try {
            $response = Refund::create($dataRefund);
            graylogInfo('Refund to gateway transaction id: ' . $order->transaction_id, [
                'category' => 'refund_to_gateway',
                'order_id' => $order->id,
                'transaction_id' => $order->transaction_id,
                'user_id' => optional(currentUser())->getUserId(),
                'payment_method' => 'stripe',
                'payload' => json_encode(array_merge(array(
                    'currency_code' => $order->currency_code,
                    'full_refund' => $fullRefund,
                    'payment_gateway_id' => $order->payment_gateway_id,
                    'response' => json_encode($response, JSON_THROW_ON_ERROR),
                ), $dataRefund), JSON_THROW_ON_ERROR)
            ]);
            if ($response->status === 'succeeded' || $response->status === 'pending') {
                return $response;
            }
        } catch (Throwable $e) {
            logException($e);
            return [
                'status' => 'failed',
                'reason' => $e->getMessage()
            ];
        }
        $message = 'Unknown error';
        if ($response) {
            $message .= ', Refund Id: ' . $response->id . ' , Refund status: ' . $response->status . ' , Reason: ' . $response->reason;
        }
        return [
            'status' => 'failed',
            'reason' => $message
        ];
    }

    public static function getPaymentMethods(): array
    {
        return array_merge(['card'], array_keys(array_merge(self::PAYMENT_METHODS, self::ADDITION_PAYMENT_METHODS)));
    }

    /**
     * @param $payment_gateway_id
     * @param $transaction_id
     * @return false|PaymentIntent
     */
    public function verifyPaymentIntent($payment_gateway_id, $transaction_id)
    {
        try {
            $this->init($payment_gateway_id);
            return PaymentIntent::retrieve($transaction_id);
        } catch (Throwable $e) {
            $message = 'Verify Payment Intent Stripe failed.'
                . "\nException: " . $e->getMessage()
                . "\nTransaction ID: " . $transaction_id
                . "\nPayment Gateway Id: " . $payment_gateway_id;
            logToDiscord($message, 'error_checkout');
            return false;
        }
    }
}
