<?php

namespace App\Http\Controllers;

use App\Models\StoreDomain;
use App\Services\CloudFlareCustomHostname;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CloudFlareController extends Controller
{
    use ApiResponse;

    private $cloudFlareService;

    public function __construct(CloudFlareCustomHostname $cloudFlareService)
    {
        $this->cloudFlareService = $cloudFlareService;
    }

    private function isValidDomain(string $domain): bool
    {
        return (
            // Check if domain is valid using PHP's filter
            filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) &&
            // Ensure domain has at least one dot and no spaces
            str_contains($domain, '.') &&
            !str_contains($domain, ' ') &&
            // Check domain length
            strlen($domain) >= 3 && strlen($domain) <= 253
        );
    }

    public function checkCustomHostname(Request $request): ?JsonResponse
    {
        abort_if($request->query('ref') !== 'senprints', 401, 'Unauthorized');

        $domain = $request->query('domain');

        if (!$domain) {
            return $this->errorResponse('Domain is required', 400);
        }

        if (!$this->isValidDomain($domain)) {
            return $this->errorResponse('Invalid domain format', 400);
        }

        $storeDomain = StoreDomain::where('domain', $domain)->first();

        if (!$storeDomain) {
            return $this->errorResponse('Domain not found', 404);
        }

        if (!$storeDomain->cloudflare_custom_hostname_id) {
            return $this->errorResponse('This domain does not have CloudFlare Custom Hostname ID', 404);
        }

        try {
            $result = $this->cloudFlareService->showCustomHostname($storeDomain->cloudflare_custom_hostname_id);
            return $this->successResponse($result);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
}
