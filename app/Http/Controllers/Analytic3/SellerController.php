<?php

/** @noinspection PhpUndefinedMethodInspection */

/** @noinspection PhpMethodParametersCountMismatchInspection */

namespace App\Http\Controllers\Analytic3;

use App\Enums\CacheKeys;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\DateRangeEnum;
use App\Enums\EventLogsTypeEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductType;
use App\Enums\SaleReport\DataTypeEnum;
use App\Enums\SaleReport\TypeEnum;
use App\Enums\UserInfoKeyEnum;
use App\Enums\UserRoleEnum;
use App\Http\Requests\Analytic\Seller\CampaignRequest;
use App\Http\Requests\Analytic\Seller\DashboardRequest;
use App\Http\Requests\Analytic\Seller\GetAdsRequest;
use App\Http\Requests\Analytic\Seller\StoreRequest;
use App\Models\Campaign;
use App\Models\IndexCampaign;
use App\Models\IndexEventLogs;
use App\Models\IndexOrder;
use App\Models\IndexProduct;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\Store;
use App\Models\SystemLocation;
use App\Models\User;
use App\Models\UserInfo;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use App\Traits\ScopeFilterDateRangeTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SellerController
{
    use ApiResponse;
    use ElasticClient;
    use ScopeFilterDateRangeTrait;

    public ?string $dataType = DataTypeEnum::OVERVIEW;
    public ?string $type = null;
    public ?string $id = null;
    protected object $controller;
    protected object $model;
    public array $arrFilter = [];
    public array $arrExclude = [
        UserInfoKeyEnum::EXCLUDE_COUNTRIES => []
    ];
    public array $dateRanges = [
        'column' => 'paid_at',
    ];
    public const LIMIT_VISIT = 300;

    /**
     * @throws \Throwable
     */
    public function dashboard(Request $request, bool $asJSON = true)
    {
        $this->setCommonFilter($request);
        $data = $this->getOverview();

        if ($asJSON) {
            return $this->successResponse($data);
        }

        return $data;
    }

    /**
     * @param $typeAnalytic
     * @param $allReportsWithoutTime
     * @return mixed
     * @throws \Exception|\Throwable
     */
    public function getOverview($typeAnalytic = 'dashboard', $allReportsWithoutTime = true)
    {
        $dateRangeType = $this->dateRanges['type'] ?? null;
        $dateType = '';
        $timeout = 0;
        $user = currentUser();
        switch ($dateRangeType) {
            case DateRangeEnum::YESTERDAY:
                $dateType = 'day';
                $timeout = CacheKeys::CACHE_24H;
                break;
            case DateRangeEnum::LAST_WEEK:
                $dateType = 'week';
                $timeout = CacheKeys::CACHE_1W;
                break;
            case DateRangeEnum::LAST_MONTH:
                $dateType = 'month';
                $timeout = CacheKeys::CACHE_1W;
                break;
            case DateRangeEnum::LAST_3_DAYS:
            case DateRangeEnum::LAST_7_DAYS:
            case DateRangeEnum::LAST_14_DAYS:
            case DateRangeEnum::LAST_30_DAYS:
            case DateRangeEnum::THIS_WEEK:
            case DateRangeEnum::THIS_MONTH:
            case DateRangeEnum::THIS_YEAR:
                $dateType = 'hour';
                $timeout = CacheKeys::CACHE_1H;
                break;
        }
        if (empty($dateType) && empty($timeout)) {
            $overview = $this->getAnalyticOverview($typeAnalytic);
            if ($allReportsWithoutTime) {
                $this->addSellerBalanceAndSenPoint($overview);
            }
            return $overview;
        }
        $storeId = Arr::get($this->arrFilter, 'store_id');

        $tags = [
            'seller_id_' . $user->getUserId(),
        ];

        if ($storeId) {
            $tags[] = CacheKeys::getStoreId($storeId);
        }

        $overview = cache()->tags($tags)
            ->remember(
                CacheKeys::getStats($typeAnalytic . '_v3', $dateType, $dateRangeType, $storeId),
                $timeout,
                function () use ($typeAnalytic) {
                    return $this->getAnalyticOverview($typeAnalytic);
                }
            );
        if ($allReportsWithoutTime) {
            $this->addSellerBalanceAndSenPoint($overview);
        }
        return $overview;
    }

    /**
     * @param $overview
     * @return void
     */
    private function addSellerBalanceAndSenPoint($overview): void
    {
        $user = currentUser();
        $overview->pending_fee = Order::query()
            ->selectRaw('sum(processing_fee + total_fulfill_fee) as pending_fee')
            ->whereRaw("sen_fulfill_status = ? AND fulfill_status != ? ", [OrderSenFulfillStatus::PENDING, OrderFulfillStatus::CANCELLED])
            ->when($user->isSeller(), function ($query) use ($user) {
                return $query->where('seller_id', $user->getUserId());
            })
            ->value('pending_fee');

        $userInfo = User::query()
            ->selectRaw('sum(balance) as balance')
            ->selectRaw('sum(sen_points) as sen_points')
            ->when($user->isSeller(), function ($q) use ($user) {
                return $q->where('id', $user->getUserId());
            })
            ->first();

        if ($userInfo) {
            $overview->seller_balance = $userInfo->balance;
            $overview->seller_sen_points = $userInfo->sen_points;
        }
    }

    /**
     * @param $typeAnalytic
     * @return mixed
     * @throws \Throwable
     */
    protected function getAnalyticOverview($typeAnalytic)
    {
        $user = currentUser();
        $isCampaignDetail = $typeAnalytic === 'campaign';
        $isDashboard = $typeAnalytic === 'dashboard';
        $query = $this->getCommonQuery($isDashboard, $isCampaignDetail);
        if ($isCampaignDetail) {
            $query->join('order_product', 'order.id', '=', 'order_product.order_id');
            $query->calculateOrders()->calculateProductItems()->calculateProductSales()->calculateProductProfits();
        }
        $overview = $query->first();
        if ($overview === null) {
            return (object)[];
        }

        // Check if seller has visit over limit and has no order
        $spamSellerId = [];
        if ($isDashboard && $user->isAdmin()) {
            $sellerHasVisitOverLimit = IndexEventLogs::query()
                ->selectRaw('COUNT(DISTINCT session_id) as total_visits')
                ->addSelect('type')
                ->addSelect('seller_id')
                ->where('type', EventLogsTypeEnum::VISIT)
                ->where('timestamp', '>=', now()->subDay())
                ->groupBy('type', 'seller_id')
                ->having('total_visits', '>', self::LIMIT_VISIT)
                ->get()
                ->pluck('seller_id')
                ->unique()
                ->toArray();

            if (!empty($sellerHasVisitOverLimit)) {
                $sellerHasOrders = IndexOrder::query()
                    ->selectRaw('count(distinct id) as total')
                    ->addSelect('seller_id')
                    ->whereIn('seller_id', $sellerHasVisitOverLimit)
                    ->where('paid_at', '>=', now()->subDay())
                    ->whereNull('deleted_at')
                    ->groupBy('seller_id')
                    ->having('total', '>=', 1)
                    ->get()
                    ->pluck('seller_id')
                    ->unique()
                    ->toArray();
                $spamSellerId = array_values(array_diff($sellerHasVisitOverLimit, $sellerHasOrders));
            }
        }
        $range = data_get($this->dateRanges, 'range', [now(), now()]);
        $eventLogs = IndexEventLogs::query()
            ->select([
                DB::raw('SUM(CASE WHEN total > 0 THEN total ELSE 0 END) AS total_sum'),
                'type',
            ])
            ->when((DateRangeEnum::isMoreThan30Days($this->dateRanges['type']) || (Carbon::create($range[0])->isBefore(now()->startOfMonth()))), function ($query) {
                $query->selectRaw('APPROX_COUNT_DISTINCT(session_id) AS count');
            }, fn ($query) => $query->selectRaw('COUNT(DISTINCT session_id) as count'))
            ->when(!empty($spamSellerId), function ($query) use ($spamSellerId) {
                return $query->whereNotIn('seller_id', $spamSellerId);
            })
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
            ->addExcludeAnalytic($this->arrExclude)
            ->groupBy('type')
            ->get();

        foreach (EventLogsTypeEnum::asArray() as $type) {
            $overview->$type = optional($eventLogs->firstWhere('type', $type))->count + optional($eventLogs->firstWhere('type', $type))->total_sum;
        }

        if ($typeAnalytic === 'dashboard') {
            $user = currentUser();
            $queryOrder = IndexOrder::query()
                ->selectRaw('sum(tip_amount) as tip_amount')
                ->selectRaw('sum(insurance_fee) as insurance_fee')
                ->addFilterAnalytic($this->arrFilter, $this->dateRanges);
            if ($user->isAdmin()) {
                $queryOrder
                    ->selectRaw("SUM(IF(type in ('" . OrderTypeEnum::FULFILLMENT . "', '" . OrderTypeEnum::FBA . "'), 1, 0)) AS fulfillment_order_count")
                    ->selectRaw("SUM(IF(type in ('" . OrderTypeEnum::FULFILLMENT . "', '" . OrderTypeEnum::FBA . "'), total_quantity, 0)) AS fulfillment_items")
                    ->selectRaw("SUM(IF(type in ('" . OrderTypeEnum::FULFILLMENT . "', '" . OrderTypeEnum::FBA . "'), total_amount, 0)) AS fulfillment_order_sales")
                    ->selectRaw("SUM(IF(type = '" . OrderTypeEnum::CUSTOM . "', 1, 0)) AS custom_order_count")
                    ->selectRaw("SUM(IF(type = '" . OrderTypeEnum::CUSTOM . "', total_quantity, 0)) AS custom_order_items")
                    ->selectRaw("SUM(IF(type = '" . OrderTypeEnum::CUSTOM . "', total_amount, 0)) AS custom_order_sales")
                    ->selectRaw("SUM(IF(type in ('" . OrderTypeEnum::CUSTOM . "', '" . OrderTypeEnum::REGULAR . "') , total_amount, 0)) AS total_sales")
                    ->selectRaw("SUM(total_fulfill_base_cost) AS total_fulfill_base_cost")
                    ->selectRaw("SUM(total_fulfill_shipping_cost) AS total_fulfill_shipping_cost")
                    ->selectRaw("SUM(total_fulfill_profit) AS total_fulfill_profit")
                    ->selectRaw("SUM(IFNULL(total_fulfill_profit, 0) + IF(type = '" . OrderTypeEnum::REGULAR . "', IFNULL(total_product_amount, 0), 0)) AS sen_profit");
                $supplierDebt = OrderProduct::query()
                    ->selectRaw('sum(fulfill_base_cost + fulfill_shipping_cost) as debt')
                    ->whereNull('billed_at')
                    ->where('fulfilled_at', '>', now()->subDays(30))
                    ->value('debt');
                $badCr = $this->getBadCr();
                $overview->bad_visits = $badCr['bad_visits'];
                $overview->bad_order = $badCr['bad_order'];
            }

            $order = $queryOrder->first();

            if ($order) {
                foreach ($order->getAttributes() as $key => $val) {
                    $overview->$key = $val;
                }
                $overview->insurance_fee = $user->isAdmin() ? $order->insurance_fee * 0.8 : $order->insurance_fee * 0.2;
                $overview->supplier_debt = $supplierDebt ?? 0;
            }

            $this->addAttributeToOverview($overview, $user);
        }

        return model_map($overview, 'floatval');
    }

    /*
     * Filter Store Has Order But CR < 0.1%
     * @return array
     */
    protected function getBadCr(): array
    {
        $dateType = Arr::get($this->dateRanges, 'type', DateRangeEnum::TODAY);
        $startDate = Arr::get($this->dateRanges, 'range.0');
        $endDate = Arr::get($this->dateRanges, 'range.1');
        $visits = IndexEventLogs::query()
            ->select([
                DB::raw('SUM(CASE WHEN total > 0 THEN total ELSE 0 END) AS total_sum'),
                'store_id',
            ])
            ->when((DateRangeEnum::isMoreThan30Days($dateType) || (Carbon::create($startDate)->isBefore(now()->startOfMonth()))), function ($query) {
                $query->selectRaw('APPROX_COUNT_DISTINCT(session_id) AS count');
            }, fn ($query) => $query->selectRaw('COUNT(DISTINCT session_id) as count'))
            ->where('type', EventLogsTypeEnum::VISIT)
            ->filterDateRange($dateType, $startDate, $endDate)
            ->groupBy('store_id')
            ->get();
        $orders = Order::query()
            ->selectRaw('count(*) as total_orders')
            ->addSelect('store_id')
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->whereNotIn('status', [OrderStatus::DRAFT, OrderStatus::DELETED, OrderStatus::PENDING])
            ->whereNotNull('paid_at')
            ->filterDateRange($dateType, $startDate, $endDate, 'paid_at')
            ->groupBy('store_id')
            ->get();
        $badVisits = 0;
        $badOrder = 0;
        foreach ($visits as $visit) {
            $order = $orders->firstWhere('store_id', $visit->store_id);
            if (!$order) { // Ignore store has no order
                continue;
            }
            $badCr = $order->total_orders * 1000 < ($visit->total_visits + $visit->total_sum);
            if ($badCr) {
                $badVisits += $visit->total_visits + $visit->total_sum;
                $badOrder += $order->total_orders;
            }
        }

        return [
            'bad_visits' => $badVisits,
            'bad_order' => $badOrder,
        ];
    }
    /**
     * @param $overview
     * @param $user
     * @return void
     * @throws \Throwable
     */
    protected function addAttributeToOverview(&$overview, $user)
    {
        $overview->pending_payment = Order::query()
            ->selectRaw('count(id) as total')
            ->whereRaw("status = ? AND payment_status = ?", [OrderStatus::PENDING_PAYMENT, OrderPaymentStatus::PENDING])
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
            ->value('total');
        $seller = currentUser()->getInfoAccess();
        $elasticSearchIndex = $seller?->getElasticSearchIndex() ?? get_env('ELATICSEARCH_INDEX', 'products');
        $filters = $this->arrFilter + ['product_type' => ProductType::CAMPAIGN];
        if (currentUser()->isAdmin()) {
            $overview->campaigns = $this->elasticCount($filters, $this->dateRanges);
            $overview->deleted_campaigns = IndexCampaign::query()
                ->selectRaw('COUNT(id) as total')
                ->withTrashed()
                ->filterDateRange($this->dateRanges['type'], null, null, 'deleted_at')
                ->value('total');
            $overview->deleted_sellers = User::query()
                ->selectRaw('COUNT(id) as total')
                ->where('role', UserRoleEnum::SELLER)
                ->withTrashed()
                ->filterDateRange($this->dateRanges['type'], null, null, 'deleted_at')
                ->value('total');
        }
        else {
            $overview->campaigns = $this->elasticCount($filters, $this->dateRanges, index: $elasticSearchIndex);
        }
        $overview->last_hour_campaigns = 0;
        if (!empty($this->dateRanges['type']) && $this->dateRanges['type'] === DateRangeEnum::TODAY) {
            $overview->last_hour_campaigns = Product::query()->onSellerConnection($seller)->selectRaw('COUNT(id) as total')->whereIn('product_type', [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
                ProductType::CAMPAIGN_TEMPLATE,
            ])->where('created_at', '>=', now()->subHour())->value('total');
        }

        return $overview;
    }

    /**
     * @param $sellerId
     * @param $arrUnsetFilter
     * @return IndexOrder|Builder
     */
    public function getAnalyticOrderQuery($sellerId = null, $arrUnsetFilter = [])
    {
        $query = IndexOrder::query()
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges, $sellerId, $arrUnsetFilter)
            ->addExcludeAnalytic($this->arrExclude);
        if (currentUser()->isAdmin()) {
            $query->countActiveSellers();
        }
        $query->whereNull('order.deleted_at');
        return $query;
    }

    /**
     * @param $sellerId
     * @return Order|Builder
     */
    public function getRealtimeAnalyticOrderQuery($sellerId = null)
    {
        return Order::query()->addFilterAnalytic($this->arrFilter, $this->dateRanges, $sellerId)->addExcludeAnalytic($this->arrExclude);
    }

    /**
     * @param bool $isDashboard
     * @param bool $isCampaignDetail
     * @return IndexOrder|Builder
     */
    public function getCommonQuery($isDashboard = false, $isCampaignDetail = false)
    {
        return $this->getAnalyticOrderQuery()->getAnalyticOverview($isDashboard, $isCampaignDetail);
    }

    /**
     * @param string $field
     * @param string|null $orderBy
     * @param int $limit
     * @return array
     */
    public function getOverViewAndViews($field, $orderBy = '', $limit = 20): array
    {
        if (request('limit')) {
            $limit = min(request('limit'), 500);
        }
        if (in_array($orderBy, EventLogsTypeEnum::getArrayForAnalytic())) {
            $limit = null;
        }
        $isCampaignDetail = $field === 'campaign_id' || !empty($this->arrFilter['campaign_id']) || $field === 'template_id';
        $query = $this->getCommonQuery(false, $isCampaignDetail);
        if (!$orderBy) {
            $query->orderByDesc(CampaignSortByAllowEnum::TOTAL_ORDERS);
        } else if (!in_array($orderBy, [...EventLogsTypeEnum::getArrayForAnalytic(), CampaignSortByAllowEnum::CONVERSION_RATE])) {
            $query->orderByDesc($orderBy);
        }
        $eventLogs = collect([]);
        if ($field === 'template_id') {
            $query->join('order_product', 'order.id', '=', 'order_product.order_id');
            $query->calculateOrders();
            $query->selectRaw('order_product.template_id');
            $query->selectRaw('SUM(order_product.quantity) AS items');
            $query->selectRaw('SUM(order_product.total_amount) AS sales');
            $query->selectRaw('SUM(order_product.seller_profit) AS profits');
            $query->whereNull('order_product.deleted_at');
            $query->whereRaw('order_product.seller_profit > 0');
            $query->whereIn('type', [
                OrderTypeEnum::CUSTOM,
                OrderTypeEnum::REGULAR,
            ]);
            $query->groupBy('order_product.' . $field);
            $data = $query->limit($limit)->get();
            if ($data->isNotEmpty()) {
                $template_id = $data->pluck('template_id')->toArray();
                $templates = IndexProduct::query()->whereIn('id', $template_id)->select(['id', 'name'])->get();
                $data->map(function ($item) use ($templates) {
                    foreach ($templates as $template) {
                        if ((int)$template->id === (int)$item->template_id) {
                            $item->template_product = $template->toArray();
                        }
                    }
                    return $item;
                });
            }
        } else {
            if ($isCampaignDetail) {
                $query->join('order_product', 'order.id', '=', 'order_product.order_id');
                $query->whereNull('order_product.deleted_at');
                $query->calculateOrders()->calculateProductItems()->calculateProductSales()->calculateProductProfits();
            }

            switch ($field) {
                case 'store_id':
                    $query->selectRaw($field);
                    $query->groupBy($field);
                    $query->addSelect('seller_id');
                    $query->groupBy('seller_id');
                    break;
                default:
                    $query->selectRaw($field);
                    $query->groupBy($field);
                    break;
            }

            $data = $query->limit($limit)->get();
            $range = data_get($this->dateRanges, 'range', [null, null]);
            $eventLogs = IndexEventLogs::query()
                ->select([
                    DB::raw('SUM(CASE WHEN total > 0 THEN total ELSE 0 END) AS total_sum'),
                    'type',
                ])
                ->when((DateRangeEnum::isMoreThan30Days($this->dateRanges['type']) || (Carbon::create($range[0])->isBefore(now()->startOfMonth()))), function ($query) {
                    $query->selectRaw('APPROX_COUNT_DISTINCT(session_id) AS count');
                }, fn ($query) => $query->selectRaw('COUNT(DISTINCT session_id) as count'))
                ->addSelect($field)
                ->addSelect('seller_id')
                ->addSelect('id')
                ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                ->addExcludeAnalytic($this->arrExclude)
                ->groupBy('type', $field)
                ->orderByDesc('count')
                ->get();
        }
        if ($data->isEmpty() && $eventLogs->isEmpty()) {
            return [];
        }
        $sellers = [];
        $stores = [];
        $countries = [];

        if ($field === 'store_id') {
            $seller_ids = $data->pluck('seller_id')->unique()->toArray();
            $store_ids = $data->pluck('store_id')->unique()->toArray();
            $sellers = User::query()->selectRaw('id, email')->whereIn('id', $seller_ids)->get()->groupBy('id')->toArray();
            $stores = Store::query()->selectRaw('id, domain, sub_domain')->whereIn('id', $store_ids)->get()->groupBy('id')->toArray();
        }
        if ($field === 'country') {
            $country_code = $data->pluck('country')->unique()->toArray();
            $countries = SystemLocation::query()->selectRaw('code, name')->whereIn('code', $country_code)->get()->groupBy('code')->toArray();
        }
        $data = $data->toArray();
        if ($field !== 'template_id') {
            $order_fields = array(
                "active_sellers",
                "total_product_amount",
                "orders",
                "items",
                "sales",
                "profits",
            );

            if (empty($data)) {
                $data = [];
            } else {
                foreach ($data as &$each) {
                    if (is_null($each[$field])) {
                        $each[$field] = 'N/A';
                    }
                    $eventLogArr = [];
                    foreach (EventLogsTypeEnum::asArray() as $type) {
                        foreach ($eventLogs as $key => $eventLog) {
                            if ($eventLog->type == $type && $eventLog->$field == $each[$field]) {
                                $eventLogArr[$type][$each[$field]] = $eventLog;
                                unset($eventLogs[$key]);
                            }
                        }
                    }
                    if (!empty($eventLogArr)) {
                        foreach (EventLogsTypeEnum::asArray() as $type) {
                            try {
                                $each[$type] = $eventLogArr[$type][$each[$field]]->count + $eventLogArr[$type][$each[$field]]->total_sum;
                            } catch (\Exception $e) {
                                $each[$type] = 0;
                            }
                        }
                    }

                    if (Str::contains($field, 'ad_')) {
                        $each['name'] = $each[$field];
                        unset($each[$field]);
                    }
                    if ($field === 'store_id') {
                        $each['seller'] = !empty($sellers[$each['seller_id']]) ? $sellers[$each['seller_id']][0] : null;
                        $each['store'] = !empty($stores[$each['store_id']]) ? $stores[$each['store_id']][0] : null;
                    }
                    if ($field === 'country') {
                        $each['system_location'] = !empty($countries[$each['country']]) ? $countries[$each['country']][0] : null;
                    }
                    $each['conversion_rate'] = (!empty($each['orders']) && !empty($each['visit'])) ? $each['orders'] / $each['visit'] : 0;
                    if (isset($each['system_location'])) {
                        $each['name'] = $each['system_location']['name'];
                        unset($each['system_location']);
                    }
                }
                unset($each);
            }
            if ($eventLogs->count() > 0) {
                $eventLogs = $eventLogs->values();
                $seller_ids = $eventLogs->pluck('seller_id')->unique()->toArray();
                $store_ids = $eventLogs->pluck('store_id')->unique()->toArray();
                if ($field === 'store_id') {
                    $sellers = User::query()->selectRaw('id, email')->whereIn('id', $seller_ids)->get()->groupBy('id')->toArray();
                    $stores = Store::query()->selectRaw('id, domain, sub_domain')->whereIn('id', $store_ids)->get()->groupBy('id')->toArray();
                }
                if ($field === 'country') {
                    $country_code = $eventLogs->pluck('country')->unique()->toArray();
                    $countries = SystemLocation::query()->selectRaw('code, name')->whereIn('code', $country_code)->get()->groupBy('code')->toArray();
                }

                $eventLogFields = [];
                $eventLogData = [];
                foreach ($eventLogs as $eventLog) {
                    $eventLogFields[] = $eventLog->$field;
                    $eventLogData[$eventLog->{$field}][$eventLog->type] = $eventLog;
                }

                foreach (array_unique($eventLogFields) as $itemField) {
                    $item = array();
                    foreach (EventLogsTypeEnum::asArray() as $type) {
                        $item[$type] = 0;
                    }
                    foreach ($order_fields as $order_field) {
                        $item[$order_field] = 0;
                    }

                    foreach (EventLogsTypeEnum::asArray() as $type) {
                        $item[$type] = isset($eventLogData[$itemField][$type]) ? ($eventLogData[$itemField][$type]->count + $eventLogData[$itemField][$type]->total_sum) : 0;
                        if ($field === 'store_id' && empty($item['seller'])) {
                            $item['seller'] = isset($eventLogData[$itemField][$type]) && !empty($sellers[$eventLogData[$itemField][$type]->seller_id]) ? $sellers[$eventLogData[$itemField][$type]->seller_id][0] : null;
                            $item['store'] = isset($eventLogData[$itemField][$type]) && !empty($stores[$eventLogData[$itemField][$type]->store_id]) ? $stores[$eventLogData[$itemField][$type]->store_id][0] : null;
                        } else if ($field === 'country' && empty($item['system_location'])) {
                            $item['system_location'] = isset($eventLogData[$itemField][$type]) && !empty($countries[$eventLogData[$itemField][$type]->country]) ? $countries[$eventLogData[$itemField][$type]->country][0] : null;
                        }
                    }
                    $item[$field] = $itemField;
                    if ($field === 'store_id' && (empty($item['seller']) || empty($item['store']))) {
                        continue;
                    }


                    if (Str::contains($field, 'ad_') && empty($item['name'])) {
                        $item['name'] = $item[$field];
                        unset($item[$field]);
                    }

                    if (empty($item['conversion_rate'])) {
                        $item['conversion_rate'] = (!empty($item['orders']) && !empty($item['visit'])) ? $item['orders'] / $item['visit'] : 0;
                    }

                    if (!empty($item['system_location'])) {
                        $item['name'] = $item['system_location']['name'];
                        unset($item['system_location']);
                    }
                    $data[] = $item;
                }
            }

        }
        return collect($data)->take($limit)->toArray();
    }

    /**
     * @param Request $request
     * @param $setDateRangeByRequest
     * @return Request
     */
    public function setCommonFilter(Request $request, $setDateRangeByRequest = true)
    {
        $user = currentUser();
        if ($user->isSeller()) {
            $userId = $user->getUserId();
        } elseif ($request->has('seller_id')) {
            $userId = $request->get('seller_id');
        } elseif ($request->has('seller_ids')) {
            $userIds = $request->get('seller_ids');
        }

        if (!empty($userId)) {
            $this->arrFilter['seller_id'] = $userId;
            $this->setExcludeCountries($userId);
        }

        if (!empty($userIds)) {
            $this->arrFilter['seller_id'] = $userIds;
        }

        if ($request->filled('store_id') && $request->get('store_id') !== 'All') {
            $this->arrFilter['store_id'] = $request->store_id;
        }

        if (empty($this->arrFilter['store_id']) && $user->isSeller()) {
            $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
            if (!empty($store_ids)) {
                $this->arrFilter['store_id'] = $store_ids[0];
            }
        }

        if ($request->filled('campaign_id')) {
            $this->arrFilter['campaign_id'] = $request->campaign_id;
        }

        if ($setDateRangeByRequest) {
            $request = $this->setDateRangeByRequest($request);
        }
        return $request;
    }

    /**
     * @param $userId
     * @return void
     */
    protected function setExcludeCountries($userId)
    {
        $excludeCountries = UserInfo::query()
            ->where('user_id', $userId)
            ->where('key', UserInfoKeyEnum::EXCLUDE_COUNTRIES)
            ->pluck('value')
            ->toArray();

        if (!empty($excludeCountries[0])) {
            $this->arrExclude[UserInfoKeyEnum::EXCLUDE_COUNTRIES] = explode(',', $excludeCountries[0]);
        }
    }

    /**
     * @param $request
     * @return Request
     */
    protected function setDateRangeByRequest($request)
    {
        $this->dateRanges['type'] = $request->date_type ?? $request->date_range ?? DateRangeEnum::TODAY;
        $this->dateRanges['is_timezone'] = $request->boolean('is_timezone', false);
        if ($request->date_type === 'custom') {
            $this->dateRanges['range'] = [
                $request->start_date,
                $request->end_date,
            ];
        }
        return $request;
    }

    /**
     * @param CampaignRequest $request
     * @param $campaignId
     * @return JsonResponse
     * @throws \Throwable
     */
    public function campaign(CampaignRequest $request, $campaignId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $campaignName = Campaign::query()
            ->where([
                'id' => $campaignId,
            ])
            ->onSellerConnection($seller)
            ->value('name');

        if (!$campaignName) {
            return $this->errorResponse('Campaign not found');
        }

        $this->type = TypeEnum::CAMPAIGN;
        $this->id = $campaignId;
        $request['campaign_id'] = $campaignId;
        $request = new Request($request->toArray());
        $this->setCommonFilter($request);
        $overview = $this->getOverview('campaign');
        $countries = $this->getOverViewAndViews('country');
        $this->dataType = DataTypeEnum::PRODUCT;
        $this->setCommonFilter($request);
        $query = $this->getAnalyticOrderQuery();
        $query->join('order_product', 'order.id', '=', 'order_product.order_id');
        $query->selectRaw('order_product.template_id');
        $query->selectRaw('order_product.product_name');
        $query->selectRaw('COUNT(DISTINCT order_product.order_id ) AS orders');
        $query->selectRaw('SUM(quantity) AS items');
        $query->selectRaw('SUM(order_product.total_amount) AS sales');
        $query->selectRaw('SUM(order_product.seller_profit) AS profits');
        $query->where('order_product.campaign_id', $campaignId);
        $query->whereNull('order_product.deleted_at');
        $products = $query->groupBy('order_product.product_id')->get();
        if ($products->isNotEmpty()) {
            $product_ids = $products->pluck('product_id')->toArray();
            $products_order = Product::query()->onSellerConnection($seller)
                ->whereIn('id', $product_ids)
                ->select(['id', 'name'])->get();
            $products->map(function ($item) use ($products_order) {
                foreach ($products_order as $product) {
                    if ((int)$product->id === (int)$item->product_id) {
                        $item->product = $product;
                        $item->product_id = $product->id;
                    }
                }
                return $item;
            });
            $products = $products->toArray();
        }
        return $this->successResponse([
            'campaign_name' => $campaignName,
            'analytic' => [
                'overview' => $overview,
                'products' => $products,
                'countries' => $countries,
            ]
        ]);
    }

    /**
     * @param DashboardRequest $request
     * @return JsonResponse
     */
    public function getCountries(DashboardRequest $request): JsonResponse
    {
        $this->setCommonFilter($request);
        $data = $this->getOverViewAndViews('country');
        return $this->successResponse($data);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getStores(Request $request): JsonResponse
    {
        $this->type = TypeEnum::STORE;
        $this->setCommonFilter($request);
        $data = $this->getOverViewAndViews('store_id');
        return $this->successResponse($data);
    }

    /**
     * @param GetAdsRequest $request
     * @return JsonResponse
     */
    public function getAds(GetAdsRequest $request): JsonResponse
    {
        $this->setCommonFilter($request);
        $data = $this->getOverViewAndViews($request->get('ad_option'));
        return $this->successResponse($data);
    }

    /**
     * @param DashboardRequest $request
     * @return JsonResponse
     */
    public function getDevices(DashboardRequest $request): JsonResponse
    {
        $this->setCommonFilter($request);
        $data = $this->getOverViewAndViews('device_detail');
        return $this->successResponse($data);
    }

    /**
     * @param DashboardRequest $request
     * @return JsonResponse
     */
    public function getTemplateProducts(DashboardRequest $request): JsonResponse
    {
        $this->setCommonFilter($request);
        $data = $this->getOverViewAndViews('template_id');

        return $this->successResponse($data);
    }

    /**
     * @param StoreRequest $request
     * @return array
     * @throws \Exception
     */
    public function getChart(StoreRequest $request): array
    {
        $dateType = 'day';
        $user = currentUser();
        $isAdmin = $user->isAdmin();
        $sellerId = $isAdmin ? 0 : currentUser()->getUserId();
        $timeout = $isAdmin ? CacheKeys::CACHE_1H : CacheKeys::CACHE_24H;
        $storeId = $request->get('store_id');

        $tags = [
            'seller_id_' . $sellerId,
        ];

        if ($storeId) {
            $tags[] = CacheKeys::getStoreId($storeId);
        }

        return cache()->tags($tags)->remember(CacheKeys::getStats('chart_v3.1', $dateType, DateRangeEnum::LAST_30_DAYS, $storeId), $timeout, function () use ($request, $isAdmin, $sellerId) {
            $timezoneOffset = $isAdmin ? 7 : get_utc_offset_by_user_or_store($sellerId);
            $days = 15;
            $request->merge([
                'date_type' => DateRangeEnum::CUSTOM,
                'start_date' => now()->subDays($days),
                'end_date' => now()->subDays(),
                'datetime' => true,
            ]);

            $this->setCommonFilter($request);
            $now = getTodayTimeZone(null, false)->subDays($days);
            $columnByTime = "DATE_FORMAT(DATE_ADD(paid_at, INTERVAL $timezoneOffset HOUR), '%Y-%m-%d')";
            $eventColumnByTime = $isAdmin ? "DATE_FORMAT(DATE_ADD(timestamp, INTERVAL $timezoneOffset HOUR), '%Y-%m-%d')" : "datestamp";

            $dates = [];
            for ($i = 1; $i < $days; $i++) {
                $dates[] = $now->copy()->addDays($i)->format('Y-m-d');
            }

            $dataEvents = IndexEventLogs::query()
                ->selectRaw("$eventColumnByTime as date")
                ->addSelect('type')
                ->calculateCount(dateRanges: $this->dateRanges)
                ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                ->addExcludeAnalytic($this->arrExclude)
                ->groupByRaw("date, type")
                ->get();
            $dataOrders = $this->getAnalyticOrderQuery()
                ->getChart()
                ->selectRaw("$columnByTime as date")
                ->whereIn('type', [
                    OrderTypeEnum::CUSTOM,
                    OrderTypeEnum::REGULAR,
                ])
                ->groupByRaw('date')
                ->get();

            $arr = [];
            $events = EventLogsTypeEnum::getValues();
            foreach ($dates as $index => $date) {
                $orders = 0;
                $items = 0;
                $activeSellers = 0;
                $sameDate = '';
                foreach ($dataEvents as $each) {
                    $value = $each->count;
                    $eachDate = date('Y-m-d', strtotime($each->date));
                    if ($each->type === EventLogsTypeEnum::VISIT) {
                        $value = round($value / 10);
                    }
                    if (empty($sameDate)) {
                        if ($date === $eachDate) {
                            $sameDate = $date;
                            $arr[$each->type][$index] = $value;
                        }
                    } else if ($sameDate === $eachDate) {
                        $arr[$each->type][$index] = $value;
                    }
                }
                foreach ($events as $event) {
                    if (!isset($arr[$event][$index])) {
                        $arr[$event][$index] = 0;
                    }
                }
                unset($each);
                foreach ($dataOrders as $each) {
                    $eachDate = $each->date;
                    if ($eachDate === $date) {
                        $orders = (int)$each->orders;
                        $items = (int)$each->items;
                        $activeSellers = $each->active_sellers ? (int)$each->active_sellers : 0;
                        break;
                    }
                }
                unset($each);
                $arr['orders'][$index] = $orders;
                $arr['items'][$index] = $items;
                if ($isAdmin) {
                    $arr['active_sellers'][$index] = $activeSellers;
                }
                $arr['dates'][$index] = Str::after($date, '-');
            }
            return $arr;
        });
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function getFunnelChart(Request $request): JsonResponse
    {
        $dateRange = $request->get('date_range', DateRangeEnum::THIS_MONTH);
        $startDate = $request->get('start_date', '');
        $endDate = $request->get('end_date', '');
        $divideBy = $request->get('divide_by', 'devices');
        $timeout = $dateRange === DateRangeEnum::TODAY ? CacheKeys::CACHE_1H : CacheKeys::CACHE_24H;
        if (!in_array($dateRange, [
            DateRangeEnum::TODAY,
            DateRangeEnum::YESTERDAY,
            DateRangeEnum::LAST_WEEK,
            DateRangeEnum::LAST_7_DAYS,
            DateRangeEnum::LAST_MONTH,
            DateRangeEnum::THIS_WEEK,
            DateRangeEnum::THIS_MONTH,
            DateRangeEnum::LAST_30_DAYS,
            DateRangeEnum::CUSTOM,
        ], true)) {
            return $this->errorResponse('Date range is invalid');
        }
        if ($dateRange === DateRangeEnum::CUSTOM && !empty($startDate) && !empty($endDate)) {
            $differenceInDays = Carbon::parse(trim($startDate, "'"))->diffInDays(Carbon::parse(trim($endDate, "'")), false);
            if ($differenceInDays > 90 || $differenceInDays < 0) {
                return $this->errorResponse('Date range must be less than 90 days');
            }
            $cacheKey = 'funnel_chart_' . $dateRange . '_' . $startDate . '_' . $endDate . '_' . $divideBy;
        } else {
            $cacheKey = 'funnel_chart_' . $dateRange . '_' . $divideBy;
        }
        $data = cache()->remember($cacheKey, $timeout, function () use ($startDate, $endDate, $dateRange, $divideBy) {
            switch ($dateRange) {
                case DateRangeEnum::TODAY:
                    $twinRange = DateRangeEnum::YESTERDAY;
                    break;
                case DateRangeEnum::THIS_MONTH:
                    $twinRange = DateRangeEnum::LAST_MONTH;
                    break;
                case DateRangeEnum::THIS_WEEK:
                    $twinRange = DateRangeEnum::LAST_WEEK;
                    break;
            }
            $current = $this->getFunnelChartData($startDate, $endDate, $dateRange, $divideBy);
            if (isset($twinRange)) {
                $twin = $this->getFunnelChartData($startDate, $endDate, $twinRange, $divideBy);
            }
            return [
                'current' => $current,
                'twin' => $twin ?? null,
            ];
        });

        if (empty($data)) {
            return $this->errorResponse('Date range is invalid');
        }

        return $this->successResponse([
            'data' => $data['current'],
            'query' => [
                'date_range' => $dateRange,
                'divide_by' => $divideBy,
            ],
            'twin' => $data['twin'],
        ]);
    }

    private function getFunnelChartData($startDate, $endDate, $dateRange, $divideBy)
    {
        $eventLogQuery = IndexEventLogs::query()
            ->addExcludeAnalytic($this->arrExclude)
            ->filterDateRange($dateRange, $startDate, $endDate, 'timestamp')
            ->where(function ($q) {
                $q->where('type', '!=', EventLogsTypeEnum::VISIT)
                    ->orWhere(function ($q) {
                        $q->where('type', EventLogsTypeEnum::VISIT)
                            ->where('campaign_id', '>', 0);
                    });
            });

        $interactTypes = $this->getInteractTypes();
        $interactWithOrderQuery = IndexEventLogs::query()
            ->addExcludeAnalytic($this->arrExclude)
            ->filterDateRange($dateRange, $startDate, $endDate, 'timestamp')
            ->whereIn('type', $interactTypes)
            ->whereIn('session_id', function ($q) {
                $q->select('session_id')
                    ->from('event_logs')
                    ->where('type', EventLogsTypeEnum::ADD_TO_CART);
            });
        $orderQuery = IndexOrder::query()
            ->filterDateRange($dateRange, $startDate, $endDate, 'paid_at')
            ->addExcludeAnalytic($this->arrExclude)
            ->whereNull('order.deleted_at')
            ->whereIn('type', [
                OrderTypeEnum::CUSTOM,
                OrderTypeEnum::REGULAR,
            ]);
        switch ($divideBy) {
            case 'ads':
                $eventLogQuery->select(['ad_campaign', 'type'])
                    ->selectRaw("CASE
                                WHEN ad_campaign = 'facebook_ad' THEN 'facebook'
                                WHEN ad_campaign = 'google_ad' THEN 'google'
                                WHEN ad_campaign = 'N/A' THEN 'no_ads'
                                ELSE 'other'
                             END as group_category");
                $interactWithOrderQuery->select(['ad_campaign'])
                    ->selectRaw("CASE
                                WHEN ad_campaign = 'facebook_ad' THEN 'facebook'
                                WHEN ad_campaign = 'google_ad' THEN 'google'
                                WHEN ad_campaign = 'N/A' THEN 'no_ads'
                                ELSE 'other'
                             END as group_category");
                $orderQuery->select(['ad_campaign'])
                    ->selectRaw("CASE
                                WHEN ad_campaign = 'facebook_ad' THEN 'facebook'
                                WHEN ad_campaign = 'google_ad' THEN 'google'
                                WHEN ad_campaign = 'N/A' THEN 'no_ads'
                                ELSE 'other'
                             END as group_category");
                break;
            case 'devices':
            default:
                $eventLogQuery->select(['device', 'type'])
                    ->selectRaw("CASE
                                WHEN device = 'desktop' THEN 'desktop'
                                WHEN device = 'mobile' THEN 'mobile'
                                ELSE 'other'
                             END as group_category");
                $interactWithOrderQuery->select(['device'])
                    ->selectRaw("CASE
                                WHEN device = 'desktop' THEN 'desktop'
                                WHEN device = 'mobile' THEN 'mobile'
                                ELSE 'other'
                             END as group_category");
                $orderQuery->select(['device'])
                    ->selectRaw("CASE
                                WHEN device = 'desktop' THEN 'desktop'
                                WHEN device = 'mobile' THEN 'mobile'
                                ELSE 'other'
                             END as group_category");
                break;
        }
        $eventLogs = $eventLogQuery
            ->calculateCount(dateRanges: ['type' => $dateRange, 'range' => [$startDate, $endDate]])
            ->selectRaw(
                "CASE
                    WHEN type LIKE 'interact_%' THEN 'interact'
                    ELSE type
                END as group_type"
            )
            ->groupBy(['group_category', 'group_type'])
            ->get();

        $interactWithOrder = $interactWithOrderQuery
            ->calculateCount(dateRanges: ['type' => $dateRange, 'range' => [$startDate, $endDate]])
            ->whereIn('type', $interactTypes)
            ->groupBy('group_category')
            ->get();
        $orderLogs = $orderQuery->calculateOrders()->groupBy('group_category')->get();
        $output = [];
        foreach ($eventLogs as $eventLog) {
            if ($eventLog->group_type === 'interact') {
                $output[$eventLog->group_category]['interact_without_atc'] = $eventLog->count + $eventLog->total_sum - $interactWithOrder->where('group_category', $eventLog->group_category)->sum('count') - $interactWithOrder->where('group_category', $eventLog->group_category)->sum('total_sum');
            } else {
                $output[$eventLog->group_category][$eventLog->group_type] = $eventLog->count + $eventLog->total_sum;
            }
        }
        foreach ($orderLogs as $order) {
            $output[$order->group_category]['orders'] = $order->orders;
        }
        return $output;
    }

    private function getInteractTypes(): array
    {
        return cache()->remember(CacheKeys::INTERACT_TYPES, CacheKeys::CACHE_24H, function () {
            return IndexEventLogs::query()
                ->select('type')
                ->where('type', 'like', 'interact_%')
                ->groupBy('type')
                ->pluck('type')
                ->toArray();
        });
    }

    /**
     * @param $dateRange
     * @return void
     */
    private function getComparesDateRange(&$dateRange): void
    {
        $hourOffset = getHourOffsetBySeller();
        $today = now()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset);
        switch ($dateRange['type']) {
            case DateRangeEnum::LIFE_TIME:
                return;
            case DateRangeEnum::CUSTOM:
                if ($dateRange['range'] === [null, null]) {
                    return;
                }
                $t1 = Carbon::parse($dateRange['range'][0]);
                $t2 = Carbon::parse($dateRange['range'][1]);

                $dateDiff = $t2->diffInDays($t1);
                $t0 = $t1->copy()->subDays($dateDiff)->startOfDay();
                $t1 = $t1->copy()->endOfDay();
                break;
            default:
            case DateRangeEnum::TODAY:
                $dateRange['type'] = DateRangeEnum::YESTERDAY;
                return;
            case DateRangeEnum::YESTERDAY:
                $isTimezone = true;
                $t1 = $today->subDay();
                $t0 = $t1->copy()->subDay();
                break;
            case DateRangeEnum::LAST_3_DAYS:
                $isTimezone = true;
                $t1 = now()->subDays(3);
                $t0 = $t1->copy()->subDays(3);
                break;
            case DateRangeEnum::LAST_7_DAYS:
                $isTimezone = true;
                $t1 = now()->subDays(7);
                $t0 = $t1->copy()->subDays(7);
                break;
            case DateRangeEnum::LAST_30_DAYS:
                $isTimezone = true;
                $t1 = now()->subDays(30);
                $t0 = $t1->copy()->subDays(30);
                break;
            case DateRangeEnum::THIS_WEEK:
                $t1 = $today->startOfWeek();
                $t0 = $t1->copy()->subDays(7);
                break;
            case DateRangeEnum::LAST_WEEK:
                $t1 = $today->subWeeks()->startOfWeek();
                $t0 = $t1->copy()->subDays(7);
                break;
            case DateRangeEnum::THIS_MONTH:
                $t1 = now()->subMonthsWithNoOverflow()->endOfMonth();
                $t0 = $t1->copy()->startOfMonth();
                break;
            case DateRangeEnum::LAST_MONTH:
                $t1 = $today->subMonthsWithNoOverflow()->startOfMonth();
                $t0 = $t1->copy()->subMonthsWithNoOverflow(2)->endOfMonth();
                break;
            case DateRangeEnum::THIS_YEAR:
                $t1 = $today->subYearsWithNoOverflow()->startOfYear();
                $t0 = $t1->copy()->subYearsWithNoOverflow()->endOfYear();
                break;
            case DateRangeEnum::LAST_YEAR:
                $t1 = $today->subYearsWithNoOverflow(2)->startOfYear();
                $t0 = $t1->copy()->subYearsWithNoOverflow(2)->endOfYear();
                break;
        }
        $dateRange['range'][0] = $t0;
        $dateRange['range'][1] = $t1;
        $dateRange['type'] = DateRangeEnum::CUSTOM;
        if (!empty($isTimezone)) {
            $dateRange['is_timezone'] = $isTimezone;
        }
    }

    /**
     * @param DashboardRequest $request
     * @return JsonResponse
     * @throws \Exception|\Throwable
     */
    public function getCompares(DashboardRequest $request): JsonResponse
    {
        $dateType = $request->get('date_type');
        $dateRanges['type'] = $dateType;
        if ($dateType === DateRangeEnum::CUSTOM) {
            $dateRanges['range'] = [
                $request->get('start_date'),
                $request->get('end_date'),
            ];
        }
        $this->getComparesDateRange($dateRanges);
        $this->dateRanges = $dateRanges;
        $this->setCommonFilter($request, false);
        $data = $this->getOverview('dashboard', false);
        return $this->successResponse($data);
    }

    /**
     * @param StoreRequest $request
     * @return JsonResponse
     */
    public function compareToPlatform(StoreRequest $request): JsonResponse
    {
        try {
            $data = cache()->remember(
                CacheKeys::ANALYTIC_PERCENT_PLATFORM . '_v3',
                CacheKeys::CACHE_1H,
                function () use ($request) {
                    $this->dateRanges['type'] = DateRangeEnum::LAST_7_DAYS;
                    $this->setCommonFilter($request, false);
                    // get all by platform
                    unset($this->arrFilter['seller_id']);
                    $order = $this->getAnalyticOrderQuery()->calculateOrders()->first();
                    $events = IndexEventLogs::query()
                        ->select('type')
                        ->calculateCount(dateRanges: $this->dateRanges)
                        ->groupBy('type')
                        ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                        ->addExcludeAnalytic($this->arrExclude)
                        ->get();
                    $data = [];
                    $data['orders'] = optional($order)->orders ?? 0;
                    $data['visit'] = 0;
                    $data['add_to_cart'] = 0;
                    $data['init_checkout'] = 0;
                    $data['cr'] = 0;

                    foreach ($events as $event) {
                        $data[$event->type] = $event->count + $event->total_sum;
                    }

                    $motivateRate = 1.05;

                    if ($data['visit']) {
                        $data['cr'] = round($data['orders'] * $motivateRate / $data['visit'] * 100, 2);
                    }
                    if ($data['init_checkout']) {
                        $data['orders'] = round($data['orders'] * $motivateRate / $data['init_checkout'] * 100, 2);
                    }
                    if ($data['add_to_cart']) {
                        $data['init_checkout'] = round(
                            $data['init_checkout'] * $motivateRate / $data['add_to_cart'] * 100,
                            2
                        );
                    }
                    if ($data['visit']) {
                        $data['add_to_cart'] = round($data['add_to_cart'] * $motivateRate / $data['visit'] * 100, 2);
                    }

                    unset($data['visit']);

                    return $data;
                }
            );
            return $this->successResponse($data);
        } catch (\Throwable $e) {
            logToDiscord(
                __FUNCTION__
                . PHP_EOL
                . $e->getMessage(),
                'analytic'
            );
            return $this->errorResponse();
        }
    }

    public function getInactiveUsers(Request $request): JsonResponse
    {
        $dateType = $request->get('date_type', DateRangeEnum::LAST_3_DAYS);
        $startTime = Carbon::now()->subDays(3);
        $endTime = Carbon::now()->subDays(7);

        switch ($dateType) {
            case DateRangeEnum::LAST_3_DAYS:
                $startTime = Carbon::now()->subDays(3);
                $endTime = Carbon::now()->subDays(7);
                break;
            case DateRangeEnum::LAST_7_DAYS:
                $startTime = Carbon::now()->subWeek();
                $endTime = Carbon::now()->subMonth();
                break;
            case DateRangeEnum::LAST_30_DAYS:
                $startTime = Carbon::now()->subMonth();
                $endTime = Carbon::now()->subDays(60);
                break;
        }

        try {
            $orders = Order::query()
                ->GetLastPaidOrder($startTime, $endTime)
                ->limit(1000);

            $data = User::query()
                ->select('user.id', 'user.name', 'user.email', 'order.paid_at')
                ->role(UserRoleEnum::SELLER)
                ->joinSub($orders, 'order', function ($join) {
                    $join->on('user.id', '=', 'order.seller_id');
                })
                ->orderBy('order.paid_at', 'desc')
                ->get();

            return $this->successResponse($data);
        } catch (\Throwable $e) {
            return $this->errorResponse();
        }
    }
}
