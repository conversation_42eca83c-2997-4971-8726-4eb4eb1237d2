<?php

namespace App\Rules\Store;

use App\Models\Store;
use Illuminate\Contracts\Validation\Rule;

class UniqueDomainRule implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        return !Store::query()
            ->where('domain', $value)
            ->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'The domain has already been taken.';
    }
}
