<?php

namespace App\Jobs;

use App\Enums\DiscordChannel;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\QueueName;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RefundShipExtraFeeToSellerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?Order $order;
    /**
     * Create a new job instance.
     */
    public function __construct($orderId)
    {
        $this->order = Order::query()
            ->with(['products', 'seller'])
            ->whereHas('order_history', function ($q) {
                $q->where('action', OrderHistoryActionEnum::ASSIGNED_SUPPLIER)->whereNotNull('updated_by')->where('updated_by', 'like', '%senprints.com');
            })
            ->where('type', OrderTypeEnum::CUSTOM)
            ->where('sen_fulfill_status', OrderSenFulfillStatus::YES)
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->whereHas('products', fn($q) => $q->where('supplier_id', '>', 0))
            ->has('products', '>', 1)
            ->whereKey($orderId)
            ->first();
        $this->onQueue(QueueName::ORDER_EVENTS);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $order = $this->order;
        if (!$order || empty($order->seller) || $order->isCustomServiceOrder()) {
            return;
        }
        try {
            $seller = $order->seller;
            $oldShippingAmount = (float)$order->total_shipping_amount;
            $newShippingAmount = (float)$order->calculateShippingCost();
            $differentShippingAmount = $oldShippingAmount - $newShippingAmount;
            if ($differentShippingAmount > 0.01) {
                $refundReason = 'Refund ship extra fee #' . $order->order_number;
                $hasRefunded = $seller->isExistsActionUpdateBalance(array(
                    'seller_id' => $seller->id,
                    'order_id' => $order->id,
                    'type' => SellerBillingType::REFUND,
                    'status' => SellerBillingStatus::COMPLETED,
                    'detail' => $refundReason,
                ));
                if (!$hasRefunded) {
                    $refunded = $seller->updateBalance($differentShippingAmount, SellerBillingType::REFUND, $refundReason, $order->id);
                    if ($refunded) {
                        $differentShippingAmount = round($differentShippingAmount, 2);
                        $embedDesc = [
                            [
                                'description' => "Đơn này đã refund lại cho seller $$differentShippingAmount tiền chênh của phí ship extra.\r\n- Seller ID: $seller->id\r\n- Seller Email: $seller->email\r\n- Shipping method: $order->shipping_method\r\n- Phí ship trên đơn: $$oldShippingAmount\r\n- Phí ship thực tế: $$newShippingAmount\r\n- Transaction ID: " . data_get($refunded, 'id'),
                                'color' => 15548997
                            ]
                        ];
                        logToDiscord("https://admin.senprints.com/order/detail/" . $order->id, DiscordChannel::ADMIN_WARNING, false, true, 7, $embedDesc);
                    }
                }
            }
        } catch (\Throwable $e) {
            logException($e, 'RefundShipExtraFeeToSellerJob::handle -> Order ID: ' . $order->id);
        }
    }
}
