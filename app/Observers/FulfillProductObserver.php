<?php

namespace App\Observers;

use App\Enums\ProductType;
use App\Jobs\SyncStockStatusJob;
use App\Models\FulfillProduct;
use Illuminate\Support\Facades\Auth;

class FulfillProductObserver
{
    /**
     * Handle the Product "created" event.
     */
    public function created(FulfillProduct $product): void
    {
        //
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(FulfillProduct $product): void
    {
        if ($product->isDirty('status') && $product->product_type === ProductType::PRODUCT_TEMPLATE) {
            SyncStockStatusJob::dispatch([$product->id]);
        }
    }

    /**
     * Handle the Product "deleting" event.
     */
    public function deleting(FulfillProduct $product): void
    {
    }

    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(FulfillProduct $product): void
    {
        if ($product->product_type === ProductType::FULFILL_PRODUCT || $product->product_type === ProductType::TEMPLATE) {
            logToDiscord('Deleting product of supplier: ' . $product->supplier_id . ' for product id : ' . $product->id, 'fulfill_product', true);
            graylogError('Deleting product of supplier', [
                'category' => 'product_observer_deleting',
                'product_id' => $product->id,
                'trace' => debug_backtrace(),
                'product' => $product->toArray(),
                'user' => Auth::user(),
            ]);
        }
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(FulfillProduct $product): void
    {
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(FulfillProduct $product): void
    {
        if ($product->product_type === ProductType::FULFILL_PRODUCT || $product->product_type === ProductType::TEMPLATE) {
            logToDiscord('Deleting product of supplier: ' . $product->supplier_id . ' for product id : ' . $product->id, 'fulfill_product', true);
            graylogError('Deleting product of supplier', [
                'category' => 'product_observer_deleting',
                'product_id' => $product->id,
                'trace' => debug_backtrace(),
                'product' => $product->toArray(),
                'user' => Auth::user(),
            ]);
        }
    }
}
