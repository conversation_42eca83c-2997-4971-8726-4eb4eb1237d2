<?php

namespace App\Observers;

use App\Enums\ProductType;
use App\Jobs\SyncStockStatusJob;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Modules\Campaign\Jobs\SyncSlugJob;

class ProductObserver
{
    /**
     * Handle the Product "created" event.
     */
    public function created(Product $product): void
    {
        //
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(Product $product): void
    {
        if ($product->isDirty('status') && $product->product_type === ProductType::PRODUCT_TEMPLATE) {
            SyncStockStatusJob::dispatch([$product->id]);
        }
    }

    /**
     * Handle the Product "deleting" event.
     */
    public function deleting(Product $product): void
    {
    }

    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(Product $product): void
    {
        if ($product->product_type === ProductType::FULFILL_PRODUCT || $product->product_type === ProductType::TEMPLATE) {
            logToDiscord('Deleting product of supplier: ' . $product->supplier_id . ' for product id : ' . $product->id, 'fulfill_product', true);
            graylogError('Deleting product of supplier', [
                'category' => 'product_observer_deleting',
                'product_id' => $product->id,
                'trace' => debug_backtrace(),
                'product' => $product->toArray(),
                'user' => Auth::user(),
            ]);
        }
        // Check if the product is a campaign or template and has a seller_id will be deleted the slug on the slugs table
        if ($product->seller_id && $product->product_type && in_array($product->product_type, [ProductType::CAMPAIGN, ProductType::CAMPAIGN_EXPRESS, ProductType::CAMPAIGN_TEMPLATE], true)) {
            $seller = User::query()->select(['id', 'email', 'sharding_status', 'db_connection'])->whereKey($product->seller_id)->first();
            SyncSlugJob::dispatch(ids: [$product->id], seller: $seller, isUpsert: false);
        }
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(Product $product): void
    {
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(Product $product): void
    {
        if ($product->product_type === ProductType::FULFILL_PRODUCT || $product->product_type === ProductType::TEMPLATE) {
            logToDiscord('Deleting product of supplier: ' . $product->supplier_id . ' for product id : ' . $product->id, 'fulfill_product', true);
            graylogError('Deleting product of supplier', [
                'category' => 'product_observer_deleting',
                'product_id' => $product->id,
                'trace' => debug_backtrace(),
                'product' => $product->toArray(),
                'user' => Auth::user(),
            ]);
        }
        // Check if the product is a campaign or template and has a seller_id will be deleted the slug on the slugs table
        if ($product->seller_id && $product->product_type && in_array($product->product_type, [ProductType::CAMPAIGN, ProductType::CAMPAIGN_EXPRESS, ProductType::CAMPAIGN_TEMPLATE], true)) {
            $seller = User::query()->select(['id', 'email', 'sharding_status', 'db_connection'])->whereKey($product->seller_id)->first();
            SyncSlugJob::dispatch(ids: [$product->id], seller: $seller, isUpsert: false);
        }
    }
}
