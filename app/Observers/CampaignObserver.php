<?php

namespace App\Observers;

use App\Enums\ProductType;
use App\Jobs\SyncStockStatusJob;
use App\Models\Campaign;
use App\Models\User;
use Modules\Campaign\Jobs\SyncSlugJob;

class CampaignObserver
{
    /**
     * Handle the Product "created" event.
     */
    public function created(Campaign $product): void
    {
        //
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(Campaign $product): void
    {
        if ($product->isDirty('status') && $product->product_type === ProductType::PRODUCT_TEMPLATE) {
            SyncStockStatusJob::dispatch([$product->id]);
        }
    }

    /**
     * Handle the Product "deleting" event.
     */
    public function deleting(Campaign $product): void
    {
    }

    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(Campaign $product): void
    {
        // Check if the product is a campaign or template and has a seller_id will be deleted the slug on the slugs table
        if ($product->seller_id && $product->product_type && in_array($product->product_type, [ProductType::CAMPAIGN, ProductType::CAMPAIGN_EXPRESS, ProductType::CAMPAIGN_TEMPLATE], true)) {
            $seller = User::query()->select(['id', 'email', 'sharding_status', 'db_connection'])->whereKey($product->seller_id)->first();
            SyncSlugJob::dispatch(ids: [$product->id], seller: $seller, isUpsert: false);
        }
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(Campaign $product): void
    {
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(Campaign $product): void
    {
        // Check if the product is a campaign or template and has a seller_id will be deleted the slug on the slugs table
        if ($product->seller_id && $product->product_type && in_array($product->product_type, [ProductType::CAMPAIGN, ProductType::CAMPAIGN_EXPRESS, ProductType::CAMPAIGN_TEMPLATE], true)) {
            $seller = User::query()->select(['id', 'email', 'sharding_status', 'db_connection'])->whereKey($product->seller_id)->first();
            SyncSlugJob::dispatch(ids: [$product->id], seller: $seller, isUpsert: false);
        }
    }
}
